import { printLabel } from "../../../components/LBL/labelUtil";
import ArtVisitLbl from "./ArtVisitLbl.vue";
import PatientDemographicLbl from "./PatientDemographicLbl.vue";
import TransferoutLbl from "./TransferoutLbl.vue";
import { Service } from "../../../services/service";
import DrugLbl from "./DrugLbl.vue";

export function printArtDrug(drugID: number, quantity: number) {
    printLabel(DrugLbl, { scaleHeight: 316, lblUrl: `drugs/${drugID}/barcode?quantity=${quantity}` });
}

export async function printArtVisitLbl() {
    await printLabel(ArtVisitLbl, {
        copies: 1,
    });
}

export function printArtPatientDemographicsLbl(patientID: number) {
    printLabel(PatientDemographicLbl, {
        lblUrl: `programs/${Service.getProgramID()}/patients/${patientID}/labels/patient_history`,
    });
}

export function printArtTransferoutLbl(patientID: number, date = Service.getSessionDate()) {
    printLabel(TransferoutLbl, { lblUrl: `programs/1/patients/${patientID}/labels/transfer_out?date=${date}` });
}
