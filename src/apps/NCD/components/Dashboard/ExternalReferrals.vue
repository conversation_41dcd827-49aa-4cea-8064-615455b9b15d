<template>
    <div class="fhir-viewer">
        <div class="container">
            <div class="header">
                <h3>Referred Patient</h3>
            </div>

            <div v-if="!fhirData.length" class="card">
                <ion-input v-model="identifierID" placeholder="Enter Patient Identifier" @ionInput="FindDrugName" fill="outline"></ion-input>
                <div class="no-data">
                    <br />
                    <button @click="loadSampleData" class="load-data-btn">Find Patient</button>
                </div>
            </div>

            <div v-for="bundle in fhirData" :key="bundle.id">
                <!-- Patient Information -->
                <div
                    v-if="bundle.resourceType === 'Bundle' && bundle.entry && bundle.entry[0] && bundle.entry[0].resource.resourceType === 'Patient'"
                    class="card"
                >
                    <h2 class="card-title">👤 Patient Information</h2>
                    <div class="patient-info">
                        <div class="info-item">
                            <div class="info-label">Full Name</div>
                            <div class="info-value">{{ getPatientName(bundle.entry[0].resource) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Gender</div>
                            <div class="info-value">{{ bundle.entry[0].resource.gender || "Not specified" }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Birth Date</div>
                            <div class="info-value">{{ formatDate(bundle.entry[0].resource.birthDate) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Age</div>
                            <div class="info-value age-display">{{ calculateAge(bundle.entry[0].resource.birthDate) }} years</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Patient ID</div>
                            <div class="info-value">{{ bundle.entry[0].resource.id }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Sync Status</div>
                            <div class="info-value">
                                <span class="status-badge status-pending">
                                    {{ getSyncStatus(bundle.entry[0].resource) }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="meta-info">
                        <h4>Bundle Metadata</h4>
                        <div class="meta-item">
                            <span>Bundle ID:</span>
                            <span>{{ bundle.id }}</span>
                        </div>
                        <div class="meta-item">
                            <span>Last Updated:</span>
                            <span>{{ formatDateTime(bundle.meta.lastUpdated) }}</span>
                        </div>
                        <div class="meta-item">
                            <span>Total Entries:</span>
                            <span>{{ bundle.total }}</span>
                        </div>
                    </div>
                </div>

                <!-- Observations -->
                <div v-if="bundle.resourceType === 'Bundle' && hasObservations(bundle)" class="card">
                    <h2 class="card-title">📊 Clinical Observations</h2>
                    <div class="observations-grid">
                        <div v-for="entry in getObservations(bundle)" :key="entry.resource.id" class="observation-card">
                            <div class="obs-title">{{ getObservationDisplay(entry.resource) }}</div>
                            <div class="obs-details">
                                <div class="obs-detail">
                                    <span class="obs-label">Status:</span>
                                    <span class="obs-value">
                                        <span :class="'status-badge status-' + entry.resource.status">
                                            {{ entry.resource.status }}
                                        </span>
                                    </span>
                                </div>
                                <div v-if="entry.resource.valueQuantity" class="obs-detail">
                                    <span class="obs-label">Value:</span>
                                    <span class="obs-value"> {{ entry.resource.valueQuantity.value }} {{ entry.resource.valueQuantity.unit }} </span>
                                </div>
                                <div class="obs-detail">
                                    <span class="obs-label">Category:</span>
                                    <span class="obs-value">{{ getObservationCategory(entry.resource) }}</span>
                                </div>
                                <div class="obs-detail">
                                    <span class="obs-label">Date:</span>
                                    <span class="obs-value">{{ formatDateTime(entry.resource.effectiveDateTime) }}</span>
                                </div>
                                <div class="obs-detail">
                                    <span class="obs-label">Code:</span>
                                    <span class="obs-value">{{ entry.resource.code.coding[0].code }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { FhirService } from "@/services/fhir_service";
import { IonInput } from "@ionic/vue";

// Props
const props = defineProps({
    initialData: {
        type: Array,
        default: () => [],
    },
    apiEndpoint: {
        type: String,
        default: "/api/fhir/patient-data",
    },
});

// Reactive data
const fhirData = ref([]);
const identifierID = ref("");

// Methods
const loadSampleData = async () => {
    fhirData.value = [await FhirService.getFhirPatient(identifierID.value)];
    console.log("🚀 ~ loadSampleData ~ fhirData:", fhirData.value);
};

const loadFHIRData = async () => {
    try {
        const response = await fetch(props.apiEndpoint);
        const data = await response.json();
        fhirData.value = Array.isArray(data) ? data : [data];
    } catch (error) {
        console.error("Error loading FHIR data:", error);
    }
};

const getPatientName = (patient) => {
    if (patient.name && patient.name[0]) {
        const name = patient.name[0];
        const given = name.given ? name.given.join(" ") : "";
        const family = name.family || "";
        return `${given} ${family}`.trim();
    }
    return "Unknown";
};

const formatDate = (dateString) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    });
};

const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return "Not specified";
    return new Date(dateTimeString).toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    });
};

const calculateAge = (birthDate) => {
    if (!birthDate) return "Unknown";
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }
    return age;
};

const getSyncStatus = (resource) => {
    if (resource.meta && resource.meta.tag) {
        const syncTag = resource.meta.tag.find((tag) => tag.system === "http://your-domain.org/fhir/StructureDefinition/sync-status");
        return syncTag ? syncTag.code : "Unknown";
    }
    return "Unknown";
};

const hasObservations = (bundle) => {
    return bundle.entry && bundle.entry.some((entry) => entry.resource && entry.resource.resourceType === "Observation");
};

const getObservations = (bundle) => {
    if (!bundle.entry) return [];
    return bundle.entry.filter((entry) => entry.resource && entry.resource.resourceType === "Observation");
};

const getObservationDisplay = (observation) => {
    if (observation.code && observation.code.coding && observation.code.coding[0]) {
        return observation.code.coding[0].display || observation.code.coding[0].code;
    }
    return "Unknown Observation";
};

const getObservationCategory = (observation) => {
    if (observation.category && observation.category[0] && observation.category[0].coding) {
        return observation.category[0].coding[0].display || observation.category[0].coding[0].code;
    }
    return "Unknown";
};

// Lifecycle hook
onMounted(() => {
    if (props.initialData.length > 0) {
        fhirData.value = props.initialData;
    }
});
</script>

<style scoped>
.fhir-viewer {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.header {
    text-align: center;
    color: #555;
    margin-bottom: 30px;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-title::before {
    content: "";
    width: 4px;
    height: 25px;
    background: linear-gradient(45deg, #006401, #008501);
    border-radius: 2px;
}

.patient-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    padding: 15px;
    background: rgba(0, 100, 1, 0.1);
    border-radius: 12px;
    border-left: 4px solid #006401;
}

.info-label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.info-value {
    font-size: 1.1rem;
    color: #333;
    font-weight: 500;
}

.observations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.observation-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(0, 100, 1, 0.2);
    transition: all 0.3s ease;
}

.observation-card:hover {
    transform: scale(1.02);
    border-color: #006401;
}

.obs-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.obs-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.obs-detail {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.obs-detail:last-child {
    border-bottom: none;
}

.obs-label {
    font-weight: 500;
    color: #666;
}

.obs-value {
    font-weight: 600;
    color: #333;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-final {
    background: #e8f5e8;
    color: #2d5a2d;
}

.status-pending {
    background: rgba(0, 100, 1, 0.1);
    color: #006401;
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
}

.load-data-btn {
    background: linear-gradient(45deg, #006401, #008501);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 100, 1, 0.3);
}

.load-data-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 100, 1, 0.4);
    background: linear-gradient(45deg, #005001, #007001);
}

.meta-info {
    background: rgba(0, 100, 1, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.age-display {
    font-size: 1.2rem;
    font-weight: 600;
    color: #006401;
}

/* Responsive Design */
@media (max-width: 768px) {
    .patient-info {
        grid-template-columns: 1fr;
    }

    .observations-grid {
        grid-template-columns: 1fr;
    }

    .header h1 {
        font-size: 2rem;
    }

    .card {
        padding: 20px;
    }
}
</style>
