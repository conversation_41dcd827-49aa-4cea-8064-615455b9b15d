<template>
    <ion-page>
        <Toolbar />
        <ion-content :fullscreen="true">
            <DemographicBar />
            <div style="width: 88vw; margin: 0 auto; margin-top: 10px">
                <Wizard
                    v-if="showWizard"
                    ref="wizard"
                    vertical-tabs
                    navigable-tabs
                    scrollable-tabs
                    :startIndex="0"
                    :doneButton="{
                        text: 'Finish',
                        icon: 'check',
                        hideText: false,
                        hideIcon: false,
                        disabled: false,
                    }"
                    :custom-tabs="tabs"
                    :beforeChange="onTabBeforeChange"
                    @change="onChangeCurrentTab"
                    @complete:wizard="saveData()"
                >
                    <div>
                        <div class="back_profile">
                            <DynamicButton
                                name="Back to profile"
                                iconSlot="start"
                                fill="clear"
                                :icon="chevronBackOutline"
                                :font-weight="'600'"
                                @click="openBackController()"
                            />
                        </div>
                    </div>

                    <!-- Only render the component for the active tab -->
                    <component :is="getActiveComponent()" />
                </Wizard>
            </div>
        </ion-content>
    </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
    IonContent,
    IonHeader,
    IonMenuButton,
    IonPage,
    IonTitle,
    IonToolbar,
    IonButton,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardSubtitle,
    IonCardTitle,
    IonAccordion,
    IonAccordionGroup,
    IonItem,
    IonLabel,
    IonModal,
} from "@ionic/vue";
import { chevronBackOutline, checkmark } from "ionicons/icons";
import { storeToRefs } from "pinia";

// Import components
import Toolbar from "@/components/Toolbar.vue";
import ToolbarSearch from "@/components/ToolbarSearch.vue";
import DemographicBar from "@/components/DemographicBar.vue";
import SaveProgressModal from "@/components/SaveProgressModal.vue";
import DynamicButton from "@/components/DynamicButton.vue";
import Stepper from "@/components/Stepper.vue";
import BasicFooter from "@/components/BasicFooter.vue";
import DiagnosisComponent from "@/apps/NCD/components/ConsultationPlan/Diagnosis.vue";
import ComplicationsScreening from "@/apps/NCD/components/ConsultationPlan/ComplicationsScreening.vue";
import Investigations from "@/apps/NCD/components/ConsultationPlan/Investigations.vue";
import TreatmentPlan from "@/apps/NCD/components/ConsultationPlan/TreatmentPlan.vue";
import RiskAssessment from "@/apps/NCD/components/ConsultationPlan/RiskAssessment.vue";
import NextAppointment from "@/apps/NCD/components/ConsultationPlan/NextAppointment.vue";
import VitalSigns from "@/apps/NCD/components/ConsultationPlan/VitalSigns.vue";
import Wizard from "form-wizard-vue3";

// Import stores
import { useVitalsStore } from "@/apps/NCD/stores/VitalsStore";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useInvestigationStore } from "@/stores/InvestigationStore";
import { useDiagnosisStore } from "@/stores/DiagnosisStore";
import { useTreatmentPlanStore } from "@/stores/TreatmentPlanStore";
import { useNCDMedicationsStore, MedicationSelectionHasValues } from "@/stores/NCDMedicationStore";
import { useGeneralStore } from "@/stores/GeneralStore";
import { useOutcomeStore } from "@/stores/OutcomeStore";
import { useEnrollementStore } from "@/stores/EnrollmentStore";
import { useComplicationsStore } from "@/stores/ComplicationsStore";
import { useAllegyStore } from "@/apps/OPD/stores/AllergyStore";
import { useNonPharmaTherapyStore, stageNotes } from "@/stores/nonPharmaTherapyStore";
import { useConfigStore } from "@/stores/ConfigStore";

// Import services and utilities
import { Service } from "@/services/service";
import { VitalsService } from "@/services/vitals_service";
import { createModal, toastWarning, toastSuccess } from "@/utils/Alerts";
import { Diagnosis } from "@/apps/NCD/services/diagnosis";
import { Treatment, stageAllergies } from "@/apps/NCD/services/treatment";
import { isEmpty } from "lodash";
import HisDate from "@/utils/Date";
import { resetNCDPatientData } from "@/apps/NCD/config/reset_ncd_data";
import { icons } from "@/utils/svg";
import {
    formatRadioButtonData,
    formatCheckBoxData,
    formatGroupRadioButtonData,
    formatInputFiledData,
    formatCheckboxInputData,
} from "@/services/formatServerData";
import {
    modifyRadioValue,
    getRadioSelectedValue,
    getCheckboxSelectedValue,
    modifyWizardData,
    modifyFieldValue,
    modifyCheckboxValue,
    modifyGroupedRadioValue,
    getFieldValue,
} from "@/services/data_helpers";
import { validateInputFiledData } from "@/services/group_validation";
import { saveEncounterData, EncounterTypeId } from "@/services/encounter_type";
import { ObservationService } from "@/services/observation_service";
import { OrderService } from "@/services/order_service";
import { ConceptService } from "@/services/concept_service";
import { createNCDDrugOrder } from "@/apps/NCD/services/medication_service";
import { useFormWizard } from "@/composables/useFormWizard";
import { useStatusStore } from "@/stores/StatusStore";
import { useUserStore } from "@/stores/userStore";
import ConfirmPrinting from "@/components/ConfirmPrinting.vue";

//composable
const { onTabBeforeChange, onChangeCurrentTab, currentTabIndex } = useFormWizard("Consultation Plan");
const { printVisitSummary } = usePatientProfile();
// Router
const router = useRouter();
const route = useRoute();

// State
const wizardData = ref<any[]>([]);
const StepperData = ref<any[]>([]);
const isOpen = ref(false);
const showWizard = ref(true);

// Store references
const vitalsStore = useVitalsStore();
const demographicsStore = useDemographicsStore();
const investigationStore = useInvestigationStore();
const diagnosisStore = useDiagnosisStore();
const treatmentPlanStore = useTreatmentPlanStore();
const ncdMedicationsStore = useNCDMedicationsStore();
const generalStore = useGeneralStore();
const outcomeStore = useOutcomeStore();
const enrollmentStore = useEnrollementStore();
const complicationsStore = useComplicationsStore();
const configStore = useConfigStore();

// Destructure store refs
const { patient } = storeToRefs(demographicsStore) as any;
const { vitals } = storeToRefs(vitalsStore);
const { investigations } = storeToRefs(investigationStore);
const { diagnosis } = storeToRefs(diagnosisStore);
const { substance } = storeToRefs(enrollmentStore);
const { selectedNCDMedicationList } = storeToRefs(ncdMedicationsStore);
const { FootScreening, visualScreening, cvScreening } = storeToRefs(complicationsStore);
const { sessionDate } = storeToRefs(configStore);
const { apiStatus } = storeToRefs(useStatusStore());

//services
import { getOfflineFirstObsValue, getOfflineSavedUnsavedData, saveOfflinePatientData } from "@/services/offline_service";
import { PrintoutService } from "@/services/printout_service";
import { usePatientProfile } from "@/composables/usePatientProfile";

// Methods
const openBackController = () => {
    router.push("patientProfile");
};

// Get only the active tabs directly from NCDActivities
const getActiveTabs = () => {
    return generalStore.NCDActivities.map((item: any) => {
        return { title: item, icon: "" };
    });
};

// This will only contain active tabs
const tabs = ref(getActiveTabs());

// Initialize currentTabIndex properly - added to fix first tab being blank
// This ensures there's always a valid initial value
const initialTabIndex = computed(() => {
    return currentTabIndex.value !== undefined ? currentTabIndex.value : 0;
});

// Check if a tab title exists in the active tabs
const titleToCheck = (titleToCheck: string) => {
    return generalStore.NCDActivities.includes(titleToCheck);
};

// New function to get the active component based on currentTabIndex
const getActiveComponent = () => {
    // Make sure tabs have been initialized
    if (!tabs.value || tabs.value.length === 0) {
        console.log("Tabs not yet initialized");
        return null;
    }

    // Default to the first tab if currentTabIndex is undefined or out of bounds
    const index = currentTabIndex.value >= 0 && currentTabIndex.value < tabs.value.length ? currentTabIndex.value : 0;

    const currentTab = tabs.value[index]?.title;
    console.log("Current tab:", currentTab, "Index:", index);

    switch (currentTab) {
        case "Vital Signs":
            return VitalSigns;
        case "Risk Assessment":
            return RiskAssessment;
        case "Investigations":
            return Investigations;
        case "Diagnosis":
            return DiagnosisComponent;
        case "Complications Screening":
            return ComplicationsScreening;
        case "Treatment Plan":
            return TreatmentPlan;
        case "Next Appointment":
            return NextAppointment;
        default:
            // For debugging - should show what's going wrong if we hit this case
            console.log("No matching component found for tab:", currentTab);

            // Fallback to first component if we have activities
            if (generalStore.NCDActivities.length > 0) {
                const firstActivity = generalStore.NCDActivities[0];
                console.log("Falling back to first activity:", firstActivity);

                switch (firstActivity) {
                    case "Vital Signs":
                        return VitalSigns;
                    case "Risk Assessment":
                        return RiskAssessment;
                    case "Investigations":
                        return Investigations;
                    case "Diagnosis":
                        return DiagnosisComponent;
                    case "Complications Screening":
                        return ComplicationsScreening;
                    case "Treatment Plan":
                        return TreatmentPlan;
                    case "Next Appointment":
                        return NextAppointment;
                }
            }

            return null;
    }
};

const refreshWizard = () => {
    showWizard.value = false;
    setTimeout(() => {
        currentTabIndex.value = 0;
        showWizard.value = true;
    }, 0);
};

const cleanVitalForm = () => {
    const vitals = useVitalsStore();
    vitals.setVitals(vitals.getInitialVitals(patient.value.ID));
};

const markWizard = async () => {
    const sessionD = getFieldValue(sessionDate.value, "sessionDate", "value") || HisDate.sessionDate();
    const vitalsData = getOfflineSavedUnsavedData("vitals");

    // Process only active tabs
    for (let i = 0; i < tabs.value.length; i++) {
        const tab = tabs.value[i];

        if (tab.title === "Vital Signs") {
            tabs.value[i].icon = isDateInArray(sessionD, vitalsData) ? "check" : "";
        } else if (tab.title === "Risk Assessment") {
            const substanceAbuseData = getOfflineSavedUnsavedData("substanceAbuse");
            tabs.value[i].icon = isDateInArray(sessionD, substanceAbuseData) ? "check" : "";
        } else if (tab.title === "Investigations") {
            const labOrders = patient?.value?.labOrders?.saved;
            const filteredArray = labOrders?.filter((obj: any) => {
                return HisDate.toStandardHisFormat(sessionD) === HisDate.toStandardHisFormat(obj.order_date);
            });
            tabs.value[i].icon = filteredArray?.length > 0 ? "check" : "";
        } else if (tab.title === "Diagnosis") {
            const diagnosisData = getOfflineSavedUnsavedData("diagnosis");
            tabs.value[i].icon = isDateInArray(sessionD, diagnosisData) ? "check" : "";
        } else if (tab.title === "Complications Screening") {
            const screeningData = getOfflineSavedUnsavedData("screening");
            tabs.value[i].icon = isDateInArray(sessionD, screeningData) ? "check" : "";
        } else if (tab.title === "Treatment Plan") {
            if (selectedNCDMedicationList.value.length > 0) {
                tabs.value[i].icon = MedicationSelectionHasValues() ? "check" : "";
            } else {
                tabs.value[i].icon = "";
            }
        }
    }
};

const isDateInArray = (dateToCheck: any, diagnosisArray: any) => {
    // Convert input date to start of day for comparison
    const checkDate = new Date(dateToCheck);
    checkDate.setHours(0, 0, 0, 0);

    return diagnosisArray.some((diagnosis: any) => {
        // Convert each obs_datetime to start of day
        const obsDate = new Date(diagnosis.obs_datetime);
        obsDate.setHours(0, 0, 0, 0);

        return obsDate.getTime() === checkDate.getTime();
    });
};

const saveVitals = async () => {
    const vitalsReasons = await formatCheckboxInputData(vitals.value);
    const newVitals = await formatInputFiledData(vitals.value);
    if (newVitals.length > 0 || vitalsReasons.length > 0) {
        let vitals = patient.value?.vitals;
        vitals.unsaved = [...vitals.unsaved, ...newVitals, ...vitalsReasons];
        toastSuccess("Vitals saved successful");
    } else toastWarning("Vitals not saved");
};

const saveComplications = async () => {
    const data = [];
    const childDataVisualScreening = await formatInputFiledData(visualScreening.value);
    const childDataFootScreening = await formatGroupRadioButtonData(FootScreening.value);
    const childDataCVRisk = await formatInputFiledData(cvScreening.value);

    if (childDataVisualScreening.length > 0) {
        data.push({
            concept_id: await ConceptService.getConceptID("Visual acuity", true),
            value_text: "visual acuity test",
            obs_datetime: ConceptService.getSessionDate(),
            child: childDataVisualScreening,
        });
    }

    if (childDataFootScreening.length > 0) {
        data.push({
            concept_id: await ConceptService.getConceptID("Foot check", true),
            value_text: "foot screening",
            obs_datetime: ConceptService.getSessionDate(),
            child: childDataFootScreening,
        });
    }

    if (childDataCVRisk.length > 0) {
        data.push(...childDataCVRisk);
    }

    if (data.length > 0) {
        (patient.value.screening ??= {}).unsaved ??= [];
        patient.value.screening.unsaved.push(...data);
        toastSuccess("Complications saved successfully");
    }
};

const saveSubstanceAbuse = async () => {
    (patient.value.substanceAbuse ??= {}).unsaved ??= [];
    const substanceAbuse = await formatRadioButtonData(substance.value);
    if (substanceAbuse.length > 0) {
        patient.value.substanceAbuse.unsaved.push(...substanceAbuse);
        toastSuccess("Substance abuse saved successfully");
    } else {
        toastWarning("Substance abuse not saved");
    }
};

const saveTreatmentPlan = async () => {
    const userID = Service.getUserID();
    const patientID = patient.value.patientID;
    const treatmentInstance = new Treatment();
    const allergyStore = useAllegyStore();

    if (!isEmpty(allergyStore.selectedMedicalAllergiesList)) {
        const userStore = useUserStore();
        const allergies = allergyStore.selectedMedicalAllergiesList.map((allergy: any) => ({
            concept_id: 985,
            obs_datetime: Service.getSessionDate(),
            value_coded: allergy.concept_id,
            location_id: userStore.facilityLocation.code,
        }));
        // await treatmentInstance.onSubmitAllergies(patientID, userID, allergies);
        stageAllergies(allergies);
        allergyStore.clearSelectedMedicalAllergiesList();
    }

    if (!isEmpty(treatmentPlanStore.nonPharmalogicalTherapyAndOtherNotes)) {
        const userStore = useUserStore();
        const treatmentNotesTxt = [
            {
                concept_id: 2688,
                obs_datetime: Service.getSessionDate(),
                value_text: treatmentPlanStore.nonPharmalogicalTherapyAndOtherNotes,
                location_id: userStore.facilityLocation.code,
            },
        ];

        // treatmentInstance.onSubmitNotes(patientID, userID, treatmentNotesTxt);
        await stageNotes(treatmentNotesTxt);
    }

    await createNCDDrugOrder();
    await useNonPharmaTherapyStore().saveNonPharmaTherapyPatientData();
};

const saveData = async () => {
    await saveVitals();
    await saveTreatmentPlan();
    await saveSubstanceAbuse();
    await saveComplications();
    await resetNCDPatientData();
    await saveOfflinePatientData(patient.value);
    await printBarcode();
    router.push("patientProfile");
};
const printBarcode = async () => {
    const response: any = await createModal(ConfirmPrinting, { class: "small-confirm-modal " });
    if (response === "dismiss") return;
    await printVisitSummary();
};
// Lifecycle hooks and watchers
onMounted(async () => {
    if (generalStore.NCDActivities.length === 0) {
        router.push("patientProfile");
        return;
    }
    const data = useComplicationsStore();
    data.resetScreening();
    tabs.value = getActiveTabs();
    await markWizard();

    // Ensure initial tab selection is properly set
    if (currentTabIndex.value === undefined || currentTabIndex.value < 0) {
        currentTabIndex.value = 0;
        console.log("Setting initial tab index to 0");
    }
});

watch(
    vitals,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    patient,
    async () => {
        const data = useComplicationsStore();
        data.resetScreening();
        await markWizard();
    },
    { deep: true }
);

watch(
    sessionDate,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    investigations,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    diagnosis,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    substance,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    selectedNCDMedicationList,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    route,
    async (newRoute) => {
        refreshWizard();
        cleanVitalForm();
        tabs.value = getActiveTabs();
    },
    { deep: true }
);

watch(
    patient,
    async (old, newData) => {
        if (old.ID != newData.ID) {
            refreshWizard();
            cleanVitalForm();
        }
    },
    { deep: true }
);

// Expose needed methods and properties
defineExpose({
    saveData,
    markWizard,
    refreshWizard,
});
</script>

<style scoped></style>
