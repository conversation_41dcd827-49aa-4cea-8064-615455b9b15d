import ArtTasks from "./ART/config/tasks";
import TaskView from "./ART/TaskView.vue";
import { createModal } from "@/utils/Alerts";

const PROGRAM_TASKS: Record<number, any> = {
    1: ArtTasks,
};

export async function showProgramTasks(programId: number, taskType: "tasks" | "other" = "tasks") {
    const tasks = PROGRAM_TASKS[programId] ?? {};
    createModal(TaskView, {}, true, {
        tasks: tasks?.[taskType] ?? [],
    });
}
