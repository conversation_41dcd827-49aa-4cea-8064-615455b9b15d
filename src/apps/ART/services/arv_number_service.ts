import { PatientService } from "@/services/patient_service";
import { IdentifierService } from "@/services/identifier_service";
import { ProgramService } from "@/services/program_service";
import { toastWarning, toastSuccess } from "@/utils/Alerts";
import { useGlobalPropertyStore } from "@/stores/GlobalPropertyStore";
import { useDemographicsStore } from "@/stores/DemographicStore";
export class ArvNumberService extends PatientService {
  suggestedNextARVNumber: any;
  patientHasARVNumber: boolean = false;
  prependValue: string = "";

  async _getArvNumber_() {
    try {
        const demographicsStore = useDemographicsStore();
        const patientID = demographicsStore.$state?.patient.patientID;
        
        if (!patientID) {
            return '';
        }
        
        const patientData = await PatientService.findByID(patientID);
        
        if (!patientData?.patient_identifiers) {
            return '';
        }
        
        // Find the ARV number (identifier_type == 4)
        const arvIdentifier = patientData.patient_identifiers.find(
            (indnt: any) => indnt.identifier_type == 4
        );
        
        return arvIdentifier?.identifier || '';
        
    } catch (error) {
        // console.error('Error getting ARV number:', error);
        return '';
    }
}

  /**
   * returns next available arv number for a particular site(facility)
   */  
  async getARVnumber() {
    try {
      const arvNumber = await this._getArvNumber_()
      if(arvNumber !== "Unknown") {
        // Existing ARV number handling
        const a = arvNumber.split('-')
        this.suggestedNextARVNumber = a[2].replace(/^\D+|\s/g, "")
        this.prependValue = `${a[0]}-${a[1]}-`
        this.patientHasARVNumber = true
      } else {
        // Auto-generate new number
        const suggestedNumber = await ProgramService.getNextSuggestedARVNumber();
        this.suggestedNextARVNumber = suggestedNumber.arv_number.replace(/^\D+|\s/g, "");
      }
    } catch (error) {
        // Auto-generate new number
        const suggestedNumber = await ProgramService.getNextSuggestedARVNumber();
        this.suggestedNextARVNumber = suggestedNumber.arv_number.replace(/^\D+|\s/g, "");
    }

    return this.suggestedNextARVNumber;
  }

  async saveARVNumber(arv_number: string) {
    const globalPropertyStore = useGlobalPropertyStore();
    const sitePrefix = globalPropertyStore.$state.globalPropertyStore.sitePrefix
    const newArvNumber = `${sitePrefix}-ARV-${arv_number}`
        if (newArvNumber === await this._getArvNumber_()) {
            toastWarning("ARV number is the same as the current one", 5000)
            return
        } 
        const exists = await IdentifierService.arvNumberExists(newArvNumber)
        if (exists) {
            toastWarning("ARV number already exists", 5000)
        } else {
          try {
            
            if (this.patientHasARVNumber) {
                await this.updateARVNumber(newArvNumber)
                toastSuccess("ARV number updated successfully")
            } else {
                await this.createArvNumber(newArvNumber)
                toastSuccess("ARV number created successfully")
            } 
          } catch (e) {
            toastWarning(`${e}`)
          }
        }   
    }
}