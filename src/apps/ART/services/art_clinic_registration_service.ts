import { AppEncounterService } from "@/services/app_encounter_service";
import { PatientRegistrationService } from "@/services/patient_registration_service";
import { ObservationService } from "@/services/observation_service";
import { useUserStore } from "@/stores/userStore";
import { Service } from "@/services/service";

interface Option {
    label: string;
    value: string;
}

export class ClinicRegistrationService extends AppEncounterService {
    constructor(patientID: number, providerID: number) {
        super(patientID, 9, providerID); //TODO: Use encounter type reference name
    }
}

export class ARTClinicRegistrationService extends AppEncounterService {
    private registration: ClinicRegistrationService;

    constructor(patientID: number, providerID: number) {
        super(patientID, 9, providerID);
        this.registration = new ClinicRegistrationService(patientID, providerID);
    }

    /**
     * Main form submission handler
     */
    async onSubmit(computedData: any): Promise<void> {
        const _encounter_ = await this.registration.createEncounter();
        if (!_encounter_) throw new Error("Failed to create encounter");
        this.encounterID = _encounter_.encounter_id;
        this.saveObservationList(computedData);
    }

    /**
     * Compute observations for followup agreements
     */
    computeFollowupAgreements = async (v: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueCoded("Agrees to followup", v.value),
    });

    // computeFollowupAgreements = (d: Array<Option>) => {
    //     const obs: any = [];
    //     d.forEach(async ({ label, value }: Option) => {
    //         obs.push(await this.registration.buildValueCoded(label, value));
    //         obs.push(await this.registration.buildValueCoded('Agrees to followup', label));
    //     });
    //     return { tag: 'reg', obs };
    // }

    /**
     * Compute HTC Serial number observation
     */
    computeHTCSerialNumber = async (v: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueText("HTC Serial number", v.value),
    });

    /**
     * Compute "Ever received ART" observation
     */
    computeEverReceivedART = async ({ value }: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueCoded("Ever received ART", value),
    });

    /**
     * Compute date ART was last taken
     */
    computeARTLastTakenDate = async (date: string, isEstimate: boolean) => ({
        date,
        tag: "reg",
        isEstimate,
        obs: await this.buildDateObs("Date ART last taken", date, isEstimate),
    });

    /**
     * Compute location of ART initiation
     */
    computeARTInitiationLocation = async ({ label }: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueText("Location of ART initiation", label),
    });

    /**
     * Compute ART number at previous location
     */
    computeARTNumberAtPreviousLocation = async (d: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueText("ART number at previous location", d.value),
    });

    /**
     * Compute "Has transfer letter" observation
     */
    computeHasTransferLetter = async ({ value }: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueCoded("Has transfer letter", value),
    });

    /**
     * Compute confirmatory HIV test type
     */
    computeConfirmatoryHIVTestType = async ({ value }: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueCoded("Confirmatory hiv test type", value),
    });

    /**
     * Compute confirmatory HIV test location
     */
    computeConfirmatoryHIVTestLocation = async (d: Option) => ({
        tag: "reg",
        obs: await this.registration.buildValueText("Confirmatory HIV test location", d.label),
    });

    /**
     * Compute confirmatory HIV test date
     */
    computeConfirmatoryHIVTestDate = async (date: string, isEstimate: boolean) => ({
        date,
        tag: "reg",
        isEstimate,
        obs: await this.buildDateObs("Confirmatory HIV test date", date, isEstimate),
    });

    computeARTRegistrationDate = async (date: string, isEstimate: boolean) => ({
        date,
        tag: "reg",
        isEstimate,
        obs: await this.buildDateObs("ART initiation date", date, isEstimate),
    });

    computeARTStartDate = async (date: string, isEstimate: boolean) => ({
        date,
        tag: "reg",
        isEstimate,
        obs: await this.buildDateObs("Contact date ART started", date, isEstimate),
    });

    computeInitialWeight = async (value: number) => ({
        tag: "reg",
        obs: await fixValueNumericStructureInPlace(await this.registration.buildValueNumber("weight", value, null, null)),
    });

    computeInitialHeight = async (value: number) => ({
        tag: "reg",
        obs: await fixValueNumericStructureInPlace(await this.registration.buildValueNumber("height", value, null, null)),
    });

    computeTBStatus = async (value: string) => {
        const userStore = useUserStore();
        return {
            tag: "reg",
            obs: {
                concept_id: await AppEncounterService.getConceptID("TB treatment start date"),
                value_datetime: ObservationService.getSessionDate(),
                obs_datetime: ObservationService.getSessionDate(),
                providerID: Service.getUserID(),
                programID: ObservationService.getProgramID(),
                location_id: userStore.facilityLocation.code,
                value_coded: await AppEncounterService.getConceptID(value as string),
            },
        };
    };

    /**
     * Helper method to build date observations
     */
    buildDateObs = async (conceptName: string, date: string, isEstimate: boolean) => {
        if (/unknown/i.test(date)) return this.buildValueText(conceptName, "Unknown");
        if (isEstimate) return this.buildValueDateEstimated(conceptName, date);
        return await this.buildValueDate(conceptName, date);
    };
}

function fixValueNumericStructureInPlace(obj: any) {
    console.log("fixValueNumericStructureInPlace", obj);
    if (obj.value_numeric.value) {
        obj.value_numeric = obj.value_numeric.value;
    }
    return obj;
}
