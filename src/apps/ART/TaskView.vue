<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>Program activities</ion-title>
        <ion-button @click="onClose" fill="clear" color="danger" slot="end">
          <ion-icon size="large" :icon="close"></ion-icon>
        </ion-button>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <ion-grid v-if="tasks && tasks.length > 0">
        <ion-row>
          <ion-col size="4" v-for="(task, index) in tasks" :key="index">
            <ion-item button color="light" lines="full" @click="handleTaskClick(task)"> 
              <ion-icon :color="task.iconColor" :icon="task.icon"></ion-icon>
              <ion-label> 
                {{ task.label }}
              </ion-label>
            </ion-item>
          </ion-col>
        </ion-row>
      </ion-grid>
      <div v-else class="no-tasks">
        <ion-icon :icon="clipboardOutline" class="no-tasks-icon"></ion-icon>
        <p>No tasks available</p>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import {
    IonButton,
    IonItem,
    IonLabel,
    IonPage,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonGrid,
    IonRow,
    IonCol,
    IonIcon,
    modalController
} from '@ionic/vue';
import { PropType } from "vue";
import { clipboardOutline, close } from 'ionicons/icons';

defineProps({
  tasks: {
    type: Object as PropType<any[]>,
    required: true
  }
});

function onClose() {
  modalController.dismiss()
}

const handleTaskClick = (task: { label: string; icon: string; action?: Function }) => {
  if (task.action && typeof task.action === 'function') {
    task.action();
  } else {
    console.warn(`No action defined for task: ${task.label}`);
  }
};
</script>

<style scoped>
.task-date {
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.task-button {
  --background: #f2f2f2;
  --color: black;
  height: 60px;
  font-size: 0.95rem;
  border-radius: 8px;
  box-shadow: none;
}

ion-icon {
  margin-right: 12px;
  font-size: 2.5rem;
}

.save-button {
  margin-top: 2rem;
}

.no-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #888;
}

.no-tasks-icon {
  font-size: 5rem;
  margin-bottom: 1rem;
  color: #ccc;
}
</style>