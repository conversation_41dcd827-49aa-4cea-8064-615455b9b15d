import { defineStore } from "pinia";
import { cloneDeep } from "lodash";
import { AdherenceService } from "../services/adherence_service";

interface MedicationData {
  name: string;
  remaining: number;
  tabsGiven: number;
  tabsPer: string;
  expected: number;
  actual: number;
  dosesMissed: number;
  dosesConsumed: number;
  adherenceStatus: string;
  orderId: number;
  drugId: number;
}

interface ARTAdherenceState {
  lastVisitDate: string;
  daysElapsed: number;
  medications: MedicationData[];
  agreeWithCalculation: boolean;
}

export const useARTAdherenceStore = defineStore("ARTAdherence", {
  state: (): ARTAdherenceState => ({
    lastVisitDate: "",
    daysElapsed: 40,
    medications: [],
    agreeWithCalculation: true
  }),

  actions: {
    processMedicationData(adherenceService: AdherenceService) {
    const rawMedications =  adherenceService.getLastDrugs();
    const processed = rawMedications.map((item) => {
    const name = item.drug.name;
    const tabsGiven = item.barcodes.reduce((sum: number, b: any) => sum + b.tabs, 0);
    const remaining = 0; // Replace with actual remaining if available
    const frequency = item.frequency || "";
      const tabsPer = frequency.includes("ONCE A DAY") ? "1 OOD" : frequency;
      
    const {
      expected,
      actual,
      dosesMissed,
      dosesConsumed,
      adherenceStatus
    } = this.calculateAdherence({
      tabsGiven,
      remaining,
      daysElapsed: this.daysElapsed,
      frequency
    });      return {
      name,
      remaining,
      tabsGiven,
      tabsPer,
      expected,
      actual,
      dosesMissed,
      dosesConsumed,
      adherenceStatus,
      orderId: item.order.order_id,
      drugId: item.drug.drug_id,
      drug: item
      };
    });

      this.medications = processed;
      this.lastVisitDate = adherenceService.getReceiptDate();
    },

    updateMedicationRemaining(index: number, value: number) {
      if (index >= 0 && index < this.medications.length) {
        this.medications[index].remaining = value;
      }
    },
    
    updateExpected(index: number, value: number) {
      if (index >= 0 && index < this.medications.length) {
        this.medications[index].expected = value;
      }
    },
    
    updateActual(index: number, value: number) {
      if (index >= 0 && index < this.medications.length) {
        this.medications[index].actual = value;
      }
    },
    
    setAgreeWithCalculation(value: boolean) {
      this.agreeWithCalculation = value;
    },
    
    
    // This would be implemented later when backend logic is added
    calculateAdherence({
      tabsGiven,
      remaining,
      daysElapsed,
      frequency
    }: {
      tabsGiven: number;
      remaining: number;
      daysElapsed: number;
      frequency: string;
    }) {
      const tabsPerDay = (() => {
        if (/once a day/i.test(frequency)) return 1;
        if (/twice a day/i.test(frequency)) return 2;
        if (/thrice a day/i.test(frequency)) return 3;
        return 1; // default fallback
      })();

      const expected = daysElapsed * tabsPerDay;
      const actual = tabsGiven - remaining;
      const dosesMissed = Math.max(expected - actual, 0);
      const dosesConsumed = Math.min(actual, expected);

      let adherenceStatus = "Good";
      const adherencePercentage = (dosesConsumed / expected) * 100;

      if (adherencePercentage < 95) {
        adherenceStatus = "Explore problem";
      } else if (adherencePercentage < 100) {
        adherenceStatus = "Acceptable";
      }

      return {
        expected,
        actual,
        dosesMissed,
        dosesConsumed,
        adherenceStatus
      };
    }
  }
  
});
