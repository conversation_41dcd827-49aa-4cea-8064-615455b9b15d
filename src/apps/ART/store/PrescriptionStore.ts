import { defineStore } from "pinia";
import { icons } from "@/utils/svg";
import _, { cloneDeep } from "lodash";
import * as yup from "yup";
import { extractArrayOfNameValue, validateStore } from "@/services/data_helpers";

const initialPrescription = {
    header: {
        title: "Prescription",
        backLink: true
    },
    sections: [
        {
            type: "radio",
            label: "Allergic to cotrimoxazole",
            name: "allergy",
            value: "",
            options: [
                { label: "Yes", value: "Yes" },
                { label: "No", value: "No" },
                { label: "Unknown", value: "Unknown" }
            ]
        },
        {
            type: "checkbox",
            label: "Medication to prescribe during this visit",
            name: "medications",
            value: [],
            options: [
                { label: "ARVs", value: "ARVs" },
                { label: "CPT", value: "CPT" },
                { label: "3HP (RFP + INH)", value: "3HP" },
                { label: "INH 300 / RFP 300 (3HP)", value: "INH300" },
                { label: "IPT", value: "IPT" },
                { label: "None", value: "None" }
            ]
        },
        {
            type: "section",
            label: "ARV Regimens",
            name: "regimens",
            patientInfo: {
                age: "34 Years",
                gender: "F",
                currentRegimen: "N/A",
                currentWeight: "60 Kg(s)",
                reasonForChange: "N/A"
            },
            regimens: [
                { id: "4A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "5A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "7A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "8A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "9A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "10A", name: "LPV/r 200/50 + TDF 300 / 3TC 300" },
                { id: "11A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "12A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "13A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "14A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "15A", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "15P", name: "AZT 300 / 3TC 150 + EFV 400" },
                { id: "17A", name: "AZT 300 / 3TC 150 + EFV 400" }
            ],
            selectedRegimen: "",
            customRegimen: false
        },
        {
            type: "table",
            label: "Selected medication",
            name: "selectedMedications",
            columns: [
                { header: "Drug name", key: "drugName" },
                { header: "Units", key: "units" },
                { header: "AM", key: "am" },
                { header: "Noon", key: "noon" },
                { header: "PM", key: "pm" },
                { header: "Frequency", key: "frequency" }
            ],
            data: [
                {
                    drugName: "AZT 300 / 3TC 150",
                    units: "tabs",
                    am: 1,
                    noon: 0,
                    pm: 1,
                    frequency: "Daily (QOD)"
                },
                {
                    drugName: "EFV 400",
                    units: "tabs",
                    am: 1,
                    noon: 0,
                    pm: 1,
                    frequency: "Daily (QOD)"
                }
            ]
        },
        {
            type: "select",
            label: "Interval to next visit",
            name: "nextVisitInterval",
            required: true,
            value: "1",
            options: [
                { label: "1 month", value: "1" },
                { label: "2 months", value: "2" },
                { label: "3 months", value: "3" }
            ]
        },
        {
            type: "info",
            label: "Medication run out date",
            name: "medicationRunOutDate",
            value: "03/Jun/2025"
        },
        {
            type: "packs",
            label: "Estimated packs/tins",
            name: "estimatedPacks",
            data: [
                { name: "AZT 300 / 3TC 150", quantity: 1 },
                { name: "EFV 400", quantity: 1 }
            ]
        }
    ]
};

export const usePrescriptionStore = defineStore("PrescriptionStore", {
    state: () => ({
        prescription: cloneDeep(initialPrescription)
    }),

    actions: {
        setPrescription(data: any) {
            this.prescription = data;
        },

        getInitial() {
            return cloneDeep(initialPrescription);
        },

        updateField(name: string, value: any) {
            const section = this.prescription.sections.find(s => s.name === name);
            if (section) {
                section.value = value;

                // Handle special cases
                if (name === 'medications') {
                    this.updateMedicationsVisibility(value);
                } else if (name === 'regimens') {
                    this.updateSelectedMedications(value);
                } else if (name === 'nextVisitInterval') {
                    this.updateMedicationRunOutDate(value);
                }
            }
        },

        updateMedicationsVisibility(selectedMeds: string[]) {
            const regimensSection = this.prescription.sections.find(s => s.name === 'regimens');
            if (regimensSection) {
                // Add visible property if it doesn't exist
                (regimensSection as any).visible = selectedMeds.includes('ARVs');
            }
        },

        updateSelectedMedications(regimenId: string) {
            const regimensSection = this.prescription.sections.find(s => s.name === 'regimens');
            const selectedMedsSection = this.prescription.sections.find(s => s.name === 'selectedMedications');

            if (regimensSection && selectedMedsSection) {
                // Cast to any to access the regimens property
                const regimen = (regimensSection as any).regimens?.find((r: any) => r.id === regimenId);
                if (regimen) {
                    if (regimen.name.includes('AZT 300 / 3TC 150 + EFV 400')) {
                        selectedMedsSection.data = [
                            {
                                drugName: "AZT 300 / 3TC 150",
                                units: "tabs",
                                am: 1,
                                noon: 0,
                                pm: 1,
                                frequency: "Daily (QOD)"
                            },
                            {
                                drugName: "EFV 400",
                                units: "tabs",
                                am: 1,
                                noon: 0,
                                pm: 1,
                                frequency: "Daily (QOD)"
                            }
                        ];
                    } else if (regimen.name.includes('LPV/r 200/50 + TDF 300 / 3TC 300')) {
                        selectedMedsSection.data = [
                            {
                                drugName: "LPV/r 200/50",
                                units: "tabs",
                                am: 2,
                                noon: 0,
                                pm: 2,
                                frequency: "Daily (QOD)"
                            },
                            {
                                drugName: "TDF 300 / 3TC 300",
                                units: "tabs",
                                am: 1,
                                noon: 0,
                                pm: 0,
                                frequency: "Daily (QOD)"
                            }
                        ];
                    }
                    this.updateEstimatedPacks();
                }
            }
        },

        updateMedicationRunOutDate(months: string) {
            const infoSection = this.prescription.sections.find(s => s.name === 'medicationRunOutDate');
            if (infoSection) {
                const date = new Date();
                date.setMonth(date.getMonth() + parseInt(months));
                infoSection.value = date.toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                });
            }
        },

        updateEstimatedPacks() {
            const selectedMedsSection = this.prescription.sections.find(s => s.name === 'selectedMedications');
            const packsSection = this.prescription.sections.find(s => s.name === 'estimatedPacks');
            const nextVisitSection = this.prescription.sections.find(s => s.name === 'nextVisitInterval');

            if (selectedMedsSection && packsSection && nextVisitSection) {
                // Use type assertion to handle the value property
                const months = parseInt(String(nextVisitSection.value || '1'));

                // Use type assertion to handle the data property and its structure
                if (selectedMedsSection.data && Array.isArray(selectedMedsSection.data)) {
                    packsSection.data = selectedMedsSection.data.map((med: any) => ({
                        name: med.drugName,
                        quantity: Math.ceil(((med.am + med.noon + med.pm) * 30 * months) / 60) // Assuming 60 tablets per pack
                    }));
                }
            }
        }
    }
});
