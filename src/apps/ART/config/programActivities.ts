import { TaskInterface } from "../../interfaces/TaskInterface";
import { alertConfirmation } from "@/utils/Alerts";
import { delayPromise } from "@/utils/Timers";

export const ART_PRIMARY_ACTIVITIES: TaskInterface[] = [
    {
        id: "hiv clinic registration",
        name: "Hiv clinic registration",
        workflowID: "HIV first visits",
        icon: "registration.png",
    },
    {
        id: "hiv reception",
        name: "HIV reception",
        workflowID: "HIV reception visits",
        icon: "reception.png",
    },
    {
        id: "vitals",
        name: "<PERSON><PERSON>",
        workflowID: "Vitals",
        icon: "vitals.png",
        taskCompletionChecklist: ["Weight"],
    },
    {
        id: "hiv staging",
        name: "HIV staging",
        workflowID: "HIV staging visits",
        icon: "hiv-staging.png",
    },
    {
        id: "hiv clinic consultation",
        name: "HIV clinic consultation",
        workflowID: "HIV clinic consultations",
        taskCompletionChecklist: ["Medication orders"],
        icon: "consultation.png",
    },
    {
        id: "art adherence",
        name: "ART adherence",
        workflowID: "ART adherence",
        icon: "adherence.png",
    },
    {
        id: "treatment",
        name: "Treatment",
        workflowID: "Prescriptions",
        icon: "prescription.png",
    },
    {
        id: "fast track assesment",
        name: "Fast Track assesment",
        icon: "fast-track.png",
    },
    {
        id: "dispensing",
        name: "Drug Dispensations",
        encounterTypeName: "DISPENSING",
        workflowID: "Drug Dispensations",
        icon: "dispensing.png",
    },
    {
        id: "appointment",
        name: "Manage Appointments",
        encounterTypeName: "APPOINTMENT",
        workflowID: "Manage Appointments",
        icon: "appointment.png",
    },
    {
        id: "patient type",
        name: "Patient Type",
        encounterTypeName: "Registration",
        icon: "patient-type.png",
    },
    {
        id: "bp_management",
        name: "bp_management",
        icon: "dispensing.png",
        encounterTypeName: "Hypertension management",
        availableOnActivitySelection: false,
    },
    {
        id: "bp_alert",
        name: "bp_alert",
        icon: "dispensing.png",
        availableOnActivitySelection: false,
    },
];

export const ART_SECONDARY_ACTIVITIES: TaskInterface[] = [
    {
        id: "demographics",
        name: "Demographics (Print)",
        description: "Print Patient Demographics",
        icon: "print.png",
    },
    {
        id: "visit_summary",
        name: "Visit Summary (Print)",
        description: "Print Patient Visit Summary",
        icon: "folder.png",
    },
    {
        id: "master_card",
        name: "Master card",
        description: "View mastercard",
        action: ({ patient }: any, router: any) => {
            router.push(`/art/mastercard/${patient.patient_id}`);
        },
        icon: "card.png",
    },
    {
        id: "f_number",
        name: "Filing Number (Print)",
        description: "Print Patient Filing Number",
        icon: "folder.png",
    },
    {
        id: "archive_client",
        name: "Archive client",
        description: "Archive a client",
        action: ({ patient }: any, router: any) => {
            delayPromise(200).then(() => {
                alertConfirmation("Are you sure you want to archive patient?").then((ok) => {
                    if (ok) {
                        router.push(`/art/filing_numbers/${patient.patient_id}?archive=true`);
                    }
                });
            });
        },
        icon: "archive.png",
    },
    {
        id: "assign_filing_number",
        name: "Assign filing number",
        description: "Assign a new filing number",
        icon: "archive.png",
    },
    {
        id: "filing_number_trail",
        name: "View filing number trail",
        description: "view trail",
        action: ({ patient }: any, router: any) => {
            router.push(`/art/filing_numbers/${patient.patient_id}?trail=true`);
        },
        icon: "folder.png",
    },
    {
        id: "art notes",
        name: "ART Clinical Notes",
        icon: "clinical-notes.png",
        availableOnActivitySelection: false,
    },
    {
        id: "emergency_supply",
        name: "Emergency drug supply",
        description: "",
        action: ({ patient }: any, router: any) => {
            router.push(`/art/emergency_supply/${patient.patient_id}`);
        },
        icon: "archive.png",
    },
];
