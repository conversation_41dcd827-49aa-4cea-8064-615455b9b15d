<template>
  <report-table
    title="MoH Disaggregated Report"
    report-type="MoH"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="getDrillTitle"
    useDateRangeFilter
    showIndices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { DisaggregatedReportService } from "@/services/reports/disagregated_report_service";
import { REGIMENS } from "@/constants";
import { toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { useRoute } from "vue-router";

const report = new DisaggregatedReportService()
const period = ref("-");
const rows =  ref<Array<any>>([]);
    
const columns: TableColumnInterface[] = [
  { path: "age_group", label: "Age Group" },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "tx_curr", label: "TX curr (receiving ART)", drillable: true },
  ...REGIMENS.map(r => ({ path: r, label: r, drillable: true })),
  { path: "unknown", label: "Unknown", drillable: true },
  { path: "total", label: "Total", drillable: true },
]

const fetchData =  async ({ dateRange }: any, regenerate: boolean) => {
  try {
    await loader.show();
    report.setStartDate(dateRange.startDate);
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.getDisaggReport(regenerate);
  } catch (error) {
    console.error(error);
    toastWarning("Error! Unable to generate report")
  }
  loader.hide();
}

function getDrillTitle(data: DrilldownData) {
  return `${data.row.age_group} | ${data.column.label} | ${toDisplayGenderFmt(data.row.gender)}s`
}

onMounted(() => {
  const { startDate, endDate } = useRoute().query;
  if(startDate && endDate) {
    fetchData({
      dateRange: {
        startDate,
        endDate
      }
    }, false);
  }
})
</script>