<template>
    <ion-card style="padding: 0 !important">
        <ion-card-header style="border-bottom: 1px solid #c2c2c2; font-weight: 500; color: #000">
            <ion-card-title>MoH Cohort Report</ion-card-title>
        </ion-card-header>
        <ion-card-content class="ion-no-padding" style="min-height: 45vh">
            <ion-grid>
                <ion-row>
                    <ion-col size="2">
                        <v-select v-model="quarter" :options="quarters" placeholder="Select Quarter" />
                    </ion-col>
                    <ion-col size="4" v-if="useCustomQuarter" style="display: flex; justify-content: flex-start">
                        <vue-date-picker
                            v-model="startDate"
                            placeholder="Start Date"
                            :enable-time-picker="false"
                            format="dd/MMM/yyyy"
                            auto-apply
                            text-input
                            class="ion-margin-end"
                        />
                        <vue-date-picker
                            v-model="endDate"
                            placeholder="End Date"
                            :enable-time-picker="false"
                            format="dd/MMM/yyyy"
                            auto-apply
                            text-input
                            class="ion-margin-end"
                        />
                    </ion-col>
                    <ion-col :size="useCustomQuarter ? '6' : '10'">
                        <ion-button class="ion-float-right" color="primary" @click="toCSV">CSV</ion-button>
                        <ion-button class="ion-float-right" color="primary" @click="printSpec">PDF</ion-button>
                        <ion-button
                            class="ion-float-right"
                            color="secondary"
                            @click="goDisagreggatedReport"
                            :disabled="hasInvalidFilters || isEmpty(indicators)"
                            >Disaggregated</ion-button
                        >
                        <ion-button class="ion-float-right" color="warning" @click="fetchData(true)">Fresh Report</ion-button>
                        <ion-button class="ion-float-right" color="success" @click="fetchData()">Archived Report</ion-button>
                    </ion-col>
                </ion-row>
                <ion-row class="his-card">
                    <ion-col size="12" :key="componentKey" id="report-content">
                        <cohort-v :indicators="indicators" style="font-weight: 600" />
                        <cohort-h :reportparams="period" />
                        <cohort-ft @onClickIndicator="onDrilldown" :indicators="indicators" />
                    </ion-col>
                </ion-row>
            </ion-grid>
        </ion-card-content>
    </ion-card>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { loader } from "@/utils/loader";
import { modal } from "@/utils/modal";
import CohortH from "./CohortHeader.vue";
import CohortV from "./CohortValidator.vue";
import CohortFt from "./CohortFooter.vue";
import { IonCol, IonRow, IonGrid, IonButton, IonCard, IonCardHeader, IonCardContent, IonCardTitle } from "@ionic/vue";
import { RowActionButtonInterface, TableColumnInterface } from "@uniquedj95/vtable";
import VueDatePicker from "@vuepic/vue-datepicker";
import { useRouter } from "vue-router";
import { parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { getReportQuarters, isValidDateRange, toDisplayFmt } from "@/utils/his_date";
import { CohortReportService } from "@/services/reports/cohort_report_service";
import { parameterizeUrl } from "@/utils/Url";
import { exportToCSV, getCsvExportBtn, getPdfExportBtn } from "@/utils/exports";
import useFacility from "@/composables/useFacility";
import VSelect from "vue-select";
import DrilldownTable from "@/components/DrilldownTable.vue";
import { eye } from "ionicons/icons";
import { isEmpty } from "lodash";

const router = useRouter();
const componentKey = ref(0);
const quarter = ref();
const period = ref<string>("");
const startDate = ref("");
const endDate = ref("");
const indicators = ref({} as Record<string, any>);
const cohort = ref({} as Record<string, any>);
const report = new CohortReportService();
const useCustomQuarter = computed(() => /custom/i.test(quarter.value?.label));
const hasInvalidFilters = computed(() => {
    if (isEmpty(quarter.value)) return true;
    if (useCustomQuarter.value) {
        return !isValidDateRange(startDate.value, endDate.value);
    }
    return false;
});

const quarters: any[] = [
    { label: "Custom", value: "Custom" },
    ...getReportQuarters(10).map((q) => ({
        label: q.name,
        value: q.name,
        other: q,
    })),
];

watch(useCustomQuarter, (isCustom) => {
    if (isCustom) {
        startDate.value = "";
        endDate.value = "";
    }
});

function goDisagreggatedReport() {
    if (!hasInvalidFilters.value) {
        router.push(
            parameterizeUrl("/reports/moh/disaggregated", {
                startDate: report.startDate,
                endDate: report.endDate,
            })
        );
    } else {
        toastWarning("Please select a period");
    }
}

async function onDrilldown(indicator: string) {
    const drillColumns: TableColumnInterface[] = [
        { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
        { path: "given_name", label: "First Name", exportable: false },
        { path: "family_name", label: "Last Name", exportable: false },
        { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
        { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
        { path: "outcome", label: "Outcome" },
    ];
    try {
        loader.show();
        const indicatorData = cohort.value.find((i: any) => i.name === indicator);
        const title = indicatorData["indicator_name"] || "Drill down";
        const res = await report.getCohortDrillDown(indicatorData.id);
        const drillRows: Array<any> = res.data ?? [];
        await loader.hide();
        return modal.show(DrilldownTable, {
            title,
            columns: drillColumns,
            rows: drillRows,
            rowActionButtons: [
                {
                    icon: eye,
                    action: ({ person_id }) => {
                        return router.push(`/patient/${person_id}`);
                    },
                } as RowActionButtonInterface,
            ],
            actionButtons: [
                getCsvExportBtn(title, quarter.value?.label, period.value),
                getPdfExportBtn(title, false, quarter.value?.label, period.value),
            ],
        });
    } catch (error) {
        loader.hide();
        console.error(error);
        toastWarning("Unable to drill down the report");
    }
}

const setReportPeriod = (quarter: string, startDate: string, endDate: string) => {
    report.setQuarter(quarter);
    report.setStartDate(startDate);
    report.setEndDate(endDate);
};

/**
 * Transform indicators from array to a simple key value pair object
 */
const toIndicators = (params: any) => {
    return params.reduce((data: Record<string, number>, indicator: any) => {
        data[indicator.name] = parseInt(indicator.contents);
        return data;
    }, {});
};

async function fetchData(regenerate = false) {
    if (hasInvalidFilters.value) {
        return toastWarning("Please select report period");
    }

    loader.show();
    let data: any = {};
    indicators.value = {};
    cohort.value = {};
    report.setRegenerate(regenerate);

    if (useCustomQuarter.value) {
        setReportPeriod(quarter.value, startDate.value, endDate.value);
        period.value = `Custom ${report.getDateIntervalPeriod()}`;
        data = report.datePeriodRequestParams();
    } else {
        setReportPeriod(quarter.value?.label, quarter.value?.other.start, quarter.value?.other.end);
        period.value = quarter.value?.label;
        data = report.qaurterRequestParams();
    }

    const response = await report.requestCohort(data);
    if (response?.ok || response.httpStatusResponse === 204) {
        const interval = setInterval(async () => {
            data.regenerate = false;
            const res = await report.requestCohort(data);
            if (res?.httpStatusResponse === 200) {
                const cohortData = res.data;
                cohort.value = cohortData.values;
                indicators.value = toIndicators(cohortData.values);
                loader.hide();
                clearInterval(interval);
                componentKey.value++;
            }
        }, 3000);
    }
}

function printSpec() {
    const printW = open("", "", "width:1024px, height:768px");
    const content = document.getElementById("report-content");
    if (content && printW) {
        printW.document.write(`
        <html>
          <head>
            <title>Print Cohort</title>
            <link rel="stylesheet" media="print" href="/assets/css/cohort.css" />
          </head>
          <body>
            ${content.innerHTML}
          </body>
        </html>
      `);
        setTimeout(() => {
            printW.print();
            printW.close();
        }, 3500);
    }
}

function toCSV() {
    const columns = [
        { label: "Indicator", path: "indicator" },
        { label: "Value", path: "value" },
    ];
    const rows = Object.entries(indicators.value).map(([indicator, value]) => ({
        indicator,
        value,
    }));
    const filename = `MOH ${useFacility().facilityName} cohort report ${period.value}`;
    exportToCSV({ columns, rows, filename });
}
</script>

<style>
.box {
    border-color: #a3a3a3;
    border-width: thin;
    border-style: solid;
    border-radius: 3px;
    font-size: large;
    height: 44px;
}

select {
    background-color: white;
    border: none;
}
</style>
