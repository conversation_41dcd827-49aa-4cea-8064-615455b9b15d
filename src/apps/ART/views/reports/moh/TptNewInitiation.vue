<template>
  <report-table
    title="TPT New Initiation Report"
    report-type="MoH"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    :custom-drill-columns="drillColumns"
    :drill-row-parser="drillRowParser"
    useDateRangeFilter
    showIndices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { TPT_INITIATION_INDICATORS, TptInitiationPatientData, TptReportService } from "@/services/reports/tpt_report_service";
import { toDisplayFmt } from "@/utils/his_date";
import { toIndicatorColumns } from "@/utils/datatable";

const period = ref("-");
const rows = ref<any[]>([]);
const report = new TptReportService();
const columns: TableColumnInterface[] = [
  { path: "location", label: "District" },
  { path: "ageGroup", label: "Age group" },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  ...toIndicatorColumns(TPT_INITIATION_INDICATORS),
]

const drillColumns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "dispensation_date", label: "Dispensation Date", formatter: toDisplayFmt },
  { path: "art_start_date", label: "Art Start Date", formatter: toDisplayFmt },
  { path: "tpt_start_date", label: "TPT Start Date", formatter: toDisplayFmt }
]

async function fetchData({dateRange}: Record<string, any>) {
  try {
    await loader.show();
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    const data = await report.getTptNewInitiations();
    const location = data.F.rows[0].location;
    rows.value = [
      ...data.F.rows,
      ...data.M.rows,
      { ageGroup: 'All', gender: 'Male', ...data.M.aggregate, location },
      ...(await report.buildMaternityAgreggateRows(data.F.aggregate)).map(row => ({
        ...row,
        location
      }))
    ];
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}


function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} | ${data.row.ageGroup} | ${toDisplayGenderFmt(data.row.gender)}s`
}

function drillRowParser(data: DrilldownData) {
  return (data.row[data.column.path] as Array<TptInitiationPatientData>).map(patient => ({
    ...patient,
    person_id: patient.patient_id
  }));
}
</script>