<template>
  <report-table
    title="MoH TX CURR MMD Report"
    subtitle="Clients that are alive and on treatment in the reporting period and the difference in days between their clinical dispensation visit and 
      next appointment / drug-runout date is: 3 months (1 - 89 days), 3-5 months (90-179 days), 6+ months (180 or more days)"
    report-type="MoH"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="getDrillTitle"
    useDateRangeFilter
    showIndices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { TX_MMD_INDICATORS, TxReportService } from "@/services/reports/tx_report_service";
import { toastWarning } from "@/utils/toasts";
import { toDisplayGenderFmt } from "@/utils/common";
import { toIndicatorColumns } from "@/utils/datatable";

const period = ref("-");
const rows = ref<Array<any>>([]);
const report = new TxReportService();
const columns: TableColumnInterface[] = [
  { path: "ageGroup", label: "Age group", thStyles: { minWidth: "150px !important" }},
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt, thStyles: { minWidth: "110px !important" }},
  ...toIndicatorColumns(TX_MMD_INDICATORS, { thStyles: { minWidth: "350px !important" }}),
];

async function fetchData({dateRange}: Record<string, any>, rebuild: boolean) {
  try {
    await loader.show()
    report.setReportType('moh')
    report.setStartDate(dateRange.startDate);
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.getTxCurrMMDReport(rebuild);
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function getDrillTitle(data: DrilldownData) {
  return `${data.row.ageGroup} | ${data.column.label} | ${data.row.gender}s`
}
</script>