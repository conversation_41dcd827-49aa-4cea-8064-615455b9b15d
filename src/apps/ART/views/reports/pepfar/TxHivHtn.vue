<template>
  <report-table
    title="PEPFAR TX HIV HTN Report"
    report-type="PEPFAR"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    useDateRangeFilter
    showIndices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt } from "@/utils/common";
import { TX_HIV_HTN_INDICATORS, TxReportService,  } from "@/services/reports/tx_report_service";
import { toastWarning } from "@/utils/toasts";
import { toIndicatorColumns } from "@/utils/datatable";

const period = ref("-");
const rows = ref<Array<any>>([]);
const columns: TableColumnInterface[] = [
  { path: "ageGroup", label: "Age group" },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  ...toIndicatorColumns(TX_HIV_HTN_INDICATORS),
]

async function fetchData({dateRange}: Record<string, any>, rebuild: boolean = false) {
  try {
    await loader.show()
    const report = new TxReportService();
    report.setStartDate(dateRange.startDate);
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.getTxHivHtnReport(rebuild);
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} | ${data.row.ageGroup} | ${toDisplayGenderFmt(data.row.gender)}s`
}
</script>