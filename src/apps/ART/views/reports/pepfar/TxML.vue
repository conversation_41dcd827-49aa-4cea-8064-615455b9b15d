<template>
  <report-table
    title="PEPFAR TX ML Report"
    subtitle="Clients that were Alive and on treatment before the reporting period and 
    their “next appointment date / drug runout” date falls within the reporting period. 
    30 or more days have gone between their appointment date and the end of the 
    reporting period without any clinical dispensation visit"
    report-type="PEPFAR"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    useDateRangeFilter
    showIndices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt } from "@/utils/common";
import { TxReportService, TX_ML_INDICATORS } from "@/services/reports/tx_report_service";
import { toastWarning } from "@/utils/toasts";
import { toIndicatorColumns } from "@/utils/datatable";

const period = ref("-");
const rows = ref<Array<any>>([]);
const columns: TableColumnInterface[] = [
  { path: "ageGroup", label: "Age group" },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  ...toIndicatorColumns(TX_ML_INDICATORS),
]

async function fetchData({dateRange}: Record<string, any>, rebuild: boolean = false) {
  try {
    await loader.show()
    const report = new TxReportService();
    report.setStartDate(dateRange.startDate);
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.getTxMlReport(rebuild);
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} | ${data.row.ageGroup} | ${toDisplayGenderFmt(data.row.gender)}s`
}
</script>