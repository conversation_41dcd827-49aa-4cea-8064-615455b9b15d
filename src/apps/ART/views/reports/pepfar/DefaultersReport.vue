<template>
  <report-table
    :title="title"
    report-type="PEPFAR"
    :columns="columns"
    :row-action-buttons="rowActionBtns"
    :rows="rows"
    :period="period"
    use-date-range-filter
    use-secure-export
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt, parseARVNumber } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { DefaulterReportService } from "@/services/reports/defaulter_report_service";
import { toastWarning } from "@/utils/toasts";
import { getSelectButton } from "@/utils/datatable";

const period = ref("-");
const rows = ref<Array<any>>([]);
const title = ref("PEPFAR Defaulters List Report");
const rowActionBtns = [getSelectButton()];

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "given_name", label: "First name", exportable: false },
  { path: "family_name", label: "Last name", exportable: false },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
  { path: "age", label: "Age (At reporting)"},
  { path: "defaulter_date", label: "Defaulted Date", formatter: toDisplayFmt }
]

async function fetchData({ dateRange }: Record<string, any>) {
  await loader.show()
  const report = new DefaulterReportService()
  report.setStartDate(dateRange.startDate)
  report.setEndDate(dateRange.endDate)
  period.value = report.getDateIntervalPeriod()
  try {
    rows.value = await report.getDefaulters()
    title.value = `PEPFAR Defaulters List Report <b>(${rows.value.length} Defaulters)</b>`
  } catch (error) {
    toastWarning("Unable to load report data");
  }
  await loader.hide();
}

</script>