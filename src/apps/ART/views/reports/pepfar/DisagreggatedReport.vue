<template>
  <report-table
    title="PEPFAR Disaggregated Report"
    report-type="PEPFAR"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="getDrillTitle"
    useDateRangeFilter
    showIndices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { DisaggregatedReportService, DisagReportData } from "@/services/reports/disagregated_report_service";
import {  REGIMENS } from "@/constants";
import { toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";

const period = ref("-");
const rows = ref<Array<DisagReportData>>([])
    
const columns: TableColumnInterface[] = [
  { path: "age_group", label: "Age Group" },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "tx_curr", label: "TX curr (receiving ART)", drillable: true },
  ...REGIMENS.map(r => ({ path: r, label: r, drillable: true })),
  { path: "unknown", label: "Unknown", drillable: true },
  { path: "total", label: "Total", drillable: true },
]

const fetchData =  async ({ dateRange }: any) => {
  try {
    await loader.show();
    const report = new DisaggregatedReportService()
    report.setStartDate(dateRange.startDate);
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.getDisaggReport(true, "pepfar");
  } catch (error) {
    console.error(error);
    toastWarning("Error! Unable to generate report")
  }
  loader.hide();
}

function getDrillTitle(data: DrilldownData) {
  return `${data.row.age_group} | ${data.column.label} | ${toDisplayGenderFmt(data.row.gender)}s`
}
</script>