<template>
  <report-table
    report-type="Clinic"
    :title="title"
    :columns="columns"
    :rows="rows"
    :period="period"
    :row-action-buttons="rowActionBtns"
    :custom-filters="customFilters"
    use-date-range-filter
    use-secure-export
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { CustomFilterInterface,  TableColumnInterface } from "@uniquedj95/vtable";
import { toastWarning } from "@/utils/toasts";
import { parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { getSelectButton } from "@/utils/datatable";
import { ProgramReportService } from "@/services/reports/program_report_service";
import { strsToOptions } from "@/utils/Arrays";

const period = ref("-");
const outcome = ref("");
const rows = ref<any[]>([]);
const title = computed(() => `${outcome.value || "Other Outcomes"} Clinic Report`);
const rowActionBtns = [getSelectButton('patient_id')];

const columns = computed(() => {
  const _columns: Array<TableColumnInterface> = [
    { path: "identifier", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
    { path: "given_name", label: "First Name" },
    { path: "family_name", label: "Last Name" },
    { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
    { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
    { path: "outcome_date", label: "Outcome Date", formatter: toDisplayFmt },
  ];

  if(/transfer/i.test(outcome.value)) _columns.push({ 
    path: "transferred_out_to",
    label: "TO Location"
  });

  return _columns;
});

const customFilters: CustomFilterInterface[] = [{
  id: "outcome",
  label: "Select Outcome",
  type: "select",
  options: strsToOptions([
    "Transfer Out", 
    "Died", 
    "Stopped"
  ]),
}]

async function fetchData(filters: Record<string, any>) {
  await loader.show();
  try {
    const report = new ProgramReportService();
    outcome.value = filters.outcome.value
    report.setStartDate(filters.dateRange.startDate);
    report.setEndDate(filters.dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.getReport('patient_outcome_list', { 
      outcome: outcome.value 
    }) ?? [];
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>

