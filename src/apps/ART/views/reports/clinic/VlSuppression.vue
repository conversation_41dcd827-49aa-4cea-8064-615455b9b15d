<template>
  <report-table
    title="Clinic VL Suppression Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    useDateRangeFilter
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { ProgramReportService } from "@/services/reports/program_report_service";
import { toastWarning } from "@/utils/toasts";

const period = ref("-");
const rows = ref<any[]>([]);
const columns: TableColumnInterface[] = [
  { path: "regimen", label: "Regimen" },
  { path: "due_for_vl", label: "Due for VL", drillable: true },
  { path: "drawn", label: "Sample drawn", drillable: true },
  { path: "high_vl", label: "High vl (>=1000 copies)", drillable: true },
  { path: "low_vl", label: "Low vl (<1000 cpies)", drillable: true },
]

async function fetchData({ dateRange }: Record<string, any>) {
  try {
    await loader.show();
    const report = new ProgramReportService();
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.generate("vl_supression", {
      system_type: "emastercard"
    });
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} on ${data.row.regimen} regimen`
}
</script>