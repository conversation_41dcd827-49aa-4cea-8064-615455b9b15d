<template>
  <report-table
    title="TPT Outcomes Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    use-date-range-filter
    show-indices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { TPT_OUTCOMES_INDICATORS, TptReportService } from "@/services/reports/tpt_report_service";

const period = ref("-");
const rows = ref<any[]>([]);
const columns: TableColumnInterface[] = [
  { path: "age_group", label: "Age Group" },
  { path: "tpt_type", label: "TPT Type" },
  ...Object.entries(TPT_OUTCOMES_INDICATORS).map(([path, label]: any) => ({
    path, 
    label,  
    drillable: true, 
    sortable: false 
  })),
]

async function fetchData({ dateRange }: Record<string, any>) {
  try {
    await loader.show();
    const report = new TptReportService();
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value  = await report.getTtpOutcomes();
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} | ${data.row.age_group} | ${toDisplayGenderFmt(data.row.tpt_type)}`
}
</script>