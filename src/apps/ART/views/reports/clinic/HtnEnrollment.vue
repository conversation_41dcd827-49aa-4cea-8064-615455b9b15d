<template>
  <report-table
    title="HTN Enrollment Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    use-date-range-filter
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { ProgramReportService } from "@/services/reports/program_report_service";

const period = ref("-");
const rows = ref<any[]>([]);
const columns: TableColumnInterface[] = [
  { path: "index", label: "#" },
  { path: "indicator", label: "Indicator", tdStyles: {textAlign: "left"}},
  { path: "reporting_period", label: "reporting Period", drillable: true },
  { path: "cummulative", label: "Cumulatively", drillable: true },
]

const enrollmentIndicators = {
  registered_with_hypertension: "Patients registered with hypertension",
  enrolled_and_active_in_care: "HTN Patients enrolled and active in care",
  who_have_defaulted_during_the_reporting_period: "HTN patients who have defaulted during the reporting period",
  who_have_died: "HTN patients who have died",
  who_have_transferred_out: "HTN patients who have transferred out",
  who_have_stopped_htn_care: "HTN patients who have stopped HTN care",
  with_a_visit_in_last_3_months: "HTN patients with a visit in the last 3 months",
  with_a_visit_in_last_3_months_who_have_a_bp_measurement_recorded: "HTN patients with a visit in the last 3 months who have a BP measurement recorded",
  with_a_visit_in_last_3_months_who_have_bp_below_140_90: "HTN patients with a visit in the last 3 months who have BP below 140/90",
}

const treatmentDrugClassification = {
  diuretics: "Diuretics",
  beta_blockers: "Beta Blockers",
  calcium_channel_blockers: "Calcium Channel Blockers",
  angiotensin2_receptor_blockers: "Angiotensin 2 Receptor Blockers (ARBs)",
  vasodilators: "Vasodilators",
  others: "Others",
}


async function fetchData({ dateRange }: Record<string, any>) {
  try {
    await loader.show();
    const report = new ProgramReportService();
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    const data  = await report.generate('HTN_ENROLLMENT');
    rows.value = buildReportRows(data);
    console.log(rows.value);
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function buildReportRows(data: Record<string, any>) {
  const rows: any[] = [{
    index: undefined,
    indicator: "HYPERTENSION (HTN) ENROLLMENT",
    reporting_period: undefined,
    cummulative: undefined,
  }]

  Object.entries(enrollmentIndicators).forEach(([key, value]: any, index) => {
    rows.push({
      index: index + 1,
      indicator: value,
      ...data['htn_enrollment'][key],
    });
  });

  rows.push({
    index: undefined,
    indicator: "TREATMENT BASED ON HTN DRUG CLASSIFICATION",
    reporting_period: undefined,
    cummulative: undefined,
  });

  Object.entries(treatmentDrugClassification).forEach(([key, value]: any, index) => {
    rows.push({
      index: index + 1,
      indicator: value,
      ...data.treatment_drug_classification[key],
    });
  });

  return rows
}

function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} | ${data.row.age_group} | ${toDisplayGenderFmt(data.row.tpt_type)}`
}
</script>