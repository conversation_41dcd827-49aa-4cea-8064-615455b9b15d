<template>
  <report-table
    title="Incomplete Visits Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    use-date-range-filter
    use-secure-export
    show-indices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toastWarning } from "@/utils/toasts";
import { toDisplayGenderFmt } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { IncompleteVisitsReportService } from "@/services/reports/incomplete_visits_report_service";

const period = ref("-");
const rows = ref<any[]>([]);

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number"},
  { path: "national_id", label: "NHID" },
  { path: "given_name", label: "First name", exportable: false },
  { path: "family_name", label: "Last name", exportable: false },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
  { path: "dates", label: "Date(s)", formatter: (dates: string[]) => dates.map(toDisplayFmt).join(', ') }
]

async function fetchData({ dateRange }: Record<string, any>) {
  await loader.show();
  try {
    const report = new IncompleteVisitsReportService()
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate)
    period.value = report.getDateIntervalPeriod()
    rows.value = await report.generate();
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>