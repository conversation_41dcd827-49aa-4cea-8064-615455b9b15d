<template>
  <report-table
    title="Regimen Formulation: Patient Level Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    :row-action-buttons="rowActionBtns"
    :custom-filters="customFilters"
    use-date-range-filter
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { CustomFilterInterface,  TableColumnInterface } from "@uniquedj95/vtable";
import { toastWarning } from "@/utils/toasts";
import { parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { RegimenReportService } from "@/services/reports/regimen_report_service";
import { REGIMENS, REGIMEN_FORMULATIONS } from "@/constants";
import { strsToOptions } from "@/utils/Arrays";
import { getSelectButton } from "@/utils/datatable";

const period = ref("-");
const rows = ref<any[]>([]);
const rowActionBtns = [getSelectButton('patient_id')];

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
  { path: "regimen", label: "Reg. name" },
  { path: "drugs", label: "Formulation" },
]

const customFilters  = computed<CustomFilterInterface[]>(() => [
  { id: "regimen", label: "Select Regimen", type: "select",  options: strsToOptions(REGIMENS) },
  { id: "formulation", label: "Formulation", type: "select", options: strsToOptions(REGIMEN_FORMULATIONS) },
])

async function fetchData(filters: Record<string, any>) {
  await loader.show();
  try {
    const report = new RegimenReportService();
    report.setStartDate(filters.dateRange.startDate);
    report.setEndDate(filters.dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value = await report.getRegimenFormulationReport(filters.regimen.value, filters.formulation.value);
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>