<template>
  <report-table
    title="Clients Due For Viral Load Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :row-action-buttons="rowActionBtns"
    :rows="rows"
    :period="period"
    use-date-range-filter
    use-secure-export
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader"; 
import ReportTable, { FilterData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt, parseARVNumber } from "@/utils/common";
import { getAge, toDisplayFmt } from "@/utils/his_date";
import { toastWarning } from "@/utils/toasts";
import { ReportService } from "@/services/reports/report_service";
import { getSelectButton } from "@/utils/datatable";

const period = ref("");
const rows = ref<any[]>([]);
const rowActionBtns = [getSelectButton()];

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV #", preSort: parseARVNumber, initialSort: true },
  { path: "given_name", label: "First name", exportable: false },
  { path: "family_name", label: "Last name", exportable: false },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "Age", formatter: (date) => date ? getAge(date) : "" },
  { path: "appointment_date", label: "App.", formatter: toDisplayFmt },
  { path: "start_date", label: "Art started", formatter: toDisplayFmt },
  { path: "months_on_art", label: "Months on ART" },
  { path: "mile_stone", label: "Milestone", formatter: toDisplayFmt },
  { path: "last_result_order_date", label: "Ordered", formatter: toDisplayFmt },
  { path: "last_result", label: "Result" },
  { path: "last_result_date", label: "Released", formatter: toDisplayFmt },
]

async function fetchData({ dateRange }: FilterData) {
  try {
    await loader.show()
    const report = new ReportService()
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate)
    period.value = report.getDateIntervalPeriod()
    rows.value = await report.getReport('clients_due_vl') ?? [];
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>