<template>
  <report-table
    title="Clinic Missed Appointments Report"
    report-type="Clinic"
    :columns="columns"
    :row-action-buttons="rowActionBtns"
    :rows="rows"
    :period="period"
    use-date-range-filter
    use-secure-export
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader"; 
import ReportTable, { FilterData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt, parseARVNumber } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { toastWarning } from "@/utils/toasts";
import { Gender, ReportService } from "@/services/reports/report_service";
import { getSelectButton } from "@/utils/datatable";

interface PatientData {
  "given_name": string;
  "family_name": string;
  "birthdate": string;
  "gender": Gender;
  "cell_number": string;
  "district": string;
  "ta": string;
  "village": string;
  "arv_number": string;
  "appointment_date": string;
  "days_missed": number;
  "current_outcome": string;
  "person_id": number;
}

const period = ref("");
const rows = ref<any[]>([]);
const rowActionBtns = [getSelectButton()];

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "given_name", label: "First name", exportable: false },
  { path: "family_name", label: "Last name", exportable: false },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "DOB", formatter: toDisplayFmt },
  { path: "appointment_date", label: "Appointment", formatter: toDisplayFmt },
  { path: "days_missed", label: "Days Missed" },
  { path: "current_outcome", label: "Current Outcome" },
  { path: "address", label: "Contact Details", exportable: false }
]

async function fetchData({ dateRange }: FilterData) {
  try {
    await loader.show()
    const report = new ReportService()
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate)
    period.value = report.getDateIntervalPeriod()
    const data = await report.getReport<Array<PatientData>>('missed_appointments') ?? [];
    rows.value = data.map(d => ({
      ...d,
      address: `CELL: ${d.cell_number}
                District: ${d.district}
                Village: ${d.village}
                TA: ${d.ta}`
    }))
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>