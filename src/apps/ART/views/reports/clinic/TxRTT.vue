<template>
    <report-table
        title="Clinic TX RTT Report"
        report-type="Clinic"
        :columns="columns"
        :rows="rows"
        :period="period"
        :total-clients="totalClients"
        :drill-title="drilldownTitleBuilder"
        useDateRangeFilter
        showIndices
        @generate="fetchData"
    />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt } from "@/utils/common";
import { TxReportService } from "@/services/reports/tx_report_service";
import { toastWarning } from "@/utils/toasts";
import { AgeGroup, AggregatedReportData, Gender } from "@/services/reports/report_service";
import { uniq, get } from "lodash";
import { AGE_GROUPS } from "@/constants";

type IndicatorData = Record<"lessThanThree" | "betweenThreeAndFive" | "overSix", Array<number>>;
type PatientData = Array<{
    months: number;
    patient_id: number;
}>;

interface RowData {
    gender: Gender;
    ageGroup: AgeGroup;
    lessThanThree: Array<number>;
    betweenThreeAndFive: Array<number>;
    overSix: Array<number>;
}

const period = ref("-");
const rows = ref<any[]>([]);
const totalClients = ref<Array<number>>();
const report = new TxReportService();
const columns: TableColumnInterface[] = [
    { path: "ageGroup", label: "Age group" },
    { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
    { path: "lessThanThree", label: "Returned <3 mo", drillable: true },
    { path: "betweenThreeAndFive", label: "Returned 3-5 mo", drillable: true },
    { path: "overSix", label: "Returned 6+ mo", drillable: true },
];

function buildRow(data: any, gender: Gender, ageGroup: AgeGroup, result: AggregatedReportData<RowData, IndicatorData>) {
    const row: RowData = { gender, ageGroup, lessThanThree: [], betweenThreeAndFive: [], overSix: [] };
    const patients: PatientData = get(data, `${ageGroup}.${gender}`, []);

    for (const { months, patient_id } of patients) {
        if (months < 3) row.lessThanThree.push(patient_id);
        else if (months >= 3 && months < 6) row.betweenThreeAndFive.push(patient_id);
        else row.overSix.push(patient_id);
    }

    result[gender].rows.push(row);
    result[gender].aggregate.lessThanThree.push(...row.lessThanThree);
    result[gender].aggregate.betweenThreeAndFive.push(...row.betweenThreeAndFive);
    result[gender].aggregate.overSix.push(...row.overSix);
}

function getDisaggregatedData(data: Array<any>) {
    const defaultData: AggregatedReportData<RowData, IndicatorData> = {
        M: { rows: [], aggregate: { lessThanThree: [], betweenThreeAndFive: [], overSix: [] } },
        F: { rows: [], aggregate: { lessThanThree: [], betweenThreeAndFive: [], overSix: [] } },
    };

    return AGE_GROUPS.reduce((result: AggregatedReportData, ageGroup: any) => {
        buildRow(data, "F", ageGroup, result);
        buildRow(data, "M", ageGroup, result);
        return result;
    }, defaultData);
}

function setTotals(data: AggregatedReportData<RowData, IndicatorData>) {
    const allFemales = Object.values(data.F.aggregate).flat(1) as Array<number>;
    const allMales = Object.values(data.M.aggregate).flat(1) as Array<number>;
    totalClients.value = uniq([...allFemales, ...allMales]) as Array<number>;
}

async function fetchData({ dateRange }: Record<string, any>, rebuild: boolean = false) {
    try {
        await loader.show();
        report.setStartDate(dateRange.startDate);
        report.setEndDate(dateRange.endDate);
        period.value = report.getDateIntervalPeriod();
        const data: any = await report.getClinicTxRtt(rebuild);
        const disaggregated = getDisaggregatedData(data);
        rows.value = [
            ...disaggregated.F.rows,
            ...disaggregated.M.rows,
            { ageGroup: "All", gender: "Male", ...disaggregated.M.aggregate },
            ...(await report.buildMaternityAgreggateRows(disaggregated.F.aggregate)),
        ];
        setTotals(disaggregated);
    } catch (error) {
        toastWarning("ERROR! Unable to load report data");
        console.error(error);
    }
    await loader.hide();
}

function drilldownTitleBuilder(data: DrilldownData) {
    return `${data.column.label} | ${data.row.ageGroup} | ${toDisplayGenderFmt(data.row.gender)}s`;
}
</script>
