<template>
  <report-table
    report-type="Clinic"
    :title="title"
    :columns="columns"
    :rows="rows"
    :period="period"
    :row-action-buttons="rowActionBtns"
    :custom-filters="customFilters"
    use-date-range-filter
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { CustomFilterInterface,  TableColumnInterface } from "@uniquedj95/vtable";
import { toastWarning } from "@/utils/toasts";
import { parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { getSelectButton } from "@/utils/datatable";
import { ProgramReportService } from "@/services/reports/program_report_service";

const period = ref("-");
const rows = ref<any[]>([]);
const title = ref("Clinic Viral Load Report");
const rowActionBtns = [getSelectButton('patient_id')];

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
  { path: "specimen", label: "Specimen" },
  { path: "order_date", label: "Ordered", formatter: toDisplayFmt },
  { path: "result_modifier", label: "Result", formatter: (_: any, row: any) => `${row.result_modifier || ''} ${row.result}` },
  { path: "result_date", label: "Released", formatter: toDisplayFmt },
  { path: "current_regimen", label: "Curr. Regimen" },
]

const customFilters: CustomFilterInterface[] = [{
  id: "type",
  label: "Select result type",
  type: "select",
  options: [
    { label: 'Viraemia 1000+',  value: 'viraemia-1000' },
    { label: 'Suppressed', value: 'suppressed' },
    { label: 'Low level viraemia', value: 'low-level-viraemia' }
  ],
}]

async function fetchData(filters: Record<string, any>) {
  await loader.show();
  try {
    const report = new ProgramReportService();
    const range = filters.type.value
    report.setStartDate(filters.dateRange.startDate);
    report.setEndDate(filters.dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    title.value = ` Clinic Viral Load (${range}) Report`
    rows.value = await report.generate('high_vl_patients', { range });
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>