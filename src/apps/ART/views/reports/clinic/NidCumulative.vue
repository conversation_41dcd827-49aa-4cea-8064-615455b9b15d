<template>
  <report-table
    title="National ID Cumulative Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    use-date-range-filter
    show-indices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { ProgramReportService } from "@/services/reports/program_report_service";

const period = ref("-");
const rows = ref<any[]>([]);
const columns: TableColumnInterface[] = [
  { path: "age_group", label: "Age Group" },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "tx_curr", label: 'Tx Curr', drillable: true },
  { path: "nid_clients", label: "Clients with NID", drillable: true },
  { path: "new_nid", label: "New NID registered", drillable: true },
]

async function fetchData({ dateRange }: Record<string, any>) {
  try {
    await loader.show();
    const report = new ProgramReportService();
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value  = await report.generate('nid_cumulative_report') ?? [];
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} | ${data.row.age_group} | ${toDisplayGenderFmt(data.row.gender)}s`
}
</script>