<template>
  <report-table
    title="National ID Utilization Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    :drill-title="drilldownTitleBuilder"
    use-date-range-filter
    show-indices
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { ReportService } from "@/services/reports/report_service";

const period = ref("-");
const rows = ref<any[]>([]);
const columns: TableColumnInterface[] = [
  { path: "age_group", label: "Age Group" },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "total_visits", label: "Total Clients visits", drillable: true },
  { path: "nid_clients", label: "Clients with NID", drillable: true },
  { path: "new_nid", label: "New NID registered", drillable: true },
]

async function fetchData({ dateRange }: Record<string, any>) {
  try {
    await loader.show();
    const report = new ReportService();
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate);
    period.value = report.getDateIntervalPeriod();
    rows.value  = await report.getReport('nid_utilization_report') ?? [];
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

function drilldownTitleBuilder (data: DrilldownData) {
  return `${data.column.label} | ${data.row.age_group} | ${toDisplayGenderFmt(data.row.gender)}s`
}
</script>