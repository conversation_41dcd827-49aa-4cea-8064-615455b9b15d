<template>
    <report-table
        report-type="Clinic"
        title="Lab Test Result(s) Report"
        :columns="columns"
        :rows="rows"
        :period="period"
        :row-action-buttons="rowActionBtns"
        :custom-filters="customFilters"
        use-date-range-filter
        use-secure-export
        @generate="fetchData"
    />
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { CustomFilterInterface, TableColumnInterface, Option } from "@uniquedj95/vtable";
import { toastWarning } from "@/utils/toasts";
import { isEmpty, parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { getSelectButton } from "@/utils/datatable";
import { LabResultRowData, LabReportService } from "@/services/reports/lab_report_service";

const period = ref("-");
const type = ref("");
const rowActionBtns = [getSelectButton("patient_id")];
const tests = ref({} as LabResultRowData);

const rows = computed(() => {
    if (isEmpty(tests.value)) return [];
    return type.value === "disaggregated" ? tests.value.disaggregatedData : tests.value.patientLevelData;
});

const columns = computed<TableColumnInterface[]>(() =>
    type.value === "disaggregated"
        ? [
              { path: "ageGroup", label: "Age Group" },
              { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
              { path: "viral_load", label: "HIV viral load", drillable: true },
          ]
        : [
              { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
              { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
              { path: "birthdate", label: "Birthdate", formatter: toDisplayFmt },
              { path: "order_date", label: "Ordered", formatter: toDisplayFmt },
              { path: "test", label: "Specimen" },
              { path: "test_name", label: "Test" },
              { path: "result", label: "Result" },
              { path: "result_date", label: "Released", formatter: toDisplayFmt },
          ]
);

const customFilters = computed<CustomFilterInterface[]>(() => [
    {
        id: "type",
        label: "Select report type",
        required: false,
        type: "select",
        options: [
            { label: "Disaggregated", value: "disaggregated" },
            { label: "Patient level", value: "patient_level" },
        ],
        onUpdate: (v: Option) => {
            type.value = v.value as string;
        },
    },
]);

async function fetchData(filters: Record<string, any>) {
    await loader.show();
    try {
        const report = new LabReportService();
        report.setStartDate(filters.dateRange.startDate);
        report.setEndDate(filters.dateRange.endDate);
        period.value = report.getDateIntervalPeriod();
        tests.value = await report.getLabResultReport();
    } catch (error) {
        toastWarning("Unable to load report data");
        console.error(error);
    }
    await loader.hide();
}
</script>
