<template>
  <report-table
    title="Clinic Regimen Switch Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    use-date-range-filter
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toastWarning } from "@/utils/toasts";
import { getAge, toDisplayFmt } from "@/utils/his_date";
import { RegimenReportService } from "@/services/reports/regimen_report_service";

const period = ref("-");
const rows = ref<any[]>([]);
const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "DOB (Age in Years)", formatter: toDobAndAge },
  { path: "art_start_date", label: "Start Date", formatter: toDisplayFmt },
  { path: "current_weight", label: "Weight (Kg)" },
  { path: "previous_regimen", label: "Prev Regimen" },
  { path: "current_regimen", label: "Curr Regimen" },
  { path: "medications", label: "ARVs" },
  { path: "dispensation_date", label: "Dispensation Date", formatter: toDisplayFmt },
]

function toDobAndAge(date: string) {
  return `${toDisplayFmt(date)} (${getAge(date)})`
}

async function fetchData({ dateRange }: Record<string, any>) {
  try {
    await loader.show();
    const report = new RegimenReportService()
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate)
    period.value = report.getDateIntervalPeriod()
    rows.value = await report.getRegimenSwitchReport()
  } catch (error) {
    toastWarning("ERROR! Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}
</script>