<template>
  <report-table
    title="Pregnant Patients Clinic Report"
    report-type="Clinic"
    :columns="columns"
    :rows="rows"
    :period="period"
    :row-action-buttons="rowActionBtns"
    use-date-range-filter
    use-secure-export
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toastWarning } from "@/utils/toasts";
import { parseARVNumber, toDisplayGenderFmt } from "@/utils/common";
import { toDisplayFmt } from "@/utils/his_date";
import { getSelectButton } from "@/utils/datatable";
import { ProgramReportService } from "@/services/reports/program_report_service";

const period = ref("-");
const rows = ref<any[]>([]);
const rowActionBtns = [getSelectButton('patient_id')];

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "given_name", label: "First name", exportable: false },
  { path: "family_name", label: "Last name", exportable: false },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
]

async function fetchData({ dateRange }: Record<string, any>) {
  await loader.show();
  try {
    const report = new ProgramReportService()
    report.setStartDate(dateRange.startDate)
    report.setEndDate(dateRange.endDate)
    period.value = report.getDateIntervalPeriod()
    rows.value = await report.generate('pregnant_patients')
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>