<template>
  <report-table
    title="Clinic Appointments Report"
    report-type="Clinic"
    :columns="columns"
    :row-action-buttons="rowActionBtns"
    :rows="rows"
    use-date-filter
    use-secure-export
    @generate="fetchData"
  />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { FilterData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { toDisplayGenderFmt, parseARVNumber } from "@/utils/common";
import { toDisplayFmt, toStandardFmt } from "@/utils/his_date";
import { toastWarning } from "@/utils/toasts";
import { ReportService } from "@/services/reports/report_service";
import { getSelectButton } from "@/utils/datatable";

const rows = ref<any[]>([]);
const rowActionBtns = [getSelectButton()];

const columns: TableColumnInterface[] = [
  { path: "arv_number", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
  { path: "given_name", label: "First name", exportable: false },
  { path: "family_name", label: "Last name", exportable: false },
  { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
  { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
]

async function fetchData({ date }: FilterData) {
  await loader.show()
  const report = new ReportService()
  try {
    rows.value = await report.getReport(`programs/${report.programId}/scheduled_appointments`, { 
      date: toStandardFmt(date)
    }) ?? [];
  } catch (error) {
    toastWarning("Unable to load report data");
    console.error(error);
  }
  await loader.hide();
}

</script>