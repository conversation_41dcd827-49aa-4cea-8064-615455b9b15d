<template>
    <ion-page>
        <Toolbar></Toolbar>
        <ion-split-pane style="margin-top:70px;" when="xs" contentId="main">
            <ion-menu contentId="main">
                <ion-content>
                    <ion-list v-if="availableTasks.length > 0">
                        <ion-item @click="goToStep(index)" lines="none" v-for="(step, index) in availableTasks"
                            :key="index" :disabled="!taskIsEnabled(step)" button>
                            <ion-icon v-if="taskIsComplete(step)" :icon="checkmarkCircleSharp"
                                style="padding:10px; color:green;" slot="start" />
                            <ion-icon v-else-if="!taskIsEnabled(step)" :icon="lockClosedOutline" style="padding:10px"
                                slot="start" />
                            <ion-icon v-else :icon="location" style="padding:10px; color:green;" slot="start" />
                            <b>{{ step.label }}</b>
                        </ion-item>
                    </ion-list>
                    <div v-else style="margin-top: 10%;" class="ion-padding ion-text-center">
                        <ion-icon style="font-size: 2rem;" :icon="clipboardOutline"></ion-icon>
                        <h4>You are not assigned to any activities</h4>
                    </div>
                </ion-content>
                <ion-footer>
                    <ion-toolbar color="light">
                        <ion-button @click="router.back()">
                            <ion-icon :icon="arrowBack" slot="start"></ion-icon>
                            Back to dashboard
                        </ion-button>
                    </ion-toolbar>
                </ion-footer>
            </ion-menu>
            <div class="ion-page" id="main">
                <div v-if="isLoadingnextWorkflowTask" class="placeholder-content">
                    <div style="margin: 0px auto;" class="ion-text-center">
                        <ion-spinner name="bubbles"></ion-spinner>
                        <p>
                        <h1>Please wait...</h1>
                        </p>
                    </div>
                </div>
                <div v-else-if="availableTasks[currentStep]?.component">
                    <ion-card>
                        <ion-card-content>
                            <keep-alive>
                                <component ref="formRef" :is="availableTasks[currentStep]?.component" />
                            </keep-alive>
                        </ion-card-content>
                    </ion-card>
                </div>
                <div v-else class="ion-padding ion-text-center" style="margin-top: 15%;">
                    <ion-icon style="color: green; font-size: 80px;" :icon="checkmarkCircleOutline"></ion-icon>
                    <h2>{{ nextWorkflowTaskStatus }}</h2>
                    <ion-button size="large" fill="clear" @click="router.back()">
                        <ion-icon slot="start" :icon="arrowBackOutline"></ion-icon>
                        Exit page
                    </ion-button>
                </div>
                <ion-footer>
                    <ion-toolbar color="light">
                        <ion-button v-if="canNavigateToNext()" @click="handleNext" color="primary" style="padding: 4px;"
                            slot="end">
                            Save and continue
                            <ion-icon style="padding: 4px;" :icon="arrowForwardOutline" slot="end"></ion-icon>
                        </ion-button>
                    </ion-toolbar>
                </ion-footer>
            </div>
        </ion-split-pane>
    </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from 'vue';
import {
    IonSplitPane,
    IonButton,
    IonFooter,
    IonToolbar,
    IonMenu,
    IonList,
    IonItem,
    IonCard,
    IonCardContent,
    IonSpinner,
    IonPage,
    IonContent,
    IonIcon
} from '@ionic/vue';
import {
    clipboardOutline,
    checkmarkCircleOutline,
    location,
    arrowForwardOutline,
    arrowBackOutline,
    lockClosedOutline,
    checkmarkCircleSharp,
    arrowBack
} from 'ionicons/icons';
import { useRouter } from 'vue-router';
import { toastDanger } from '@/utils/Alerts';
import Toolbar from "@/components/Toolbar.vue";
import { Service } from '@/services/service';
import { WorkflowService } from '@/services/workflow_service';
import { PatientService } from '@/services/patient_service';
import { EncounterService } from '@/services/encounter_service';
import { Encounter } from '@/interfaces/encounter';

interface Task {
    encounterTypeID?: number;
    otherWorkflowNames?: string[];
    userProp?: string;
    label: string;
    /**
     * Enable this if you're testing new workflow steps that are not yet available to users
     */
    enableForDebugging?: boolean;
    component: any;
}

/**
 * Add your components here for the workflow sequence. Just make sure your form component exposes the "onSubmit" function...
 * To expose the onSubmit function in your form component, add this line:
*  defineExpose({
*      onSubmit
*  })
 * 
*/
const workflowSteps: Task[] = [
    {
        label: "Emergency Refill",
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/EmergencyRefill.vue")),
    },
    {
        encounterTypeID: 5,
        userProp: "patient type",
        label: "Patient Type",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/ARTPatientType.vue")),
    },
    {
        label: "Filing Number Management",
        enableForDebugging: false,
        // Set to true if you wanna test things
        component: defineAsyncComponent(() => import("@/apps/ART/components/ARTRegistration/FilingNumberManagement.vue")),
    },
    {
        encounterTypeID: 9,
        userProp: "HIV first visits",
        // Set to true if you wanna test things
        enableForDebugging: false,
        label: "HIV Clinic Registration",
        component: defineAsyncComponent(() => import("@/apps/ART/components/ARTRegistration/HIVClinicRegistration.vue")),
    },
    {
        encounterTypeID: 51,
        userProp: "HIV reception visits",
        label: "Reception",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/Reception/Reception.vue")),
    },
    {
        encounterTypeID: 6,
        otherWorkflowNames: ['htn_vitals'],
        userProp: "vitals",
        label: "Vitals",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/Vitals.vue")),
    },
    {
        encounterTypeID: 52,
        userProp: "HIV staging visits",
        label: "Staging",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/Staging.vue")),
    },
    {
        encounterTypeID: 53,
        userProp: "HIV clinic consultations",
        label: "Consultation",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/Consultation.vue")),
    },
    {
        encounterTypeID: 68,
        userProp: "ART adherence",
        label: "ART Adherence",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/ARTAdherence.vue")),
    },
    {
        encounterTypeID: 25,
        userProp: "Prescriptions",
        label: "Prescription",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/Prescription.vue")),
    },
    {
        encounterTypeID: 54,
        userProp: "Drug Dispensations",
        label: "Dispense drugs",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/ArtDispensation.vue")),
    },
    {
        encounterTypeID: 7,
        userProp: "Manage Appointments",
        label: "Appointment",
        // Set to true if you wanna test things
        enableForDebugging: false,
        component: defineAsyncComponent(() => import("@/apps/ART/components/ARTAppointment.vue")),
    }
];

const patientService = new PatientService()
const router = useRouter();
const currentStep = ref(-1);
const formRef = ref<any>(null);
const availableTasks = ref<Task[]>([])
const completedTasks = ref<any[]>([])
const isLoadingnextWorkflowTask = ref(false)
const nextEncounterType = ref(-1)
const nextEncounterIdName = ref('')
const nextWorkflowTaskStatus = ref("")
const encounterDescription = ref("")

function taskIsComplete(task: Task) {
    return completedTasks.value.includes(task?.encounterTypeID ?? 0)
}

function taskIsEnabled(task: Task) {
    if (task.enableForDebugging) return true
    // Safety check for task
    if (!task) return false
    // Task is enabled if it's completed or if it's the current step (next task)
    return !isLoadingnextWorkflowTask.value && nextEncounterType.value === task?.encounterTypeID
        || (task?.otherWorkflowNames ?? []).includes(nextEncounterIdName.value)
}

function canNavigateToNext() {
    if (availableTasks.value.length === 0 || currentStep.value === availableTasks.value.length - 1) return false
    // Can navigate to next step if the next step is enabled
    const nextWorkflowTask = availableTasks.value[currentStep.value]
    return nextWorkflowTask && taskIsEnabled(nextWorkflowTask)
}

async function loadCompletedTasks() {
    const encounters = await EncounterService.getEncounters(patientService.getID(), { date: PatientService.getSessionDate() })
    completedTasks.value = encounters.map((encounter: Encounter) => encounter.encounter_type)
}

async function nextWorkflowTask() {
    encounterDescription.value = ""
    isLoadingnextWorkflowTask.value = true
    nextWorkflowTaskStatus.value = ""
    nextEncounterIdName.value = ""
    try {
        const task = await WorkflowService.nextTask(patientService.getID())
        if (!task) {
            nextWorkflowTaskStatus.value = "No workflows found"
            isLoadingnextWorkflowTask.value = false
            return
        }
        nextEncounterIdName.value = task.name
        nextEncounterType.value = task?.encounter_type_id ?? -1
        // Find index of next task
        const nextWorkflowTaskIndex = availableTasks.value.findIndex((t) => {
            return t?.encounterTypeID === nextEncounterType.value ||
                (t.otherWorkflowNames ?? []).includes(nextEncounterIdName.value)
        })
        // Sort available tasks by current step - put current step and subsequent steps first
        if (nextWorkflowTaskIndex !== -1) {
            encounterDescription.value = task.description
            const sortedTasks = [
                ...availableTasks.value.slice(nextWorkflowTaskIndex), // Current step and subsequent steps
                ...availableTasks.value.slice(0, nextWorkflowTaskIndex) // Previous steps
            ]
            availableTasks.value = sortedTasks
            currentStep.value = 0 // Current step is now at index 0
        } else {
            nextWorkflowTaskStatus.value = "Error loading workflow task"
            currentStep.value = nextWorkflowTaskIndex
        }
    } catch (e) {
        nextWorkflowTaskStatus.value = "An error has occured while loading next task: " + e
        console.error(e)
    } finally {
        await loadCompletedTasks()
        isLoadingnextWorkflowTask.value = false
    }
}

async function initTasks() {
    // Constrain tasks to user selected activities
    const props = await Service.getJson('user_properties', { property: "activities" })
    try {
        if (!props.property_value) {
            availableTasks.value = workflowSteps
            return
        }
        const userSelectedActivities = props.property_value.split(',').map((prop: string) => prop.toLowerCase())
        availableTasks.value = workflowSteps.filter((task) => task.enableForDebugging ||
            userSelectedActivities.includes(task.userProp?.toLowerCase()))
    } catch (e) {
        toastDanger("Unable to load activities")
        console.error(e)
    }
}

function submitForm() {
    if (formRef.value && typeof (formRef.value as any).onSubmit === 'function') {
        return (formRef.value as any).onSubmit()
    }
    return Promise.resolve(true)
}

// Handle navigation between steps
function handleNext() {
    submitForm().then((ok: boolean) => ok && nextWorkflowTask())
}

function goToStep(index: number) {
    // Safety checks
    if (index < 0 || index >= availableTasks.value.length) return
    const task = availableTasks.value[index]
    // Only allow navigation to enabled steps
    if (task && taskIsEnabled(task)) {
        currentStep.value = index;
    }
}

onMounted(() => initTasks().then(nextWorkflowTask));
</script>

<style scoped>
#main {
    height: calc(100vh - 69px);
    overflow-y: auto;
}

.placeholder-content {
    padding: 20px;
}
</style>
<style scoped>
#main {
    height: calc(100vh - 69px);
    overflow-y: auto;
}

.placeholder-content {
    padding: 20px;
}
</style>