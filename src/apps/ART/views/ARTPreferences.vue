<template>
    <ion-page>
        <Toolbar />
        <ion-content class="ion-padding">
            <ion-card style="margin:0px auto; width:70%;">
                <ion-card-content>
                    <ion-accordion-group @ion-change="(e) => handleAccordionChange(e.detail.value)">
                        <ion-accordion v-for="(config, index) in configurations" :key="index" class="ion-padding"
                            :value="config.title">
                            <ion-item slot="header">
                                <ion-icon size="large" :icon="config.icon"> </ion-icon>
                                <ion-label style="padding:10px;">{{ config.title }}</ion-label>
                                <ion-spinner v-if="configStates?.[config.title]?.isLoading ?? false" name="dots"
                                    size="small" slot="end" />
                            </ion-item>
                            <div class="ion-padding" slot="content">
                                <ion-card>
                                    <ion-card-content>
                                        <ion-list style="background: white;" v-if="configStates?.[config.title]?.data">
                                            <div v-for="option in config.options" :key="option.id">
                                                <div v-if="option.type === 'toggle'">
                                                    <ion-item color="light">
                                                        <ion-label>{{ option.title }}</ion-label>
                                                        <ion-toggle
                                                            @update:model-value="(value) => option.dataHandler(value, option.id, config.title)"
                                                            v-model="configStates[config.title].data[option.id]"
                                                            size="large" slot="end" :enable-on-off-labels="true" />
                                                    </ion-item>
                                                    <ion-note v-if="configStates[config.title]?.statusMessage"
                                                        color="danger">
                                                        {{ configStates[config.title]?.statusMessage }}
                                                    </ion-note>
                                                </div>
                                                <div v-if="option.type === 'input'">
                                                    <ion-input label-placement="stacked"
                                                        @ion-change="(e) => option.dataHandler(e.detail.value, option.id, config.title)"
                                                        v-model="configStates[config.title].data[option.id]"
                                                        style="margin-top: 20px;" :label="option.title" fill="outline"
                                                        placeholder="Enter value" slot="end"></ion-input>
                                                    <ion-note v-if="configStates[config.title]?.statusMessage"
                                                        color="danger">
                                                        {{ configStates[config.title]?.statusMessage }}
                                                    </ion-note>
                                                </div>
                                                <div v-if="option.type === 'clinic-days'">
                                                    <ion-item color="light">
                                                        <ion-label>{{ option.title }}</ion-label>
                                                    </ion-item>
                                                    <div class="ion-padding">
                                                        <ClinicDaysSelector
                                                            v-model="configStates[config.title].data[option.id]"
                                                            @change="(value) => option.dataHandler(value, option.id, config.title)" />
                                                        <ion-note v-if="configStates[config.title]?.statusMessage"
                                                            color="danger">
                                                            {{ configStates[config.title]?.statusMessage }}
                                                        </ion-note>
                                                    </div>
                                                </div>
                                                <div v-if="option.type === 'facility'">
                                                    <ion-item color="light">
                                                        <ion-label>{{ option.title }}</ion-label>
                                                        {{ configStates[config.title].data[option.id]?.label ??
                                                            configStates[config.title].data[option.id] }}
                                                    </ion-item>
                                                    <div class="ion-padding">
                                                        <VueMultiselect
                                                            v-model="configStates[config.title].data[option.id]"
                                                            @update:model-value="(value: any) => option.dataHandler(value, option.id, config.title)"
                                                            :multiple="false" :taggable="false" :close-on-select="true"
                                                            openDirection="bottom"
                                                            tag-placeholder="Find and select facility"
                                                            placeholder="Select target lab facility" selectLabel=""
                                                            label="label" :searchable="true"
                                                            :loading="isLabFacilitiesLoading"
                                                            @search-change="fetchLabFacilities" track-by="label"
                                                            :options="labFacilities" />
                                                        <ion-note v-if="configStates[config.title]?.statusMessage"
                                                            color="danger">
                                                            {{ configStates[config.title]?.statusMessage }}
                                                        </ion-note>
                                                    </div>
                                                </div>
                                            </div>
                                        </ion-list>

                                    </ion-card-content>
                                </ion-card>
                            </div>
                        </ion-accordion>
                    </ion-accordion-group>
                </ion-card-content>
            </ion-card>
        </ion-content>
    </ion-page>
</template>
<script lang="ts" setup>
import {
    IonCard,
    IonCardContent,
    IonNote,
    IonInput,
    IonToggle,
    IonList,
    IonSpinner,
    IonIcon,
    IonPage,
    IonItem,
    IonContent,
    IonAccordion,
    IonAccordionGroup,
    IonLabel
} from "@ionic/vue"
import Toolbar from "@/components/Toolbar.vue";
import {
    flask,
    calendar,
    heartCircle,
    pulse,
    woman,
    settings,
    fileTrayFull
} from "ionicons/icons";
import { ref } from "vue";
import { GlobalPropertyService } from "@/services/global_property_service";
import { GlobalPropName } from "@/constants/global_property_name";
import ClinicDaysSelector from "../components/ClinicDaysSelector.vue";
import VueMultiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.css";
import { getFacilities } from "@/utils/HisFormHelpers/LocationFieldOptions";

interface Setting {
    icon: string;
    title: string;
    onOpen?: () => Promise<any>;
    options: {
        id: string;
        title: string;
        type: 'toggle' | 'input' | 'clinic-days' | 'facility';
        get: (id: GlobalPropName) => any;
        dataHandler: (value: any, id: string, configTitle: string) => void;
    }[];
}

interface State {
    isLoading: boolean;
    data: any;
    statusMessage?: string;
}

const configStates = ref<Record<string, State>>({})
const labFacilities = ref<{ label: string; value: string }[]>([])
const isLabFacilitiesLoading = ref(false)

// Function to fetch lab facilities
const fetchLabFacilities = async (filter = '') => {
    isLabFacilitiesLoading.value = true;
    try {
        const facilities = await getFacilities(filter);
        labFacilities.value = facilities;
    } catch (error) {
    } finally {
        isLabFacilitiesLoading.value = false;
    }
}

// Reusable function for saving toggle properties
const saveToggleProperty = async (value: boolean, id: string, configTitle: string) => {
    if (configStates.value[configTitle]) {
        // Ensure the value is a boolean
        const boolValue = Boolean(value);

        // Update the UI state immediately
        configStates.value[configTitle].data[id] = boolValue;
        configStates.value[configTitle].isLoading = true;
        configStates.value[configTitle].statusMessage = undefined;

        try {
            // Save to backend
            await GlobalPropertyService.set(id, boolValue ? 'true' : 'false');
            console.log(`Successfully saved ${id} with value: ${boolValue}`);
        } catch (error) {
            console.error(`Error saving ${id}:`, error);
            configStates.value[configTitle].statusMessage = `Failed to save: ${error instanceof Error ? error.message : 'Unknown error'}`;
        } finally {
            configStates.value[configTitle].isLoading = false;
        }
    }
};

// Reusable function for saving input properties
const saveInputProperty = async (value: any, id: string, configTitle: string, validateNumeric = false) => {
    if (configStates.value[configTitle]) {
        configStates.value[configTitle].data[id] = value;
        configStates.value[configTitle].isLoading = true;
        configStates.value[configTitle].statusMessage = undefined;

        try {
            // Handle facility object
            let valueToSave = value;

            // If value is an object with a value property (from VueMultiselect)
            if (value && typeof value === 'object' && 'value' in value) {
                valueToSave = value.label;
            }

            // Validate numeric value if required
            if (validateNumeric && typeof valueToSave === 'string') {
                const numValue = Number(valueToSave);
                if (isNaN(numValue)) {
                    configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                    configStates.value[configTitle].isLoading = false;
                    return;
                }
            }

            await GlobalPropertyService.set(id, valueToSave);
        } catch (error) {
            console.error(`Error saving ${id}:`, error);
            configStates.value[configTitle].statusMessage = `Failed to save: ${error instanceof Error ? error.message : 'Unknown error'}`;
        } finally {
            configStates.value[configTitle].isLoading = false;
        }
    }
};

const configurations: Setting[] = [
    {
        icon: fileTrayFull,
        title: "Filing Numbers",
        options: [
            {
                id: GlobalPropName.FILING_NUMBERS,
                title: 'Enabled Filing Number',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.FILING_NUMBER_LIMIT,
                title: 'Filing Number Limit',
                type: 'input',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    // Validate filing number limit
                    const limitValue = Number(value);
                    if (isNaN(limitValue)) {
                        configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                        return;
                    }

                    if (limitValue < 1 || limitValue > 1000) {
                        configStates.value[configTitle].statusMessage = 'Filing number limit should be between 1 and 1000';
                        return;
                    }

                    // If validation passes, save the value
                    saveInputProperty(value, id, configTitle, true);
                }
            }
        ],
    },
    {
        icon: calendar,
        title: "Appointments",
        options: [
            {
                id: GlobalPropName.APPOINTMENT_LIMIT,
                title: 'Appointment Limit',
                type: 'input',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    // Validate appointment limit
                    const limitValue = Number(value);
                    if (isNaN(limitValue)) {
                        configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                        return;
                    }

                    if (limitValue < 1 || limitValue > 500) {
                        configStates.value[configTitle].statusMessage = 'Appointment limit should be between 1 and 500';
                        return;
                    }

                    // If validation passes, save the value
                    saveInputProperty(value, id, configTitle, true);
                }
            },
            {
                id: GlobalPropName.ADULT_CLINIC_DAYS,
                title: 'Clinic days (adults: 18 yrs and over)',
                type: 'clinic-days',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => saveInputProperty(value, id, configTitle, false)
            },
            {
                id: GlobalPropName.PEADS_CLINIC_DAYS,
                title: 'Clinic days (children: Under 18 yrs)',
                type: 'clinic-days',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => saveInputProperty(value, id, configTitle, false)
            }
        ]
    },
    {
        icon: settings,
        title: "Clinic Preferences",
        options: [
            {
                id: GlobalPropName.IS_DIC_SITE,
                title: 'Is Military Site',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            }
        ]
    },
    {
        icon: pulse,
        title: "Viral load Preferences",
        options: [
            {
                id: GlobalPropName.VL_ROUTINE_CHECK,
                title: 'Activate VL routine check',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.CAN_SCAN_DBS_BARCODE,
                title: 'Scan DBS barcode',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            }
        ]
    },
    {
        icon: flask,
        title: "Lab preferences",
        options: [
            {
                id: GlobalPropName.EXTENDED_LABS,
                title: 'Activate Extended Labs',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.TARGET_LAB,
                title: 'Target lab',
                type: 'facility',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => saveInputProperty(value, id, configTitle, false)
            },
            {
                id: GlobalPropName.LAB_ORDER_PRINT_COPIES,
                title: "Specify number of copies to print after an order",
                type: "input",
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    // Validate number of copies
                    const copiesValue = Number(value);
                    if (isNaN(copiesValue)) {
                        configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                        return;
                    }

                    if (copiesValue < 1 || copiesValue > 10) {
                        configStates.value[configTitle].statusMessage = 'Number of copies should be between 1 and 10';
                        return;
                    }

                    // If validation passes, save the value
                    saveInputProperty(value, id, configTitle, true);
                }
            }
        ]
    },
    {
        icon: heartCircle,
        title: "Hypertension Preferences",
        options: [
            {
                id: GlobalPropName.HTN_ENHANCEMENT,
                title: 'Activate Hypertension screening',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.HTN_DIASTOLIC_THRESHOLD,
                title: 'Set diastolic blood pressure minimum threshold',
                type: 'input',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    // Validate diastolic BP (normal range: 60-90 mmHg)
                    const diastolicValue = Number(value);
                    if (isNaN(diastolicValue)) {
                        configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                        return;
                    }

                    if (diastolicValue < 40 || diastolicValue > 120) {
                        configStates.value[configTitle].statusMessage = 'Diastolic BP should be between 40 and 120 mmHg';
                        return;
                    }

                    // If validation passes, save the value
                    saveInputProperty(value, id, configTitle, true);
                }
            },
            {
                id: GlobalPropName.HTN_SYSTOLIC_THRESHOLD,
                title: 'Set systolic blood pressure minimum threshold',
                type: 'input',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    // Validate systolic BP (normal range: 90-140 mmHg)
                    const systolicValue = Number(value);
                    if (isNaN(systolicValue)) {
                        configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                        return;
                    }

                    if (systolicValue < 70 || systolicValue > 220) {
                        configStates.value[configTitle].statusMessage = 'Systolic BP should be between 70 and 220 mmHg';
                        return;
                    }

                    // If validation passes, save the value
                    saveInputProperty(value, id, configTitle, true);
                }
            },
            {
                id: GlobalPropName.HTN_SCREENING_AGE_THRESHOLD,
                title: 'Set HTN Age',
                type: 'input',
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    // Validate age threshold
                    const ageValue = Number(value);
                    if (isNaN(ageValue)) {
                        configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                        return;
                    }

                    if (ageValue < 10 || ageValue > 100) {
                        configStates.value[configTitle].statusMessage = 'Age threshold should be between 10 and 100 years';
                        return;
                    }

                    // If validation passes, save the value
                    saveInputProperty(value, id, configTitle, true);
                }
            }
        ]
    },
    {
        icon: woman,
        title: "Cervical Cancer Preferences",
        onOpen: async () => {
            configStates.value['Cervical Cancer Preferences'].data = {
                [GlobalPropName.CERVICAL_CANCER_AGE_BOUNDS]: await GlobalPropertyService.get(GlobalPropName.CERVICAL_CANCER_AGE_BOUNDS) || '',
            }
            const [start, end] = (configStates.value['Cervical Cancer Preferences'].data[GlobalPropName.CERVICAL_CANCER_AGE_BOUNDS] ?? '0:0').split(':').map(Number);
            configStates.value['Cervical Cancer Preferences'].data.min_cervical_age = start;
            configStates.value['Cervical Cancer Preferences'].data.max_cervical_age = end;
        },
        options: [
            {
                id: GlobalPropName.CERVICAL_CANCER_SCREENING,
                title: 'Activate Cervical Cancer Screening',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: 'min_cervical_age',
                title: 'Starting screening age',
                type: 'input',
                get: () => configStates.value['Cervical Cancer Preferences'].data.min_cervical_age || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    const min = Number(value)
                    if (isNaN(min)) {
                        configStates.value[configTitle].statusMessage = 'Please enter valid min age';
                        return;
                    }
                    if (min < 10 || min > 100) {
                        configStates.value[configTitle].statusMessage = 'Age bounds should be between 10 and 100 years, with the minimum age being less than the maximum age';
                        return;
                    }
                    const max = configStates.value[configTitle].data.max_cervical_age;
                    if (max && min >= Number(max)) {
                        configStates.value[configTitle].statusMessage = 'Minimum age should be less than the maximum age';
                        return;
                    }
                    // If validation passes, save the value
                    saveInputProperty(`${min}:${max}`, GlobalPropName.CERVICAL_CANCER_AGE_BOUNDS, configTitle, false);
                }
            },
            {
                id: 'max_cervical_age',
                title: 'Maximum screening age',
                type: 'input',
                get: () => configStates.value['Cervical Cancer Preferences'].data.max_cervical_age || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    const max = Number(value)
                    if (isNaN(max)) {
                        configStates.value[configTitle].statusMessage = 'Please enter valid max age';
                        return;
                    }
                    if (max < 10 || max > 100) {
                        configStates.value[configTitle].statusMessage = 'Age bounds should be between 10 and 100 years, with the minimum age being less than the maximum age';
                        return;
                    }
                    const min = configStates.value[configTitle].data.min_cervical_age;
                    if (min && max < Number(min)) {
                        configStates.value[configTitle].statusMessage = 'Maximum age should be less than the minimum age';
                        return;
                    }
                    // If validation passes, save the value
                    saveInputProperty(`${min}:${max}`, GlobalPropName.CERVICAL_CANCER_AGE_BOUNDS, configTitle, false);
                }
            },
        ]
    },
    {
        icon: settings,
        title: "Other Preferences",
        options: [
            {
                id: GlobalPropName.PILLS_REMAINING,
                title: 'Ask pills remaining at home',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.FAST_TRACK,
                title: 'Activate fast track',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.THREE_HP_AUTO_SELECT,
                title: 'Activate 3HP auto select',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.EXCLUDE_EXTERNAL_AND_DRUG_REFILLS,
                title: '(DATA CLEANING) Exclude External and Emergency supply',
                type: 'toggle',
                get: (id) => GlobalPropertyService.isProp(`${id}=true`),
                dataHandler: (value: boolean, id: string, configTitle: string) => saveToggleProperty(value, id, configTitle)
            },
            {
                id: GlobalPropName.NOTIFICATION_PERIOD,
                title: "Set Auto Cleaning Alert Days",
                type: "input",
                get: (id) => GlobalPropertyService.get(id) || '',
                dataHandler: (value: string, id: string, configTitle: string) => {
                    // Validate notification period
                    const periodValue = Number(value);
                    if (isNaN(periodValue)) {
                        configStates.value[configTitle].statusMessage = 'Please enter a valid number';
                        return;
                    }

                    if (periodValue < 1 || periodValue > 365) {
                        configStates.value[configTitle].statusMessage = 'Notification period should be between 1 and 365 days';
                        return;
                    }

                    // If validation passes, save the value
                    saveInputProperty(value, id, configTitle, true);
                }
            }
        ]
    }
]

async function handleAccordionChange(option: string) {
    const config = configurations.find((config) => config.title === option);
    if (config) {
        // Initialize the config state if it doesn't exist
        if (!configStates.value[config.title]) {
            configStates.value[config.title] = {
                isLoading: false,
                data: {},
                statusMessage: undefined
            }
        }

        // Set loading state
        configStates.value[config.title].isLoading = true;
        if (config.onOpen) {
            await config.onOpen()
                .catch(error => {
                    console.error(`Error loading ${config.title} settings:`, error);
                    configStates.value[config.title].statusMessage = `Failed to load settings: ${error instanceof Error ? error.message : 'Unknown error'}`;
                })
        }
        const res = await Promise.all(
            config.options.map(async (option: any) => ({ [option.id]: await option.get(option.id) }))
        )
        configStates.value[config.title].data = {
            ...configStates.value[config.title].data,
            ...res.reduce((acc, curr) => ({ ...acc, ...curr }), {})
        }
        configStates.value[config.title].isLoading = false
    }
}
</script>