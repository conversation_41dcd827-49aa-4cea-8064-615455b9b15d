import { AppEncounterService } from "@/services/app_encounter_service";
import HISDate from "@/utils/Date";
import { computed, Ref, ref, watch } from "vue";

export type TBTherapyStatus = "Yes" | "No" | "Unknown";
export type EstimatePeriod = "Months" | "Weeks" | "Days";

export interface ExistingTBTreatment {
    history: string;
    status: string;
    methods: Record<string, string>;
    symptoms: Record<string, boolean>;
    encounterService: AppEncounterService;
}

interface TBTherapyModule {
    submitTBTherapy: Ref<boolean>;
    tbTherapyStatus: Ref<TBTherapyStatus>;
    tbTreatmentStartDate: Ref<{ day: string; month: string; year: string } | undefined>;
    tbTreatmentPeriod: Ref<string>;
    showEstimator: Ref<boolean>;
    onTBTreatmentOptions: Ref<{ label: string; value: TBTherapyStatus }[]>;
    tbStatusOptions: Ref<string[]>;
    tbEstimatePeriods: Ref<EstimatePeriod[]>;
    tbStartEstimate: Ref<string>;
    selectedEstimatePeriod: Ref<{ name: EstimatePeriod }>;
    tptHistoryOptions: Ref<string[]>;
    startDateEstimator: (value: number, period: EstimatePeriod) => { day: string; month: string; year: string };
    showTBStatus: (methods: Record<string, string>, symptoms: Record<string, boolean>) => boolean;
    buildTBTreatmentPayload: (
        patientID: number,
        encounterTypeID: number,
        providerID: number,
        locationID: string,
        existingTBTreatment: ExistingTBTreatment
    ) => Promise<any[]>;
}

const startDateEstimator = (value: number, period: EstimatePeriod): { day: string; month: string; year: string } => {
    const date = new Date();

    const adjusters: Record<EstimatePeriod, () => void> = {
        Months: () => date.setMonth(date.getMonth() - value),
        Weeks: () => date.setDate(date.getDate() - value * 7),
        Days: () => date.setDate(date.getDate() - value),
    };

    adjusters[period]();

    return {
        day: date.getDate().toString().padStart(2, "0"),
        month: (date.getMonth() + 1).toString().padStart(2, "0"),
        year: date.getFullYear().toString(),
    };
};

export const useTBTherapy = (): TBTherapyModule => {
    const tbTherapyStatus = ref<TBTherapyStatus>("Unknown");
    const tbTreatmentStartDate = ref<{ day: string; month: string; year: string }>();
    const tbTreatmentPeriod = ref<string>("3");
    const submitTBTherapy = ref<boolean>(false);
    const showEstimator = ref<boolean>(false);
    const tbEstimatePeriods = ref<EstimatePeriod[]>(["Months", "Weeks", "Days"]);
    const tbStartEstimate = ref<string>("0");
    const selectedEstimatePeriod = ref<{ name: EstimatePeriod }>({ name: "Months" });
    const onTBTreatmentOptions = ref<{ label: string; value: TBTherapyStatus }[]>([
        { label: "Yes", value: "Yes" },
        { label: "No", value: "No" },
        { label: "Unknown", value: "Unknown" },
    ]);
    const tbStatusOptions = ref(["TB NOT suspected", "TB Suspected", "Confirmed TB Not on treatment"]);
    const tptHistoryOptions = computed(() => {
        let options: string[] = [];
        if (/no/i.test(tbTherapyStatus.value.toLowerCase())) {
            options = ["Currently on IPT", "Currently on 3HP (RFP + INH)", "Currently on INH 300 / RFP 300 (3HP)"];
        }
        options = options.concat([
            "Complete course of 3HP in the past (3 months RFP+INH)",
            "Complete course of IPT in the past (min. 6 months of INH)",
            "Aborted course of 3HP (RFP + INH) in the past",
            "Aborted course of INH 300 / RFP 300 (3HP) in the past",
            "Aborted course of IPT in the past",
            "Never taken IPT or 3HP",
        ]);
        return options;
    });

    watch([tbStartEstimate, selectedEstimatePeriod], ([newEstimate, newPeriod]) => {
        if (newEstimate || newPeriod) {
            const estimatedDate = startDateEstimator(Number(newEstimate), selectedEstimatePeriod.value.name);
            tbTreatmentStartDate.value = estimatedDate;
        }
    });

    const buildNewTBTreament = async (encounterService: AppEncounterService): Promise<any[]> => {
        const startDate = `${tbTreatmentStartDate.value?.year}-${tbTreatmentStartDate.value?.month}-${tbTreatmentStartDate.value?.day}`;
        const treatmentStartDate = showEstimator.value
            ? await encounterService.buildValueDateEstimated("TB treatment start date", HISDate.toStandardHisFormat(startDate))
            : await encounterService.buildValueDate("TB treatment start date", HISDate.toStandardHisFormat(startDate));
        const treatmentPeriod = await encounterService.buildValueNumber("TB treatment period", Number(tbTreatmentPeriod.value));
        return [treatmentStartDate, treatmentPeriod];
    };

    const isRapidTestPositive = (methods: Record<string, string>): boolean => {
        const CXR = "Chest X-ray";
        const mWRD = "Molecular WHO Recommended Rapid Diagnostic Test";

        if (methods[mWRD].toLowerCase() === "normal") {
            return false;
        }

        return methods[CXR].toLowerCase() === "abnormal" || methods[mWRD].toLowerCase() === "positive";
    };

    const hasTBSymptoms = (symptoms: Record<string, boolean>): boolean => {
        return Object.values(symptoms).some((value) => value);
    };

    const showTBStatus = (methods: Record<string, string>, symptoms: Record<string, boolean>): boolean => {
        if (Object.values(methods).every((value) => value.toLowerCase() === "unknown")) return hasTBSymptoms(symptoms);
        return isRapidTestPositive(methods);
    };

    const buildTBSymptoms = async (symptoms: Record<string, boolean>, encounterService: AppEncounterService): Promise<any[]> => {
        const symptomsPromises = Object.entries(symptoms).map(async ([key, value]) => ({
            ...(await encounterService.buildValueCoded("Routine TB Screening", key)),
            child: [await encounterService.buildValueCoded(key, value ? "Yes" : "No")],
        }));
        return Promise.all(symptomsPromises);
    };

    const buildMethodsUsed = async (methods: Record<string, string>, encounterService: AppEncounterService): Promise<any[]> => {
        const methodsPromises = Object.entries(methods).map(async ([key, value]) => ({
            ...(await encounterService.buildValueCoded("TB screening method used", key)),
            child: [await encounterService.buildValueCoded(key, value)],
        }));
        return Promise.all(methodsPromises);
    };

    const buildTBStatus = (
        status: string,
        methods: Record<string, string>,
        symptoms: Record<string, boolean>,
        encounterService: AppEncounterService
    ): Promise<any> => {
        if (tbTherapyStatus.value === "Yes") {
            return encounterService.buildValueCoded("TB Status", "Confirmed TB on treatment");
        }
        if (status) {
            return encounterService.buildValueCoded("TB Status", status);
        }
        if (methods) {
            if (isRapidTestPositive(methods)) {
                return encounterService.buildValueCoded("TB Status", "TB Suspected");
            } else {
                return encounterService.buildValueCoded("TB Status", "TB NOT suspected");
            }
        }
        if (symptoms && !hasTBSymptoms(symptoms)) {
            return encounterService.buildValueCoded("TB Status", "TB NOT suspected");
        }
        return Promise.resolve({});
    };

    const buildTBTreatmentHistory = (history: string, encounterService: AppEncounterService): Promise<any> => {
        return encounterService.buildValueText("TB Treatment History", history);
    };

    const buildExistingTBTreatment = async (
        history: string,
        status: string,
        methods: Record<string, string>,
        symptoms: Record<string, boolean>,
        encounterService: AppEncounterService
    ): Promise<any[]> => {
        const treatmentStatus = await buildTBStatus(status, methods, symptoms, encounterService);
        const treatmentHistory = await buildTBTreatmentHistory(history, encounterService);
        const treatmentMethods = await Promise.all(await buildMethodsUsed(methods, encounterService));
        const treatmentSymptoms = await Promise.all(await buildTBSymptoms(symptoms, encounterService));
        return [treatmentStatus, treatmentHistory, ...treatmentMethods, ...treatmentSymptoms];
    };

    const buildTBTreatmentPayload = async (
        patientID: number,
        encounterTypeID: number = 53,
        providerID: number,
        locationID: string,
        existingTBTreatment: ExistingTBTreatment
    ): Promise<any[]> => {
        const encounterService = new AppEncounterService(patientID, encounterTypeID, providerID, locationID);
        const treatmentStatus = await encounterService.buildValueCoded("TB treatment", tbTherapyStatus.value);

        if (tbTherapyStatus.value === "Yes") {
            const newTreatment = await buildNewTBTreament(encounterService);
            return [treatmentStatus, ...newTreatment];
        } else {
            const existingTreatment = await buildExistingTBTreatment(
                existingTBTreatment.history,
                existingTBTreatment.status,
                existingTBTreatment.methods,
                existingTBTreatment.symptoms,
                encounterService
            );
            return [treatmentStatus, ...existingTreatment];
        }
    };

    return {
        tbTherapyStatus,
        tbTreatmentStartDate,
        tbTreatmentPeriod,
        onTBTreatmentOptions,
        tbStatusOptions,
        tbEstimatePeriods,
        selectedEstimatePeriod,
        showEstimator,
        tbStartEstimate,
        startDateEstimator,
        showTBStatus,
        tptHistoryOptions,
        buildTBTreatmentPayload,
        submitTBTherapy,
    };
};
