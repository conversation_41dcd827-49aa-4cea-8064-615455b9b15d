import { ref } from 'vue';

export interface TBSymptom {
  id: string;
  label: string;
  value: boolean;
}

export const useTBSymptoms = () => {
  const tbSymptomOptions = ref<TBSymptom[]>([
      { id: 'Weight loss / Failure to thrive / malnutrition', label: "Weight loss / failure to thrive / malnutrition", value: false },
      { id: "Fever", label: "Fever", value: false },
      { id: "Night sweats", label: "Night sweats", value: false },
      { id: "Cough", label: "Cough", value: false },
  ]);

  const tbSymptoms = ref<Record<string, boolean>>({});

  tbSymptomOptions.value.forEach(option => {
    tbSymptoms.value[option.id] = option.value;
  });

  const resetTBSymptoms = () => {
    tbSymptomOptions.value.forEach(option => {
      tbSymptoms.value[option.id] = false;
    });
  };

  const hasSelectedTBSymptoms = (): boolean => {
    return Object.values(tbSymptoms.value).some(value => value === true);
  };

  const getSelectedTBSymptoms = (): string[] => {
    return tbSymptomOptions.value
      .filter(option => tbSymptoms.value[option.id])
      .map(option => option.label);
  };

  return {
    tbSymptomOptions,
    tbSymptoms,
    resetTBSymptoms,
    hasSelectedTBSymptoms,
    getSelectedTBSymptoms
  };
};
