import { Ref, ref } from 'vue';
import { OrderService } from '@/services/order_service';
import { LabOrder } from '@/services/lab_order';
import { getOfflineRecords } from '@/services/offline_service';
import { toastSuccess, toastWarning, toastDanger } from '@/utils/Alerts';

export interface TestOption {
    id: string;
    name: string;
    assigned_id: string;
}

export interface SpecimenOption {
    id: string;
    name: string;
    assigned_id: string;
}

export interface LabInvestigation {
    id: string;
    test: TestOption;
    specimen: SpecimenOption;
    dateAdded: string;
    isEditing?: boolean;
}

export const useLabInvestigation = (): {
    testOptions: Ref<TestOption[]>;
    specimenOptions: Ref<SpecimenOption[]>;
    selectedTest: Ref<TestOption | null>;
    selectedSpecimen: Ref<SpecimenOption | null>;
    labInvestigations: Ref<LabInvestigation[]>;
    editingInvestigationId: Ref<string | null>;
    loadTestTypes: () => Promise<void>;
    loadSpecimens: (testName: string) => Promise<void>;
    handleTestChange: (test: TestOption | null) => void;
    refreshTestTypes: () => Promise<void>;
    saveLabInvestigation: () => Promise<void>;
    editLabInvestigation: (id: string) => void;
    cancelLabInvestigation: (id: string) => void;
} => {
    const testOptions = ref<TestOption[]>([]);
    const specimenOptions = ref<SpecimenOption[]>([]);
    const selectedTest = ref<TestOption | null>(null);
    const selectedSpecimen = ref<SpecimenOption | null>(null);
    const labInvestigations = ref<LabInvestigation[]>([]);
    const editingInvestigationId = ref<string | null>(null);

    const generateUniqueId = (): string => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };

    const loadTestTypes = async (): Promise<void> => {
        try {
            const offlineTests = await getOfflineRecords("testTypes");
            if (Array.isArray(offlineTests) && offlineTests.length > 0) {
                testOptions.value = offlineTests.map((test: any) => ({
                    id: test.concept_id.toString(),
                    name: test.name,
                    assigned_id: test.concept_id.toString()
                }));
            } else if (offlineTests && 'records' in offlineTests && offlineTests.records.length > 0) {
                testOptions.value = offlineTests.records.map((test: any) => ({
                    id: test.concept_id.toString(),
                    name: test.name,
                    assigned_id: test.concept_id.toString()
                }));
            } else {
                const apiTests = await OrderService.getTestTypes();
                if (apiTests && apiTests.length > 0) {
                    testOptions.value = apiTests.map((test: any) => ({
                        id: test.concept_id.toString(),
                        name: test.name,
                        assigned_id: test.concept_id.toString()
                    }));
                }
            }
        } catch (error) {
            console.error('Error loading test types:', error);
            toastDanger('Failed to load test types');
        }
    };

    const loadSpecimens = async (testName: string): Promise<void> => {
        if (!testName) {
            specimenOptions.value = [];
            return;
        }
        try {
            const specimens = await OrderService.getSpecimens(testName);
            if (specimens && specimens.length > 0) {
                specimenOptions.value = specimens.map((specimen: any) => ({
                    id: specimen.concept_id.toString(),
                    name: specimen.name,
                    assigned_id: specimen.concept_id.toString()
                }));
                if (specimens.length === 1) {
                    selectedSpecimen.value = specimenOptions.value[0];
                }
            } else {
                specimenOptions.value = [];
                toastWarning('No specimens found for the selected test');
            }
        } catch (error) {
            console.error('Error loading specimens:', error);
            toastDanger('Failed to load specimens');
        }
    };

    const handleTestChange = (test: TestOption | null): void => {
        if (test && test.name) {
            loadSpecimens(test.name);
        } else {
            specimenOptions.value = [];
            selectedSpecimen.value = null;
        }
    };

    const refreshTestTypes = async (): Promise<void> => {
        try {
            const apiTests = await OrderService.getTestTypesViaDirectAPICall();
            if (apiTests && apiTests.length > 0) {
                testOptions.value = apiTests.map((test: any) => ({
                    id: test.concept_id.toString(),
                    name: test.name,
                    assigned_id: test.concept_id.toString()
                }));
                toastSuccess('Test types refreshed from API');
            } else {
                toastWarning('No test types found');
            }
        } catch (error) {
            console.error('Error refreshing test types:', error);
            toastDanger('Failed to refresh test types');
        }
    };

    const saveLabInvestigation = async (): Promise<void> => {
        if (selectedTest.value && selectedSpecimen.value) {
            if (editingInvestigationId.value) {
                const index = labInvestigations.value.findIndex(item => item.id === editingInvestigationId.value);
                if (index !== -1) {
                    labInvestigations.value[index] = {
                        ...labInvestigations.value[index],
                        test: selectedTest.value,
                        specimen: selectedSpecimen.value,
                        isEditing: false
                    };
                    editingInvestigationId.value = null;
                }
            } else {
                const newInvestigation: LabInvestigation = {
                    id: generateUniqueId(),
                    test: selectedTest.value,
                    specimen: selectedSpecimen.value,
                    dateAdded: new Date().toISOString(),
                    isEditing: false
                };
                labInvestigations.value.push(newInvestigation);
            }
            selectedTest.value = null;
            selectedSpecimen.value = null;
        } else {
            toastWarning('Please select both test and specimen');
        }
    };

    const editLabInvestigation = (id: string): void => {
        const investigation = labInvestigations.value.find(item => item.id === id);
        if (investigation) {
            selectedTest.value = investigation.test;
            selectedSpecimen.value = investigation.specimen;
            editingInvestigationId.value = investigation.id;
        }
    };

    const cancelLabInvestigation = (id: string): void => {
        const index = labInvestigations.value.findIndex(item => item.id === id);
        if (index !== -1) {
            labInvestigations.value.splice(index, 1);
        }

        if (editingInvestigationId.value === id) {
            selectedTest.value = null;
            selectedSpecimen.value = null;
            editingInvestigationId.value = null;
        }
    };

    return {
        testOptions,
        specimenOptions,
        selectedTest,
        selectedSpecimen,
        labInvestigations,
        editingInvestigationId,
        loadTestTypes,
        loadSpecimens,
        handleTestChange,
        refreshTestTypes,
        saveLabInvestigation,
        editLabInvestigation,
        cancelLabInvestigation
    };
};
