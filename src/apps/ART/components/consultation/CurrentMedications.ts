import { ref, onMounted } from 'vue';
import { ConsultationService } from '../../services/consultation_service';
import { useUserStore } from '@/stores/userStore';
import { useDemographicsStore } from "@/stores/DemographicStore";
import { toastDanger } from '@/utils/Alerts';

interface DoseValue {
  source: string;
  parsedValue: number;
}

interface DrugDosageStruct {
  drug_id: number;
  drug_name: string;
  am: DoseValue;
  noon: number;
  pm: DoseValue;
  units: string;
}

interface DrugOrder {
  order_id: number;
  drug_inventory_id: number;
  dose: DoseValue;
  equivalent_daily_dose: DoseValue;
  units: string;
  frequency: string;
  prn: number;
  complex: number;
  quantity: number;
  dosage_struct: DrugDosageStruct;
  amount_needed: number;
  barcodes: any[];
  regimen: string;
  order: {
    order_id: number;
    start_date: string;
    auto_expire_date: string;
    instructions: string;
    [key: string]: any;
  };
  drug: {
    drug_id: number;
    name: string;
    units: string;
    [key: string]: any;
  };
}

export interface Medication {
  drug_id: number;
  drug_name: string;
  dose: string;
  units: string;
  frequency: string;
  start_date: string;
  end_date?: string;
  quantity?: number;
}

export const useCurrentMedications = () => {
  const medications = ref<Medication[]>([]);
  const isLoading = ref<boolean>(false);
  const error = ref<string | null>(null);

  const loadCurrentMedications = async () => {
    isLoading.value = true;
    error.value = null;

    try {
      const userStore = useUserStore();
      const demographicsStore = useDemographicsStore();
      const patientID = demographicsStore.patient?.patient_id;
      const providerID = parseInt(userStore.getUserId() || '-1');
      const locationID = userStore.getfacilityLocation()?.code || '';

      if (!locationID) {
        throw new Error('Location ID not found');
      }

      const consultationService = new ConsultationService(patientID, providerID, locationID);
      const data = await consultationService.getCurrentMedications();

      if (data && typeof data === 'object') {
        const medicationsArray: Medication[] = Object.values(data).map((drugOrder: any) => {
          const order = drugOrder as DrugOrder;
          return {
            drug_id: order.drug_inventory_id,
            drug_name: order.dosage_struct.drug_name,
            dose: order.dose.source,
            units: order.units,
            frequency: order.frequency,
            start_date: order.order.start_date,
            end_date: order.order.auto_expire_date,
            quantity: order.quantity
          };
        });

        medications.value = medicationsArray;
      } else {
        medications.value = [];
      }
    } catch (err) {
      console.error('Error loading current medications:', err);
      error.value = err instanceof Error ? err.message : 'Failed to load medications';
      toastDanger('Failed to load current medications');
    } finally {
      isLoading.value = false;
    }
  };

  onMounted(() => {
    loadCurrentMedications();

    if (medications.value.length === 0) {
      medications.value = [
        {
          drug_id: 1,
          drug_name: 'Tenofovir 300mg',
          dose: '1 tablet',
          units: 'tablet',
          frequency: 'Once daily',
          start_date: '2023-01-01'
        },
        {
          drug_id: 2,
          drug_name: 'Lamivudine 150mg',
          dose: '1 tablet',
          units: 'tablet',
          frequency: 'Twice daily',
          start_date: '2023-01-01'
        },
        {
          drug_id: 3,
          drug_name: 'Efavirenz 600mg',
          dose: '1 tablet',
          units: 'tablet',
          frequency: 'Once daily',
          start_date: '2023-01-01'
        }
      ];
    }
  });

  return {
    medications,
    isLoading,
    error,
    loadCurrentMedications
  };
};
