import { ref } from 'vue';
import Chart from 'chart.js/auto';
import { PatientService } from '@/services/patient_service';
import { toastWarning, toastDanger } from '@/utils/Alerts';
import dayjs from 'dayjs';

export interface WeightDataPoint {
    date: string;
    weight: number;
    height?: number;
}

export const useWeightChart = () => {
    const chartPeriod = ref<number>(2);
    const weightChartCanvas = ref<HTMLCanvasElement | null>(null);
    let weightChart: Chart | null = null;
    const isLoadingWeightData = ref<boolean>(false);
    const weightData = ref<WeightDataPoint[]>([]);

    window.addEventListener('resize', () => {
        if (weightChart && weightChartCanvas.value) {
            weightChart.resize();
        }
    });

    const loadWeightHistory = async (): Promise<void> => {
        isLoadingWeightData.value = true;
        try {
            const patientService = new PatientService();
            const weightHistory = await patientService.getWeightHistory();

            if (weightHistory && weightHistory.length > 0) {
                weightData.value = weightHistory.map((item: any) => ({
                    date: dayjs(item.date).format('MM-DD'),
                    weight: parseFloat(item.weight)
                }));

                weightData.value.sort((a, b) => {
                    return dayjs(a.date, 'MM-DD').valueOf() - dayjs(b.date, 'MM-DD').valueOf();
                });
            } else {
                toastWarning('No weight history found for this patient');
                weightData.value = [];

                if (weightChart) {
                    weightChart.destroy();
                    weightChart = null;
                }
            }
        } catch (error) {
            console.error('Error loading weight history:', error);
            toastDanger('Failed to load weight history');

            weightData.value = [];

            if (weightChart) {
                weightChart.destroy();
                weightChart = null;
            }
        } finally {
            isLoadingWeightData.value = false;
        }
    };

    const initializeWeightChart = (): void => {
        if (!weightChartCanvas.value || weightData.value.length === 0) return;

        if (weightChart) {
            weightChart.destroy();
            weightChart = null;
        }

        const parentWidth = weightChartCanvas.value.parentElement?.clientWidth || 800;
        weightChartCanvas.value.width = parentWidth;
        weightChartCanvas.value.height = 300;

        const ctx = weightChartCanvas.value.getContext('2d');
        if (!ctx) return;

        const dates: string[] = weightData.value.map(item => item.date);
        const weights: number[] = weightData.value.map(item => item.weight);

        const heightPointIndex: number = weightData.value.findIndex(item => item.height !== undefined);

        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(75, 192, 75, 0.6)');
        gradient.addColorStop(1, 'rgba(75, 192, 75, 0.1)');

        weightChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: 'Weight',
                    data: weights,
                    borderColor: 'rgb(75, 192, 75)',
                    backgroundColor: gradient,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: 'rgb(75, 192, 75)',
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        top: 10,
                        right: 10,
                        bottom: 10,
                        left: 10
                    }
                },
                scales: {
                    y: {
                        min: 30,
                        max: 220,
                        ticks: {
                            stepSize: 10
                        },
                        grid: {
                            color: 'rgba(200, 200, 200, 0.3)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(200, 200, 200, 0.3)'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            afterLabel: (context): string | void => {
                                if (context.dataIndex === heightPointIndex && weightData.value[heightPointIndex].height) {
                                    return `Height: ${weightData.value[heightPointIndex].height}cm`;
                                }
                            }
                        }
                    }
                }
            }
        });
    };

    const cleanupChart = (): void => {
        if (weightChart) {
            weightChart.destroy();
            weightChart = null;
        }
    };

    return {
        chartPeriod,
        weightChartCanvas,
        isLoadingWeightData,
        weightData,
        loadWeightHistory,
        initializeWeightChart,
        cleanupChart
    };
};
