<template>
  <div class="tb-symptoms-grid">
    <div v-for="option in tbSymptomOptions" :key="option.id" class="checkbox-option">
      <ion-checkbox v-model="modelValue[option.id]" :value="option.label"
        @update:model-value="updateModelValue"></ion-checkbox>
      <span class="checkbox-label">{{ option.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IonCheckbox } from '@ionic/vue';
import { TBSymptom } from './TBSymptoms';

interface Props {
  modelValue: Record<string, boolean>;
  tbSymptomOptions: TBSymptom[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update:modelValue', value: Record<string, boolean>): void;
}>();

const updateModelValue = () => {
  emit('update:modelValue', props.modelValue);
};
</script>

<style scoped>
.tb-symptoms-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-top: 10px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-label {
  font-size: 14px;
}

:deep(ion-checkbox) {
  --size: 20px;
  --checkbox-background-checked: var(--ion-color-primary);
  --border-radius: 2px;
  margin: 0;
}
</style>
