import { ref, onMounted, computed } from 'vue';
import sideEffectsDictionary from './data/sideEffectsDictionary';
import { AppEncounterService } from '@/services/app_encounter_service';
import { ObsValue } from '@/services/observation_service';
import { toastDanger, toastSuccess } from '@/utils/Alerts';
import { Option } from '@/components/Forms/FieldInterface';

export interface SideEffect {
  id: string;
  label: string;
  value: boolean;
  concept_id: number;
  reason?: ObsValue[];
}

export type CategoryType = 'side_effect' | 'contraindication' | 'all';

export interface SideEffectWithReason {
  selected: boolean;
  reason?: string | number | number[];
}

export const useSideEffects = (category: CategoryType = 'all') => {
  const sideEffectOptions = ref<SideEffect[]>([]);
  const sideEffects = ref<Record<string, SideEffectWithReason>>({});
  const isLoading = ref<boolean>(false);
  const encounterId = ref<number | null>(null);
  const error = ref<string | null>(null);

  const filteredDictionary = computed(() => {
    if (category === 'all') {
      return sideEffectsDictionary;
    }
    return sideEffectsDictionary.filter(item => item.category === category);
  });

  const initializeSideEffects = () => {
    const options = filteredDictionary.value.map(item => ({
      id: `${item.concept_id}`,
      label: item.name,
      value: false,
      concept_id: item.concept_id
    }));

    sideEffectOptions.value = options;
    options.forEach(option => {
      sideEffects.value[option.id] = { selected: option.value };
    });
  };

  const resetSideEffects = () => {
    sideEffectOptions.value.forEach(option => {
      sideEffects.value[option.id] = { selected: false };
    });
  };

  const hasSelectedSideEffects = (): boolean => {
    return Object.values(sideEffects.value).some(value => value.selected === true);
  };

  const getSelectedSideEffects = (): string[] => {
    return sideEffectOptions.value
      .filter(option => sideEffects.value[option.id]?.selected)
      .map(option => option.label);
  };

  const filterByCategory = (newCategory: CategoryType) => {
    const currentSelections = { ...sideEffects.value };

    category = newCategory;
    initializeSideEffects();

    sideEffectOptions.value.forEach(option => {
      if (currentSelections[option.id] !== undefined) {
        sideEffects.value[option.id] = currentSelections[option.id];
      }
    });
  };

  const mapSideEffectsToOptions = (sideEffects: SideEffect[]): Option[] => {
    return sideEffects.map(option => ({
      label: option.label,
      value: option.id,
      isChecked: option.value
    }));
  };

  onMounted(() => {
    initializeSideEffects();
  });

  const buildSideEffectsPayload = async (patientID: number, providerID: number, locationID: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      const encounterService = new AppEncounterService(patientID, 53, providerID, locationID);
      const encounter = await encounterService.createEncounter();

      if (!encounter) {
        throw new Error('Failed to create encounter');
      }

      encounterId.value = encounter.encounter_id;

      const conceptName: Record<CategoryType, string> = {
        'side_effect': 'Malawi ART side effects',
        'contraindication': 'Other side effect',
        'all': 'Malawi ART Side Effects'
      };

      const sideEffectOpts: SideEffect[] = sideEffectsDictionary
        .filter(item => item.category === 'side_effect')
        .map(item => ({
          id: `${item.concept_id}`,
          label: item.name,
          value: false,
          concept_id: item.concept_id
        }));

      const contraindicationOpts: SideEffect[] = sideEffectsDictionary
        .filter(item => item.category === 'contraindication')
        .map(item => ({
          id: `${item.concept_id}`,
          label: item.name,
          value: false,
          concept_id: item.concept_id
        }));

      sideEffectOpts.forEach(option => {
        if (sideEffects.value[option.id]) {
          option.value = sideEffects.value[option.id].selected;
        }
      });

      contraindicationOpts.forEach(option => {
        if (sideEffects.value[option.id]) {
          option.value = sideEffects.value[option.id].selected;
        }
      });

      const sideEffectObs = await encounterService.buildOptionsGroupObs(
        conceptName['side_effect'],
        mapSideEffectsToOptions(sideEffectOpts)
      );

      const contraindicationObs = await encounterService.buildOptionsGroupObs(
        conceptName['contraindication'],
        mapSideEffectsToOptions(contraindicationOpts)
      );

      const sideEffectReasons = sideEffectOpts
        .filter(option => option.value && option.reason)
        .flatMap(option => option.reason || []);

      const contraindicationReasons = contraindicationOpts
        .filter(option => option.value && option.reason)
        .flatMap(option => option.reason || []);

      return {
        encounter_id: encounter.encounter_id,
        observations: [...sideEffectObs, ...contraindicationObs, ...sideEffectReasons, ...contraindicationReasons]
      };
    } catch (err) {
      console.error('Error building side effects payload:', err);
      error.value = 'Failed to build side effects payload';
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  const saveSideEffectsData = async (patientID: number, providerID: number, locationID: string): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;

    try {
      const payload = await buildSideEffectsPayload(patientID, providerID, locationID);
      encounterId.value = Number(payload?.encounter_id);

      if (!payload) {
        throw new Error('Failed to build payload');
      }

      await AppEncounterService.saveObsArray(payload.encounter_id, payload.observations as unknown as ObsValue[]);
      toastSuccess('Side effects saved successfully');
      return true;
    } catch (err) {
      console.error('Error saving side effects:', err);
      error.value = 'Failed to save side effects';
      toastDanger('Failed to save side effects');
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    sideEffectOptions,
    sideEffects,
    resetSideEffects,
    hasSelectedSideEffects,
    getSelectedSideEffects,
    filterByCategory,
    isLoading,
    error,
    buildSideEffectsPayload,
    saveSideEffectsData,
    encounterId
  };
};
