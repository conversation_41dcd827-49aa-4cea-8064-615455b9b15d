<template>
  <ion-modal :is-open="isOpen" @didDismiss="handleClose">
    <ion-header>
      <ion-toolbar>
        <div slot="start" class="ion-padding">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5">
              <path
                d="M6.01 16.607a3.495 3.495 0 1 0 0-6.99a3.495 3.495 0 0 0 0 6.99m5.242 6.616a5.243 5.243 0 0 0-10.485 0m20.968-7.821V3.751a2.996 2.996 0 1 0-5.991 0v11.651a4.493 4.493 0 1 0 5.991 0M18.74 5.249v11.983" />
              <path
                d="M18.74 20.227a1.498 1.498 0 1 0 0-2.995a1.498 1.498 0 0 0 0 2.995M2.014 7.745a2.47 2.47 0 0 1 0-3.495a2.47 2.47 0 0 0 0-3.495m3.721 6.99a2.47 2.47 0 0 1 0-3.495a2.47 2.47 0 0 0 0-3.495m3.995 6.99a2.47 2.47 0 0 1 0-3.495a2.473 2.473 0 0 0 0-3.495" />
            </g>
          </svg>
        </div>
        <ion-title>{{ sideEffect?.label || 'Side Effect Details' }}</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="emit('close')">
            <ion-icon :icon="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-card-content class="ion-padding">
      <ion-list>
        <ion-item>
          <ion-label>Is this drug-induced?</ion-label>
        </ion-item>
        <ion-radio-group v-model="isDrugInduced" @ion-change="handleDrugInducedChange">
          <ion-item>
            <ion-radio value="yes">Yes</ion-radio>
          </ion-item>
          <ion-item>
            <ion-radio value="no">No</ion-radio>
          </ion-item>
        </ion-radio-group>

        <template v-if="isDrugInduced === 'yes'">
          <ion-item>
            <ion-label>Select medications:</ion-label>
          </ion-item>
          <ion-spinner v-if="isLoadingMedications" name="crescent"></ion-spinner>
          <ion-item v-else-if="medications.length === 0">
            <ion-label>No medications found</ion-label>
          </ion-item>
          <ion-item v-else v-for="med in medications" :key="med.drug_id">
            <ion-checkbox :value="med.drug_id" :checked="selectedDrugs.includes(med.drug_id)"
              @ion-change="toggleDrug(med.drug_id, $event)">
            </ion-checkbox>
            <ion-label class="ion-padding-start">{{ med.drug_name }}</ion-label>
          </ion-item>
          <ion-item>
            <ion-checkbox value="drug_side_effect" :checked="selectedDrugs.includes('drug_side_effect')"
              @ion-change="toggleDrug('drug_side_effect', $event)">
            </ion-checkbox>
            <ion-label class="ion-padding-start">Drug side effect (unspecified)</ion-label>
          </ion-item>
        </template>

        <template v-if="isDrugInduced === 'no'">
          <ion-item>
            <ion-label>Reason:</ion-label>
          </ion-item>
          <ion-item>
            <ion-radio value="other_not_drug_related" :checked="reason === 'other_not_drug_related'"
              @ion-change="updateReason('other_not_drug_related')">
            </ion-radio>
            <ion-label class="ion-padding-start">Other not drug related</ion-label>
          </ion-item>
        </template>
      </ion-list>
    </ion-card-content>

    <ion-footer>
      <ion-toolbar color="light">
        <ion-buttons slot="end">
          <ion-button @click="saveAndClose" color="primary">Save</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-footer>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonCardContent,
  IonFooter,
  IonButtons,
  IonButton,
  IonIcon,
  IonRadio,
  IonRadioGroup,
  IonCheckbox,
  IonSpinner,
  IonList,
  IonItem,
  IonLabel
} from '@ionic/vue';
import { close } from 'ionicons/icons';
import { SideEffect } from './SideEffects';
import { useCurrentMedications } from './CurrentMedications';
import { ConceptService } from '@/services/concept_service';

interface Props {
  isOpen: boolean;
  sideEffect: SideEffect | null;
  currentReason: string | number | number[] | undefined;
}

const props = defineProps<Props>();
const emit = defineEmits(['update', 'close']);

const { medications, isLoading: isLoadingMedications } = useCurrentMedications();
const isDrugInduced = ref<'yes' | 'no'>('no');
const selectedDrugs = ref<(string | number)[]>([]);
const reason = ref<string | number | undefined>(undefined);

onMounted(() => {
  initializeValues();
});

const initializeValues = () => {
  selectedDrugs.value = [];
  reason.value = undefined;

  if (props.currentReason) {
    if (typeof props.currentReason === 'number' || props.currentReason === 'drug_side_effect' || Array.isArray(props.currentReason)) {
      isDrugInduced.value = 'yes';
      if (Array.isArray(props.currentReason) && props.currentReason.length > 0) {
        selectedDrugs.value = [...props.currentReason];
      } else if (props.currentReason !== 'drug_side_effect') {
        selectedDrugs.value = [props.currentReason as string | number];
      }
    } else {
      isDrugInduced.value = 'no';
      reason.value = props.currentReason;
    }
  } else {
    isDrugInduced.value = 'no';
    reason.value = 'other_not_drug_related';
  }
};

const handleDrugInducedChange = (event: CustomEvent) => {
  isDrugInduced.value = event.detail.value;

  if (isDrugInduced.value === 'yes') {
    selectedDrugs.value = [];
    reason.value = undefined;
  } else {
    selectedDrugs.value = [];
    reason.value = 'other_not_drug_related';
  }
};

const toggleDrug = (drugId: string | number, event: CustomEvent) => {
  const isChecked = event.detail.checked;

  if (isChecked && !selectedDrugs.value.includes(drugId)) {
    selectedDrugs.value.push(drugId);
  } else if (!isChecked) {
    selectedDrugs.value = selectedDrugs.value.filter(id => id !== drugId);
  }
};

const updateReason = (newReason: string) => {
  reason.value = newReason;
};

const buildSideEffectsReasons = async (sideEffect: SideEffect, drugId: number) => {
  const drugInducedConcept = ConceptService.getCachedConceptID('Drug induced', true)
  return {
    'concept_id': drugInducedConcept,
    'value_coded': sideEffect.concept_id,
    'value_text': isDrugInduced.value == 'no' ? 'Past medication history' : null,
    'value_drug': isDrugInduced.value == 'yes' ? drugId : null
  }
}

const filteredReason = computed(() => {
  let reasons: Record<string, any>[] = [];
  selectedDrugs.value.length > 0 && selectedDrugs.value.map((drugId: number | string) => {
    if (props.sideEffect) {
      reasons.push(buildSideEffectsReasons(props.sideEffect, Number(drugId)));
    }
  });
  return reasons;
})

const resetModalState = () => {
  isDrugInduced.value = 'no';
  selectedDrugs.value = [];
  reason.value = 'other_not_drug_related';
};

const handleClose = () => {
  resetModalState();
  emit('close');
};

const saveAndClose = () => {
  if (!props.sideEffect) return;

  emit('update', {
    id: props.sideEffect.id,
    reason: filteredReason.value
  });

  handleClose();
};
</script>