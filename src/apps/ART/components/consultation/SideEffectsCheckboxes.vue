<template>
  <div class="side-effects-grid">
    <div v-for="option in sideEffectOptions" :key="option.id" class="side-effect-item">
      <div class="checkbox-container">
        <ion-checkbox :value="option.id" :checked="modelValue[option.id]?.selected"
          @ion-change="handleSideEffectChange(option, $event)"></ion-checkbox>
        <span class="checkbox-label">{{ option.label }}</span>
      </div>

      <div v-if="modelValue[option.id]?.selected" class="reason-summary">
        <div class="reason-label">
          <span v-if="isDrugInduced(option.id) === 'yes'" class="drug-induced">Drug-induced</span>
          <span v-else class="not-drug-induced">Not drug-induced</span>
        </div>
        <ion-button fill="clear" size="small" class="edit-button" @click="openSideEffectModal(option)">
          Edit
          <ion-icon slot="end" :icon="createOutline"></ion-icon>
        </ion-button>
      </div>
    </div>
  </div>

  <side-effect-modal :is-open="modalOpen" :side-effect="selectedSideEffect" :current-reason="currentReason"
    @update="updateSideEffectFromModal" @close="closeModal" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { IonCheckbox, IonButton, IonIcon } from '@ionic/vue';
import { createOutline } from 'ionicons/icons';
import { SideEffect, SideEffectWithReason } from './SideEffects';
import SideEffectModal from './SideEffectModal.vue';

interface Props {
  modelValue: Record<string, SideEffectWithReason>;
  sideEffectOptions: SideEffect[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update:modelValue', value: Record<string, SideEffectWithReason>): void;
}>();

const modalOpen = ref<boolean>(false);
const selectedSideEffect = ref<SideEffect | null>(null);
const currentReason = ref<string | number | number[] | undefined>(undefined);

const handleSideEffectChange = (option: SideEffect, event: CustomEvent) => {
  const isChecked = event.detail.checked;
  const newValue = { ...props.modelValue };

  if (isChecked) {
    newValue[option.id] = {
      selected: true,
      reason: 'other_not_drug_related'
    };
    emit('update:modelValue', newValue);
    openSideEffectModal(option);
  } else {
    newValue[option.id] = {
      selected: false,
      reason: newValue[option.id]?.reason
    };
    emit('update:modelValue', newValue);
  }
};

const isDrugInduced = (id: string): 'yes' | 'no' => {
  const reason = props.modelValue[id]?.reason;

  if (
    (typeof reason === 'number') ||
    reason === 'drug_side_effect' ||
    (Array.isArray(reason) && reason.length > 0)
  ) {
    return 'yes';
  }

  return 'no';
};

const openSideEffectModal = (option: SideEffect) => {
  selectedSideEffect.value = option;
  currentReason.value = props.modelValue[option.id]?.reason;
  modalOpen.value = true;
};

const closeModal = () => {
  modalOpen.value = false;
  selectedSideEffect.value = null;
};

const updateSideEffectFromModal = (data: { id: string; reason: string | number | number[] }) => {
  const newValue = { ...props.modelValue };

  if (newValue[data.id]) {
    newValue[data.id] = {
      ...newValue[data.id],
      reason: data.reason
    };
    emit('update:modelValue', newValue);
  }
};
</script>

<style scoped>
.side-effects-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.side-effect-item {
  display: flex;
  flex-direction: column;
  border: 1px dotted #eae9e9;
  border-radius: 5px;
  padding: 12px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-label {
  font-size: 14px;
}

.reason-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 30px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #eee;
}

.reason-label {
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.drug-induced {
  color: var(--ion-color-primary);
  background-color: rgba(0, 100, 2, 0.034);
  padding: 4px 8px;
  border-radius: 4px;
}

.not-drug-induced {
  padding: 4px 8px;
  border-radius: 4px;
}

.edit-button {
  font-size: 12px;
  --padding-start: 8px;
  --padding-end: 8px;
}

:deep(ion-checkbox) {
  --size: 20px;
  --checkbox-background-checked: var(--ion-color-primary);
  --border-radius: 2px;
  margin: 0;
}
</style>
