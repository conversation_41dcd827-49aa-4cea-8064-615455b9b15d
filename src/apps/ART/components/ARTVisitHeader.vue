<template>
    <IonToolbar color="light" class="patient-header">
        <IonRow class="patient-info-row">
            <IonCol size="12" size-sm="6" size-md="4" size-lg="2.4" class="patient-info-col">
                <div class="info-item">
                    <IonIcon :icon="person" class="info-icon" />
                    <div class="info-text">
                        <IonLabel>Full name</IonLabel>
                        <div class="info-value"><PERSON></div>
                    </div>
                </div>
            </IonCol>
            <IonCol size="12" size-sm="6" size-md="4" size-lg="2.4" class="patient-info-col">
                <div class="info-item">
                    <IonIcon :icon="barcode" class="info-icon" />
                    <div class="info-text">
                        <IonLabel>MRN</IonLabel>
                        <div class="info-value">10773807-00-00-04/2023</div>
                    </div>
                </div>
            </IonCol>
            <IonCol size="12" size-sm="6" size-md="4" size-lg="2.4" class="patient-info-col">
                <div class="info-item">
                    <IonIcon :icon="calendar" class="info-icon" />
                    <div class="info-text">
                        <IonLabel>Age</IonLabel>
                        <div class="info-value">21y (08 June, 1996)</div>
                    </div>
                </div>
            </IonCol>
            <IonCol size="12" size-sm="6" size-md="4" size-lg="2.4" class="patient-info-col">
                <div class="info-item">
                    <IonIcon :icon="male" class="info-icon" />
                    <div class="info-text">
                        <IonLabel>Sex</IonLabel>
                        <div class="info-value">M</div>
                    </div>
                </div>
            </IonCol>
            <IonCol size="12" size-sm="6" size-md="4" size-lg="2.4" class="patient-info-col">
                <div class="info-item">
                    <IonIcon :icon="location" class="info-icon" />
                    <div class="info-text">
                        <IonLabel>Address</IonLabel>
                        <div class="info-value">Chaundwa, Dedza</div>
                    </div>
                </div>
            </IonCol>
        </IonRow>
    </IonToolbar>
</template>

<script setup lang="ts">
import { IonCol, IonIcon, IonLabel, IonRow, IonToolbar } from '@ionic/vue';
import {
    person,
    idCard,
    calendar,
    male,
    location,
    barcode
} from 'ionicons/icons';

</script>

<style lang="css" scoped>
.patient-header {
    --padding-top: 8px;
    --padding-bottom: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.patient-info-row {
    width: 100%;
}

.patient-info-col {
    padding: 8px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.info-icon {
    font-size: 24px;
    color: var(--ion-color-primary);
    flex-shrink: 0;
    background-color: rgba(0, 100, 2, 0.147);
    border-radius: 50%;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-text {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.info-label {
    font-size: 12px;
    color: var(--ion-color-medium);
    margin-bottom: 2px;
}

.info-value {
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .patient-info-col {
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .patient-info-col:last-child {
        border-bottom: none;
    }

    .info-item {
        padding: 4px 0;
    }
}

@media (max-width: 576px) {
    .info-icon {
        font-size: 20px;
        padding: 6px;
    }
}

@media (min-width: 576px) and (max-width: 992px) {
    .info-icon {
        font-size: 20px;
        padding: 6px;
    }

    .info-value {
        font-size: 14px;
    }
}
</style>