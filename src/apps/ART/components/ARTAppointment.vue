<template>
    <ion-card>
        <span class="section-header ion-text-left" style="margin-bottom: 10px">
            <!-- Warning section -->
            <div v-if="hasWarnings" class="warning-section">
                <template v-for="(message, key) in warnings" :key="key">
                    <ion-note v-if="message">
                        {{ message }}
                    </ion-note>
                    <p />
                </template>
            </div>
            <hr style="border-top: 1px dashed #0c0c0c; margin-top: 10px" />
        </span>
        <ion-card-content>
            <ion-grid>
                <ion-row>
                    <ion-col size-sm="12" size-md="7">
                        <ion-datetime presentation="date" size="cover" locale="en-US"
                            v-model="appointmentInfo.userSetAppointment.val"
                            @ionChange="($event) => dateChanged($event?.detail?.value ?? ''.split('T')[0])"
                            :highlightedDates="highlightNonClinicDays" :isDateEnabled="disableDates"
                            :key="`${clinicDays.length}-${appointmentInfo.userSetAppointment.val}`" />
                    </ion-col>
                    <ion-col size-sm="12" size-md="5">
                        <ion-list>
                            <ion-item v-for="(info, index) in appointmentInfo" :key="index">
                                <ion-label> {{ info.label }}: </ion-label>
                                <span style="font-style: italic" v-if="info.isLoading">
                                    Please wait...
                                </span>
                                <span v-else>
                                    {{ info.formattedValue ?? info.val }}
                                </span>
                            </ion-item>
                        </ion-list>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import { GlobalPropName } from "@/constants/global_property_name";
import { AppointmentService } from "@/services/appointment_service";
import { GlobalPropertyService } from "@/services/global_property_service";
import {
    IonGrid,
    IonList,
    IonRow,
    IonLabel,
    IonItem,
    IonCol,
    IonDatetime,
    IonCard,
    IonCardContent,
    IonNote
} from "@ionic/vue";
import { ref, onMounted, computed } from "vue";
import { PatientService } from "@/services/patient_service";
import dayjs from "dayjs";
import { alertConfirmation, toastDanger, toastSuccess } from "@/utils/Alerts";

const patientService = new PatientService();
const appointmentService = new AppointmentService(patientService.getID(), -1);
const warnings = ref<Record<string, string>>({})
const clinicDays = ref<any>([])
const clinicHolidays = ref<any>([])
const canSubmit = ref(false)

// Computed property to check if there are any warnings to display
const hasWarnings = computed(() => {
    return Object.values(warnings.value).some(warning => warning !== "")
})

const appointmentInfo = ref<any>({
    medicationRunOut: {
        label: "Drug runout",
        val: "...",
        formattedValue: "",
        isLoading: false,
        apiObj: {} as any,

    },
    userSetAppointment: {
        label: "Selected date",
        val: "",
        formattedValue: "",
        isLoading: false
    },
    bookedAppointments: {
        label: "Booked Appointment",
        val: "",
        isLoading: false,
        limitCache: {}
    },
    appointmentLimit: {
        label: "Appointment limit",
        val: "",
        isLoading: false
    }
})

const appointmentInfoHandler: any = {
    userSetAppointment: {
        init: async () => {
            warnings.value['clinicDays'] = ""
            warnings.value['clinicHolidays'] = ""
            const prop = patientService.getAge() > 18 ? GlobalPropName.ADULT_CLINIC_DAYS : GlobalPropName.PEADS_CLINIC_DAYS
            return Promise.all([
                GlobalPropertyService.get(prop)
                    .then((value: any) => {
                        if (value) clinicDays.value = value.split(',').map((day: string) => day.trim())
                    })
                    .catch((e: any) => {
                        console.error(e)
                        warnings.value['clinicDays'] = "Failed to load clinic days"
                    }),
                GlobalPropertyService.get(GlobalPropName.CLINIC_HOLIDAYS)
                    .then((value: any) => {
                        if (value) clinicHolidays.value = value
                    })
                    .catch((e: any) => {
                        console.error(e)
                        warnings.value['clinicHolidays'] = "Failed to load clinic holidays"
                    })
            ])
        },
        validate: () => {
            warnings.value['userSetAppointment'] = ""
            if (!appointmentInfo.value.userSetAppointment.val) {
                warnings.value['userSetAppointment'] = "Appointment date is required"
                return false
            }

            if (appointmentInfo.value.bookedAppointments.val >= appointmentInfo.value.appointmentLimit.val) {
                warnings.value['userSetAppointment'] = "Appointment limit reached"
                return false
            }
            return true
        },
        buildObs: () => appointmentService.buildValueDate("Appointment date", appointmentInfo.value.userSetAppointment.val),
        onDateChange: async (date: string) => {
            warnings.value['clinicDateWarning'] = ""
            warnings.value['clinicHolidays'] = ""
            appointmentInfo.value.userSetAppointment.val = date
            appointmentInfo.value.userSetAppointment.formattedValue = dayjs(appointmentInfo.value.userSetAppointment.val).format('DD/MMM/YYYY')
            if (!isClinicDay(date)) {
                warnings.value['clinicDateWarning'] = "Selected date is not a clinic day"
            }
            if (isClinicHoliday(date)) {
                warnings.value['clinicHolidays'] = "Selected date is a clinic holiday"
            }
        }
    },
    bookedAppointments: {
        onDateChange: async (date: string) => {
            warnings.value['bookedAppointments'] = ""
            let count = 0
            if (appointmentInfo.value.bookedAppointments.limitCache[date]) {
                count = appointmentInfo.value.bookedAppointments.limitCache[date]
            } else {
                const appointments = await AppointmentService.getDailiyAppointments(date);
                if (appointments.length) {
                    appointmentInfo.value.bookedAppointments.limitCache[date] = appointments.length
                    count = appointments.length
                }
            }
            appointmentInfo.value.bookedAppointments.val = count;
            if (appointmentInfo.value.appointmentLimit.val > 1 && count >= appointmentInfo.value.appointmentLimit.val) {
                warnings.value['bookedAppointments'] = "Appointment limit reached"
            }
        }
    },
    medicationRunOut: {
        init: async () => {
            warnings.value['medicationRunOut'] = ""
            try {
                const res = await appointmentService.getNextAppointment();
                if (res) {
                    appointmentInfo.value['medicationRunOut'].apiObj = res;
                    appointmentInfo.value['medicationRunOut'].formattedValue = dayjs(res.drugs_run_out_date).format('DD/MMM/YYYY')
                    dateChanged(res.nextAppointment)
                }
            } catch (e) {
                warnings.value['medicationRunOut'] = "No medication runout date found for this patient"
                appointmentInfo.value['medicationRunOut'].formattedValue = "N/A"
                console.log(e)
            }
        },
        buildObs: () => {
            const apiObj = appointmentInfo.value.medicationRunOut.apiObj
            return appointmentService.buildValueDate("Estimated date", apiObj.nextAppointment ?? appointmentService.date)
        }
    },
    appointmentLimit: {
        init: async () => {
            appointmentInfo.value.appointmentLimit.val = await GlobalPropertyService.get(GlobalPropName.APPOINTMENT_LIMIT) ?? 'N/A'
        }
    }
}

function isClinicDay(date: string) {
    const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    const dayName = dayNames[new Date(date).getDay()];
    return clinicDays.value.some((day: string) => day.trim().toLowerCase() === dayName.toLowerCase())
}

function isClinicHoliday(date: string) {
    return new RegExp(date, 'i').test(clinicHolidays.value)
}

// Function to highlight non-clinic days in red
const highlightNonClinicDays = (dateString: string) => {
    if (!isClinicDay(dateString) || isClinicHoliday(dateString)) {
        return {
            textColor: '#ff0000',
            backgroundColor: 'rgba(255, 0, 0, 0.08)'
        };
    }
    return undefined;
}

// Function to disable dates on the calendar
const disableDates = (dateString: string) => {
    // Get the date from the string
    const date = new Date(dateString);
    date.setHours(0, 0, 0, 0); // Set time to beginning of day for proper comparison

    // Get the current session date from appointmentService
    const currentDate = new Date(appointmentService.date);
    currentDate.setHours(0, 0, 0, 0);

    // Get the medication run out date if available
    const medicationRunOutDate = appointmentInfo.value.medicationRunOut.apiObj?.drugs_run_out_date;

    // If we have a medication run out date, disable dates before current date and beyond medication run out date
    if (medicationRunOutDate) {
        const runOutDate = new Date(medicationRunOutDate);
        runOutDate.setHours(0, 0, 0, 0);

        // Return true only for dates between current date and medication run out date (inclusive)
        return date >= currentDate && date <= runOutDate;
    }

    // If no medication run out date is available, only disable dates before current date
    return date >= currentDate;
}

function validateAppointmentData(): boolean {
    // Simply check if any validation function returns false
    for (const key in appointmentInfoHandler) {
        if (typeof appointmentInfoHandler[key]?.validate != 'function') {
            continue
        }
        if (!appointmentInfoHandler[key].validate()) {
            return false
        }
    }
    return true;
}

async function onSubmit(): Promise<boolean> {
    console.log("Submitting appointment")
    if (!canSubmit.value) {
        toastDanger("Please wait for all data to load before submitting")
        return false
    }
    // Validate data before submission
    if (!validateAppointmentData()) {
        toastDanger("Please fix the validation errors before submitting");
        return false;
    }
    if (Object.keys(warnings.value).some((key) => warnings.value[key] !== "")) {
        if (!(await alertConfirmation("Are you sure you want to submit with current warnings?"))) {
            return false
        }
    }
    const obs = await Promise.all(Object.keys(appointmentInfoHandler).map((k) => {
        if (typeof appointmentInfoHandler?.[k]?.buildObs === 'function') {
            return appointmentInfoHandler[k].buildObs()
        }
        return null
    }).filter(Boolean))

    if (!obs.length) {
        toastDanger("No observations to save")
        return false
    }

    try {
        await appointmentService.createEncounter()
        await appointmentService.saveObservationList(obs)
        toastSuccess("Appointment saved!")
        return true
    } catch (e) {
        toastDanger("Error has occured while saving observations")
        console.error(e)
        return false
    }
}

// Handle date change event
function dateChanged(rawDate: any) {
    const date = dayjs(rawDate).format('YYYY-MM-DD')
    Object.keys(appointmentInfoHandler).forEach((k) => {
        if (typeof appointmentInfoHandler?.[k]?.onDateChange === 'function') {
            appointmentInfo.value[k].isLoading = true
            appointmentInfoHandler[k]
                .onDateChange(date)
                .finally(() => appointmentInfo.value[k].isLoading = false)
        }
    })
}

async function init() {
    const res = Object.keys(appointmentInfoHandler).map((key) => {
        if (typeof appointmentInfoHandler[key]?.init === 'function') {
            appointmentInfo.value[key].isLoading = true
            return appointmentInfoHandler[key].init()
                .catch((e: any) => {
                    appointmentInfo.value[key].val = "Error loading data..."
                    console.error(e)
                }).finally(() => appointmentInfo.value[key].isLoading = false)
        }
        return Promise.resolve() // Return a resolved promise for handlers without init
    })
    Promise.all(res).then(() => canSubmit.value = true)
}

onMounted(() => init())

defineExpose({
    onSubmit
})
</script>

<style scoped>
ion-datetime {
    --background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Ensure the highlighted dates are properly styled */
:deep(.calendar-day) {
    font-weight: 500;
}

/* This is handled by the highlightedDates property */

/* Warning section styling */
.warning-section {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
    background-color: rgba(255, 0, 0, 0.05);
}

.warning-section ion-note {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}
</style>
