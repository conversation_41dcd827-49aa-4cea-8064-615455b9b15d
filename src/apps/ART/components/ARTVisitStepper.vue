<template>
    <div class="stepper-container">
        <ion-row>
            <ion-col size="12">
                <div class="wizard-container">
                    <Wizard v-if="showWizard" ref="wizard" vertical-tabs navigable-tabs scrollable-tabs :doneButton="{
                        text: 'Finish',
                        icon: 'check',
                        hideText: false,
                        hideIcon: false,
                        disabled: false,
                    }" :custom-tabs="tabs" :beforeChange="onTabBeforeChange" @change="onChangeCurrentTab"
                        @complete:wizard="saveData()">
                        <div class="content-container">
                            <div class="back_button">
                                <ion-button fill="clear" @click="openModal" class="back-button">
                                    <ion-icon slot="start" :icon="chevronBackOutline"></ion-icon>
                                    <span>Back to profile</span>
                                </ion-button>
                            </div>
                            <HIVClinicRegistration v-if="currentTabIndex === 0 || !currentTabIndex" />
                            <Reception v-if="currentTabIndex === 1" />
                            <Vitals v-if="currentTabIndex === 2" />
                            <Staging v-if="currentTabIndex === 3" />
                            <Consultation v-if="currentTabIndex === 4" />
                            <Prescription v-if="currentTabIndex === 5" />
                        </div>
                    </Wizard>
                </div>
            </ion-col>
        </ion-row>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import {
    IonRow,
    IonCol,
    IonIcon,
    IonButton
} from '@ionic/vue';
import { chevronBackOutline } from 'ionicons/icons';
import Prescription from './Prescription.vue';
import Staging from './Staging.vue';
import HIVClinicRegistration from './ARTRegistration/HIVClinicRegistration.vue';
import Reception from './Reception/Reception.vue';
import Vitals from './Vitals.vue';
import Consultation from './Consultation.vue';
import Wizard from "form-wizard-vue3";

const showWizard = ref<boolean>(true);
const wizard = ref<any>(null);

interface TabItem {
    title: string;
}

const tabs = ref<TabItem[]>([
    { title: 'HIV Clinic Registration' },
    { title: 'Reception' },
    { title: 'Vitals' },
    { title: 'Staging' },
    { title: 'Consultation' },
    { title: 'Prescription' }
]);

const saveData = (): void => {
    console.log('Form completed');
};

const openModal = async (): Promise<void> => {
    console.log('Back to profile clicked');
};

interface FormWizardReturn {
    currentTabIndex: any;
    onChangeCurrentTab: (index: number, oldIndex: number) => void;
    onTabBeforeChange: () => boolean;
    changeBtnIconPosition: () => void;
}

const useFormWizard = (): FormWizardReturn => {
    const currentTabIndex = ref<number | string>("") as any;

    const onChangeCurrentTab = (index: number, _oldIndex: number): void => {
        if (index % 1 === 0) currentTabIndex.value = index;
    };

    const onTabBeforeChange = (): boolean => {
        if (currentTabIndex.value === 0) {
            console.log("First Tab");
        }

        console.log("All Tabs");
        return true;
    };

    const changeBtnIconPosition = (): void => {
        nextTick((): void => {
            const button = document.querySelector(".fw-footer-left .fw-btn") as HTMLElement;

            if (!button) return;

            const span = button.querySelector("span") as HTMLElement;
            const icon = button.querySelector("i") as HTMLElement;

            if (!span || !icon) return;

            button.removeChild(span);
            button.removeChild(icon);

            button.appendChild(icon);
            button.appendChild(span);
        });
    };

    return {
        currentTabIndex,
        onChangeCurrentTab,
        onTabBeforeChange,
        changeBtnIconPosition,
    }
};

const {
    currentTabIndex,
    onChangeCurrentTab,
    onTabBeforeChange,
} = useFormWizard();

onMounted((): void => {
    const formWizards: HTMLCollectionOf<Element> = document.getElementsByClassName("form-wizard-vue fw-vertical fw-overflow-scroll");
    if (formWizards.length > 0) {
        const formWizard: Element = formWizards[0];
        const ulElements: HTMLCollectionOf<Element> = formWizard.getElementsByClassName("fw-body-list");
        if (ulElements.length > 0) {
            const ulElement: Element = ulElements[0];
            const titleLi: HTMLLIElement = document.createElement("li");
            titleLi.textContent = "ART Visit";
            titleLi.style.textAlign = "center";
            titleLi.style.fontWeight = "700";
            titleLi.style.fontSize = "18px";
            titleLi.style.flexGrow = "0.08";
            ulElement.insertBefore(titleLi, ulElement.firstChild);
        }
    }
});
</script>

<style scoped>
#container {
    text-align: center;

    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

#container strong {
    font-size: 20px;
    line-height: 26px;
}

#container p {
    font-size: 16px;
    line-height: 22px;

    color: #8c8c8c;

    margin: 0;
}

#container a {
    text-decoration: none;
}

.centered-content {
    display: flex;
    justify-content: center;
    align-items: center;
}



.stepper-container {
    width: 95%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
}

.back-button-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 15px;
    padding-top: 10px;
}

.back-button {
    --padding-start: 4px;
    --padding-end: 12px;
    height: 36px;
    font-weight: 400;
    font-size: 14px;
}

.back-button ion-icon {
    font-size: 18px;
    margin-right: 4px;
}

.wizard-container {
    width: 100%;
    margin: 0 auto;
    margin-top: 10px;
}

.wizard_card {
    position: fixed;
    width: 100%;
    max-width: 300px;
    background-color: #fff;
    top: 150px;
}

.rightCol {
    top: 100px;
    width: 90%;
}

.accordion_group {
    position: fixed;
    height: 78vh;
    width: 58%;
    overflow-y: auto;
    top: 200px;
}

.accordion_group::-webkit-scrollbar {
    display: none;
}

.accordion_group {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

/* Responsive styles */
@media (max-width: 768px) {
    .stepper-container {
        width: 100%;
        padding: 0 10px;
    }

    .back-button-container {
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .back-button {
        --padding-end: 8px;
        font-size: 13px;
    }

    .back-button ion-icon {
        font-size: 16px;
    }
}
</style>