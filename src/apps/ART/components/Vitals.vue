<template>
  <div class="vitals-container">
    <div class="section-content">
      <div class="section-title">
        <h3>Height and weight</h3>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Height*</label>
          <div class="input-with-unit">
            <ion-input
              v-model="height"
              type="number"
              placeholder="Enter height"
              @ionChange="calculateBMI"
              required
            >
              <div slot="start">
                <ion-icon :icon="bodyOutline" class="input-icon"></ion-icon>
              </div>
            </ion-input>
            <div class="unit">cm</div>
          </div>
        </div>

        <div class="form-group">
          <label>Weight*</label>
          <div class="input-with-unit">
            <ion-input
              v-model="weight"
              type="number"
              placeholder="Enter weight"
              @ionChange="calculateBMI"
              required
            >
              <div slot="start">
                <ion-icon :icon="scaleOutline" class="input-icon"></ion-icon>
              </div>
            </ion-input>
            <div class="unit">kg</div>
          </div>
        </div>
      </div>

      <div v-if="bmiValue" class="bmi-result" :class="bmiClass">
        <ion-icon :icon="checkmarkCircleOutline" class="bmi-icon"></ion-icon>
        <span>BMI {{ bmiValue }} {{ bmiCategory }}</span>
      </div>
    </div>

    <div class="section-content">
      <div class="section-title">
        <h3>Blood pressure</h3>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Systolic pressure</label>
          <div class="input-with-unit">
            <ion-input
              v-model="systolic"
              type="number"
              placeholder="Enter systolic"
            >
              <div slot="start">
                <ion-icon :icon="heartOutline" class="input-icon"></ion-icon>
              </div>
            </ion-input>
            <div class="unit">mmHg</div>
          </div>
        </div>

        <div class="form-group">
          <label>Diastolic pressure</label>
          <div class="input-with-unit">
            <ion-input
              v-model="diastolic"
              type="number"
              placeholder="Enter diastolic"
            >
              <div slot="start">
                <ion-icon :icon="heartOutline" class="input-icon"></ion-icon>
              </div>
            </ion-input>
            <div class="unit">mmHg</div>
          </div>
        </div>
      </div>
    </div>

    <div class="section-content">
      <div class="section-title">
        <h3>Temperature and rates</h3>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Temperature</label>
          <div class="input-with-unit">
            <ion-input
              v-model="temperature"
              type="number"
              placeholder="Enter temperature"
            >
              <div slot="start">
                <ion-icon
                  :icon="thermometerOutline"
                  class="input-icon"
                ></ion-icon>
              </div>
            </ion-input>
            <div class="unit">°C</div>
          </div>
        </div>

        <div class="form-group">
          <label>Pulse rate</label>
          <div class="input-with-unit">
            <ion-input
              v-model="pulseRate"
              type="number"
              placeholder="Enter pulse rate"
            >
              <div slot="start">
                <ion-icon :icon="pulseOutline" class="input-icon"></ion-icon>
              </div>
            </ion-input>
            <div class="unit">BMP</div>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Respiratory rate</label>
          <div class="input-with-unit">
            <ion-input
              v-model="respiratoryRate"
              type="number"
              placeholder="Enter respiratory rate"
            >
              <div slot="start">
                <ion-icon :icon="fitnessOutline" class="input-icon"></ion-icon>
              </div>
            </ion-input>
            <div class="unit">BMP</div>
          </div>
        </div>

        <div class="form-group">
          <label>Oxygen saturation</label>
          <div class="input-with-unit">
            <ion-input
              v-model="oxygenSaturation"
              type="number"
              placeholder="Enter oxygen saturation"
            >
              <div slot="start">
                <ion-icon :icon="waterOutline" class="input-icon"></ion-icon>
              </div>
            </ion-input>
            <div class="unit">%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { IonInput, IonIcon } from "@ionic/vue";
import {
  chevronUp,
  bodyOutline,
  scaleOutline,
  heartOutline,
  thermometerOutline,
  pulseOutline,
  fitnessOutline,
  waterOutline,
  checkmarkCircleOutline,
} from "ionicons/icons";
import { VitalsService } from "@/services/vitals_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { Service } from "@/services/service";
import { BMIService } from "@/services/bmi_service";
import { storeToRefs } from "pinia";
import { getAge } from "@/utils/his_date";
import StandardValidations from "@/validations/StandardValidations";
import { toastDanger } from "@/utils/Alerts";

const patientStore = useDemographicsStore();
const { patient } = storeToRefs(patientStore);
const providerId = Service.getUserID() as number;
const vitalsService = new VitalsService(patient.value.patientID, providerId);

const height = ref<number>();
const weight = ref<number>();
const systolic = ref<number>();
const diastolic = ref<number>();
const temperature = ref<number>();
const pulseRate = ref<number>();
const respiratoryRate = ref<number>();
const oxygenSaturation = ref<number>();
const bmiValue = ref<number>();
const bmiCategory = ref<string>();

const age = computed(() => getAge(patient.value.personInformation.birthdate));
const gender = computed(() => patient.value.personInformation.gender);
const bmiClass = computed(() => bmiCategory.value?.replace(" ", "-"))

async function calculateBMI() {
  if (height.value && weight.value) {
    const bmi = await BMIService.getBMI(
      weight.value,
      height.value,
      gender.value,
      age.value
    );

    bmiValue.value = bmi.index;
    bmiCategory.value = bmi.result;
  }
}

async function validateForm () {
    const weightErrors = StandardValidations.vitalsWeight(weight.value);
    const heightErrors = StandardValidations.vitalsHeight(height.value);
    const systolicErrors = StandardValidations.vitalsSystolic(systolic.value);
    const diastolicErrors = StandardValidations.vitalsDiastolic(diastolic.value);
    const temperatureErrors = StandardValidations.vitalsTemperature(temperature.value);
    const respiratoryErrors = StandardValidations.vitalsRespiratoryRate(respiratoryRate.value);
    const oxygenErrors = StandardValidations.vitalsOxygenSaturation(oxygenSaturation.value);
    const pulseRateErrors = StandardValidations.vitalsPulseRate(pulseRate.value);

    console.log(
        "Vitals Validations Errors", 
        weightErrors, 
        heightErrors, 
        systolicErrors, 
        diastolicErrors, 
        temperatureErrors,
        respiratoryErrors,
        oxygenErrors,
        pulseRateErrors
    );
    return 
}

async function onSubmit(): Promise<boolean> {
  try {
    // validateForm();
    if(!weight.value || !height.value) throw new Error("Weight and Height are required")
    const encounter = vitalsService.createEncounter();
    if(!encounter) throw new Error("Unable to create Vitals encounter");
    const obs: any = [
        vitalsService.buildValueNumber("Height", height.value as number),
        vitalsService.buildValueNumber("Weight", weight.value as number),
        ...await vitalsService.buildBMIObs(weight.value as number, height.value as number, age.value)
    ];

    if(systolic.value && diastolic.value) {
        obs.push(vitalsService.buildValueNumber("Systolic", systolic.value as number));
        obs.push(vitalsService.buildValueNumber("Diastolic", diastolic.value as number));
    }

    if(temperature.value) obs.push(vitalsService.buildValueNumber("Temperature", temperature.value as number))
    if(pulseRate.value) obs.push(vitalsService.buildValueNumber("Pulse", pulseRate.value as number))
    if(respiratoryRate.value) obs.push(vitalsService.buildValueNumber("Respiratory rate", respiratoryRate.value as number))
    if(oxygenSaturation.value) obs.push(vitalsService.buildValueNumber("SAO2", oxygenSaturation.value as number))

    await vitalsService.saveObservationList(
        await Promise.all(obs)
    );
    return true;
  } catch (error) {
    toastDanger(`${error}`)
    console.error(error);
    return false;
  }
}

defineExpose({
  onSubmit,
  validateForm,
});
</script>

<style lang="css" scoped>
.vitals-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.collapse-icon {
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
}

.section-content {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e0e0e0;
}

.section-title h3 {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
  min-width: 250px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.input-with-unit {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
}

.input-icon {
  margin: 0 10px;
  color: #666;
}

ion-input {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 10px;
  --padding-bottom: 10px;
  --background: transparent;
  --color: #333;
  font-size: 1rem;
}

.unit {
  padding: 0 12px;
  background-color: #f5f5f5;
  color: #666;
  font-weight: 500;
  height: 100%;
  display: flex;
  align-items: center;
  border-left: 1px solid #ddd;
}

.bmi-result {
  margin-top: 15px;
  padding: 12px 15px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  background-color: #e8f5e9;
  color: #2e7d32;
}

.bmi-icon {
  font-size: 1.5rem;
}

.bmi-result.Severely-Underweight, 
.bmi-result.Moderately-Underweight,
.bmi-result.Mildly-Underweight {
  background-color: #fff8e1;
  color: #F9A639;
}

.bmi-result.Normal {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.bmi-result.overweight {
  background-color: #fff3e0;
  color: #ef6c00;
}

.bmi-result.Obese {
  background-color: #ffebee;
  color: #c62828;
}

.previous-measurements {
  margin-top: 5px;
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-group {
    min-width: 200px;
  }
}

@media (max-width: 576px) {
  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-group {
    width: 100%;
  }

  .section-header h2 {
    font-size: 1.3rem;
  }

  .section-title h3 {
    font-size: 1.1rem;
  }
}
</style>
