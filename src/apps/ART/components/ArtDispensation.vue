<template>
    <ion-card style="width: 90%; margin:0px auto;min-height: 40vh;">
        <ion-card-content>
            <div v-if="drugOrders.length">
                <ion-grid>
                    <ion-row style="font-weight: bold;">
                        <ion-col>Medication</ion-col>
                        <ion-col class="ion-text-center">Amount needed</ion-col>
                        <ion-col>Quantity</ion-col>
                    </ion-row>
                    <ion-row v-for="(order, index) in drugOrders" :key="index">
                        <ion-col>
                            <h3>{{ order.name }}</h3>
                        </ion-col>
                        <ion-col class="ion-text-center">
                            <ion-input fill="solid" readonly :value="order.amount_needed" />
                        </ion-col>
                        <ion-col>
                            <ion-input :disabled="order.disabled" v-model="order.quantity" type="number"
                                fill="outline"></ion-input>
                            <ion-text v-if="!order.disabled && dispensationStatus(order)" style="color:red;">
                                {{ dispensationStatus(order) }}
                            </ion-text>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </div>
            <div v-else>
                <div style="margin-top: 10%;" class="ion-text-center">
                    <ion-icon style="font-size: 5rem!important;" :icon="medkitOutline" />
                    <h1>No Dispensations needed at the moment</h1>
                </div>
            </div>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import {
    IonText,
    IonInput,
    IonCard,
    IonCardContent,
    IonIcon,
    IonGrid,
    IonRow,
    IonCol
} from "@ionic/vue"
import { DispensationService } from "../services/art_dispensing";
import { PatientService } from "@/services/patient_service";
import { ref, onMounted } from "vue";
import { medkitOutline } from "ionicons/icons";
import { toastDanger, toastWarning } from "@/utils/Alerts";

const drugOrders = ref<any>([])
const patientService = new PatientService()
const dispensationService = new DispensationService(patientService.getID(), -1)

onMounted(() => {
    dispensationService.loadCurrentDrugOrder()
        .then(() => {
            drugOrders.value = dispensationService.getCurrentOrder().map((d) => {
                return {
                    name: d.drug.name,
                    quantity: d.quantity,
                    drug_id: d.drug.drug_id,
                    order_id: d.order.order_id,
                    amount_needed: (() => {
                        const units = parseFloat(d.amount_needed) - (d.quantity || 0)
                        if (units <= 0) return 0
                        return dispensationService.calcCompletePack(d.order, units)
                    })(),
                    pack_sizes: dispensationService.getDrugPackSizes(d.drug.drug_id),
                    disabled: (d.quantity ?? 0) > 0
                }
            })
        })
})

function dispensationStatus(order: any) {
    if (!order.quantity) return ''
    const totalTabs = parseInt(`${order.quantity}`)
    const amountNeeded = order.amount_needed
    const percentageGiven = (totalTabs / amountNeeded) * 100

    if (percentageGiven > 110) {
        return 'You are dispensing over 110% of prescribed amount'
    }
    if (percentageGiven < 100) {
        return 'You are dispensing less than 100% of prescribed amount'
    }
}

async function onSubmit() {
    const dispenses = drugOrders.value.filter((o: any) => o.quantity >= 1 && !o.disabled)
        .map((o: any) => dispensationService.buildDispensations(o.order_id, parseInt(o.quantity), 1))

    if (dispenses.length <= 0) {
        toastWarning("Please enter one or more drug quantities")
        return false
    }

    const res = await Promise.all(dispenses.map((req: any) => dispensationService.saveDispensations(req)))
    if (!res.every(Boolean)) {
        toastDanger("An error has occured")
        return false
    }
    return true
}

defineExpose({
    onSubmit
})
</script>
<style scoped>
ion-col {
    border-right: 1px dotted #ccc;
}
</style>
