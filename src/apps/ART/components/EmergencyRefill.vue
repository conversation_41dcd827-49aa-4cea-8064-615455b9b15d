<template>
  <ion-card>
    <ion-card-content>
      <!-- Date of Emergency Refill -->
      <div class="ion-padding" style="border-bottom: dashed #ccc;">
        <ion-grid>
          <ion-row>
            <ion-col>
              <ion-label>Date of emergency refill</ion-label>
              <DatePicker :place_holder="'Select date'"
                @date-up-dated="(d) => dataHandler('date_of_emergency_refill', `${d.value.year}-${d.value.month}-${d.value.day}`)"
                :date_prop="''" />
              <div class="error">{{ validationMessages['date_of_emergency_refill'] }}</div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>

      <!-- Facility -->
      <div class="ion-padding" style="border-bottom: dashed #ccc;">
        <ion-grid>
          <ion-row>
            <ion-col>
              <VueMultiselect v-model="formData.selectedFacilityObj"
                @update:model-value="(val: any) => dataHandler('facility', val)" :multiple="false" :taggable="false"
                :close-on-select="true" openDirection="bottom" tag-placeholder="Find and select facility"
                placeholder="Select facility where emergency refill was collected" selectLabel="" label="label"
                :searchable="true" track-by="label"
                @search-change="(q: any) => formHandlers.facility.searchFacilities(q)" :options="facilities" />
              <div class="error">{{ validationMessages['facility'] }}</div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>

      <!-- Drug Details -->
      <div class="ion-padding" style="border-bottom: dashed #ccc;">
        <ion-label>Emergency Supply Drugs</ion-label>
        <div v-for="(drug, index) in formData.drugs" :key="index" class="drug-item">
          <ion-grid>
            <ion-row>
              <ion-col size="12">
                <ion-item lines="none">
                  <ion-label>{{ drug.label }}</ion-label>
                </ion-item>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col size="6">
                <ion-input v-model="drug.other.remainingAmount"
                  @ion-input="(e) => dataHandler('drugs', { index, field: 'remainingAmount', value: e.detail.value })"
                  placeholder="Pills Remaining" label="Pills Remaining" label-placement="stacked" type="number"
                  fill="outline"></ion-input>
              </ion-col>
              <ion-col size="6">
                <ion-input v-model="drug.other.givenAmount"
                  @ion-input="(e) => dataHandler('drugs', { index, field: 'givenAmount', value: e.detail.value })"
                  placeholder="Pills Given" label="Pills Given" label-placement="stacked" type="number"
                  fill="outline"></ion-input>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>
        <div class="error">{{ validationMessages['drugs'] }}</div>
      </div>

      <!-- Next Appointment -->
      <div class="ion-padding" style="border-bottom: dashed #ccc;">
        <ion-grid>
          <ion-row>
            <ion-col>
              <ion-label>Next Appointment Date</ion-label>
              <DatePicker :place_holder="'Select next appointment date'"
                @date-up-dated="(d) => dataHandler('next_appointment', `${d.value.year}-${d.value.month}-${d.value.day}`)"
                :date_prop="''" />
              <div class="error">{{ validationMessages['next_appointment'] }}</div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {
  IonCard, IonCardContent,
  IonItem, IonLabel, IonInput, IonGrid, IonRow, IonCol
} from '@ionic/vue';
import DatePicker from '@/components/DatePicker.vue';
import VueMultiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.css';
import { toastSuccess, toastDanger, toastWarning, alertConfirmation } from '@/utils/Alerts';
import dayjs from 'dayjs';
import { PatientService } from '@/services/patient_service';
import { AppEncounterService } from '@/services/app_encounter_service';
import { AdherenceService } from '@/apps/ART/services/adherence_service';
import { getFacilities } from '@/utils/HisFormHelpers/LocationFieldOptions';
// Initialize services
const patientService = new PatientService();
const emergencyRefillService = new AppEncounterService(patientService.getID(), 25, -1, '');
const adherenceService = new AdherenceService(patientService.getID(), emergencyRefillService.providerID);

// Form state
const formData = reactive<any>({
  date_of_emergency_refill: '',
  facility: '',
  drugs: [] as any[],
  next_appointment: '',
  selectedFacilityObj: null as any
});

const validationMessages = ref<any>({});
const facilities = ref<any>([]);

// Form handlers
const formHandlers: any = {
  date_of_emergency_refill: {
    required: () => true,
    dataHandler: (val: any) => {
      formData.date_of_emergency_refill = val;
    },
    validation: async () => {
      if (!formData.date_of_emergency_refill) {
        validationMessages.value['date_of_emergency_refill'] = 'Date is required';
        return;
      }

      if (dayjs(formData.date_of_emergency_refill).isAfter(dayjs())) {
        validationMessages.value['date_of_emergency_refill'] = 'Date cannot be in the future';
        return;
      }

      // Check if date is before last dispensation
      const lastDrugDate = adherenceService.getLastDrugs()?.[0]?.order?.start_date;
      if (lastDrugDate && dayjs(formData.date_of_emergency_refill).isBefore(lastDrugDate)) {
        await alertConfirmation(`Date entered is less than last known dispensation of ${dayjs(lastDrugDate).format('DD-MMM-YYYY')}`);
      }
    },
    buildObs: () => {
      return emergencyRefillService.buildValueDate('Prescription refill date', formData.date_of_emergency_refill);
    }
  },
  facility: {
    required: () => true,
    dataHandler: (val: any) => {
      formData.facility = val.label;
      formData.selectedFacilityObj = val;
    },
    searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res),
    validation: () => {
      if (!formData.facility) {
        validationMessages.value['facility'] = 'Facility is required';
      }
    },
    buildObs: () => {
      return emergencyRefillService.buildValueText('Health facility name', formData.facility);
    }
  },
  drugs: {
    required: () => true,
    dataHandler: (val: any) => {
      if (val.field === 'remainingAmount') {
        formData.drugs[val.index].other.remainingAmount = val.value;
      } else if (val.field === 'givenAmount') {
        formData.drugs[val.index].other.givenAmount = val.value;
      }
    },
    validation: () => {
      if (formData.drugs.some((drug: any) =>
        !drug.other.remainingAmount || !drug.other.givenAmount)) {
        validationMessages.value['drugs'] = 'Please enter both remaining and given amounts for all drugs';
      }
    },
    buildObs: () => {
      return formData.drugs.map(async (drug: any) => {
        const adherence = adherenceService.calculateAdherence(
          drug.other.drug.quantity,
          drug.other.remainingAmount,
          calcPillsExpected(drug.other.drug)
        );

        return {
          ...(await adherenceService.buildValueCoded('Medications dispensed', drug.value)),
          child: [
            await adherenceService.buildPillCountObs(
              drug.other.drug.order_id,
              drug.other.remainingAmount
            ),
            await adherenceService.buildAdherenceObs(
              drug.other.drug.order_id,
              drug.other.drug.drug_id,
              adherence,
              formData.date_of_emergency_refill
            ),
            await adherenceService.buildValueNumber(
              'Amount of drug dispensed',
              drug.other.givenAmount
            )
          ]
        };
      });
    }
  },
  next_appointment: {
    required: () => true,
    dataHandler: (val: any) => {
      formData.next_appointment = val;
    },
    validation: () => {
      if (!formData.next_appointment) {
        validationMessages.value['next_appointment'] = 'Next appointment date is required';
        return;
      }

      if (formData.date_of_emergency_refill &&
        dayjs(formData.next_appointment).isBefore(formData.date_of_emergency_refill)) {
        validationMessages.value['next_appointment'] = 'Next appointment date must be after emergency refill date';
      }
    },
    buildObs: () => {
      return emergencyRefillService.buildValueDate('Appointment date', formData.next_appointment);
    }
  }
};

// Helper function to calculate pills expected
function calcPillsExpected(drugData: any) {
  const formatFrequency = (frequency: string) => {
    return `${frequency}`.match(/qod/i)
      ? 'QOD'
      : `${frequency}`.match(/weekly/i)
        ? 'QW'
        : frequency;
  };

  return adherenceService.calculateExpected(
    drugData.quantity,
    drugData.equivalent_daily_dose,
    drugData.order.start_date,
    formatFrequency(drugData.frequency) as 'QOD' | 'QW'
  );
}

// Data handler
function dataHandler(field: string, value: any) {
  if (field in formHandlers && typeof formHandlers[field].dataHandler === 'function') {
    formHandlers[field].dataHandler(value);
  }

  runValidation(field);
}

// Validation
function runValidation(field: string) {
  validationMessages.value[field] = '';

  if (typeof formHandlers[field].required === 'function') {
    const required = formHandlers[field].required();
    if (required && !formData[field]) {
      validationMessages.value[field] = 'This field is required';
      return;
    }
  }

  if (formData[field] && typeof formHandlers[field].validation === 'function') {
    formHandlers[field].validation();
  }
}

// Build payload
function buildPayload() {
  return Object.keys(formData).reduce((a: any, c: any) => {
    if (c in formHandlers && formData[c] && typeof formHandlers[c].buildObs === 'function') {
      const obs = formHandlers[c].buildObs();
      if (Array.isArray(obs)) {
        return [...a, ...obs];
      }
      return [...a, obs];
    }
    return a;
  }, []);
}

// Form submission
async function onSubmit() {
  // Run validations
  Object.keys(formHandlers).forEach((key: string) => runValidation(key));
  const errorsFound = Object.keys(validationMessages.value).some((key) =>
    `${validationMessages.value[key]}`.length >= 1);

  if (errorsFound) {
    return toastWarning("Please review form for errors");
  }

  try {
    // Create encounter and save observations
    const encounter = await emergencyRefillService.createEncounter();
    if (!encounter) {
      return toastDanger("Failed to create encounter");
    }

    const payload = await Promise.all(buildPayload());
    await emergencyRefillService.saveObservationList(payload);

    toastSuccess("Emergency refill saved successfully");

    // Navigate back to patient dashboard (you'll need to implement this)
    // window.location.href = '/patientDashboard';
  } catch (error) {
    console.error('Error saving emergency refill data:', error);
    toastDanger("Failed to save emergency refill data");
  }
}

// Initialize form
async function initForm() {
  await adherenceService.loadPreviousDrugs();
  formData.drugs = adherenceService.getLastDrugs().map((data: any) => ({
    label: data.drug.name,
    value: data.drug.concept_id,
    other: {
      givenAmount: '',
      remainingAmount: '',
      drug: data
    }
  }));
}

// Initialize on component mount
onMounted(async () => {
  await initForm();
});

defineExpose({
  onSubmit
});
</script>

<style scoped>
.section-header ion-label {
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--ion-color-primary);
  text-transform: uppercase;
}

.drug-item {
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 10px;
}

.error {
  color: red;
  font-weight: bold;
  font-style: italic;
}
</style>
