<template>
    <div class="facility-selector">
        <ion-item>
            <ion-select
                :value="modelValue"
                @ion-change="handleChange"
                interface="popover"
                placeholder="Select a facility"
                :label="label"
                :label-placement="labelPlacement"
                :disabled="disabled"
                class="facility-select"
            >
                <ion-select-option v-if="facilities.length === 0 && isLoading" value="">
                    Loading...
                </ion-select-option>
                <ion-select-option v-if="facilities.length === 0 && !isLoading" value="">
                    No facilities found
                </ion-select-option>
                <ion-select-option v-for="facility in facilities" :key="facility.value" :value="facility.value">
                    {{ facility.label }}
                </ion-select-option>
            </ion-select>
            <ion-button v-if="!isLoading" fill="clear" slot="end" @click="refreshFacilities">
                <ion-icon :icon="refreshIcon" />
            </ion-button>
            <ion-spinner v-if="isLoading" name="dots" slot="end" />
        </ion-item>
        <div class="search-container" v-if="showSearch">
            <ion-searchbar
                placeholder="Search facilities"
                v-model="searchQuery"
                @ion-input="handleSearch"
                :debounce="300"
            ></ion-searchbar>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { IonItem, IonSelect, IonSelectOption, IonButton, IonIcon, IonSpinner, IonSearchbar } from '@ionic/vue';
import { LocationService } from '@/services/location_service';
import { refresh as refreshIcon } from 'ionicons/icons';

interface Facility {
    label: string;
    value: string;
}

const props = defineProps<{
    modelValue: string;
    label?: string;
    labelPlacement?: 'end' | 'start' | 'floating' | 'stacked' | 'fixed';
    disabled?: boolean;
    showSearch?: boolean;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string): void;
}>();

const facilities = ref<Facility[]>([]);
const isLoading = ref(false);
const searchQuery = ref('');

const fetchFacilities = async (filter = '') => {
    isLoading.value = true;
    try {
        // Use the getLabs method from LocationService to get lab facilities
        const response = await LocationService.getLabs({ search_name: filter });

        // Map the response to the format we need
        facilities.value = response.map((facility: any) => ({
            label: facility,
            value: facility
        }));
    } catch (error) {
        console.error('Error fetching facilities:', error);
        facilities.value = [];
    } finally {
        isLoading.value = false;
    }
};

const refreshFacilities = () => {
    searchQuery.value = '';
    fetchFacilities();
};

const handleChange = (event: CustomEvent) => {
    const value = event.detail.value;
    emit('update:modelValue', value);
    emit('change', value);
};

const handleSearch = (event: CustomEvent) => {
    const value = event.detail.value;
    fetchFacilities(value);
};

// Initialize component
onMounted(() => {
    fetchFacilities();
});

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
    if (newValue && !facilities.value.some(f => f.value === newValue)) {
        // If the current value is not in the list, fetch facilities again
        // This handles the case where the value is set programmatically
        fetchFacilities();
    }
});
</script>

<style scoped>
.facility-selector {
    width: 100%;
}

.facility-select {
    width: 100%;
}

.search-container {
    margin-top: 8px;
}
</style>
