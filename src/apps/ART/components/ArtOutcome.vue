<template>
    <div class="ion-padding">
        <h1>ART Outcome</h1>

        <!-- Error Message -->
        <ion-note v-if="errorMessage" color="danger" class="ion-margin-bottom">
            {{ errorMessage }}
        </ion-note>

        <!-- Program State Form -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>Patient Program State</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-grid>
                    <ion-row>
                        <ion-col size="12" size-md="6">
                            <ion-item>
                                <ion-label position="stacked">Program State *</ion-label>
                                <ion-select v-model="formData.programState" placeholder="Select program state"
                                    interface="popover" :disabled="isLoading">
                                    <ion-select-option v-for="state in programStates" :key="state.value"
                                        :value="state.value">
                                        {{ state.label }}
                                    </ion-select-option>
                                </ion-select>
                            </ion-item>
                        </ion-col>
                        <ion-col size="12" size-md="6">
                            <ion-item>
                                <ion-label position="stacked">Start Date</ion-label>
                                <ion-input :value="displayStartDate" readonly fill="outline"
                                    placeholder="Will be populated from program data" />
                            </ion-item>
                        </ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="12" size-md="6">
                            <ion-item>
                                <ion-label position="stacked">End Date</ion-label>
                                <DatePicker :place_holder="endDateProps.placeHolder"
                                    @date-up-dated="endDateProps.dataHandler" :date_prop="endDateDataValue.value" />
                            </ion-item>
                        </ion-col>
                        <ion-col size="12" size-md="6">
                            <ion-item>
                                <ion-label position="stacked">Reason (Optional)</ion-label>
                                <ion-textarea v-model="formData.reason" placeholder="Enter reason for state change"
                                    :disabled="isLoading" :rows="3"></ion-textarea>
                            </ion-item>
                        </ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="12">
                            <ion-button @click="saveOutcome" expand="block" :disabled="!isFormValid || isLoading"
                                color="primary">
                                <ion-spinner v-if="isLoading" name="crescent" slot="start"></ion-spinner>
                                {{ isLoading ? 'Saving...' : 'Save Outcome' }}
                            </ion-button>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card-content>
        </ion-card>

        <!-- Previous Outcomes Table -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>Previous Outcomes</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <div v-if="previousOutcomes.length === 0" class="ion-text-center ion-padding">
                    <ion-note>No previous outcomes found</ion-note>
                </div>
                <div v-else class="table-container">
                    <table class="outcomes-table">
                        <thead>
                            <tr>
                                <th>Program State</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="outcome in previousOutcomes" :key="outcome.id">
                                <td>{{ outcome.stateName }}</td>
                                <td>{{ formatDate(outcome.startDate) }}</td>
                                <td>{{ outcome.endDate ? formatDate(outcome.endDate) : 'Ongoing' }}</td>
                                <td>{{ calculateDuration(outcome.startDate, outcome.endDate) }}</td>
                                <td>
                                    <ion-badge :color="outcome.endDate ? 'medium' : 'success'">
                                        {{ outcome.endDate ? 'Completed' : 'Active' }}
                                    </ion-badge>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </ion-card-content>
        </ion-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import {
    IonContent,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonGrid,
    IonRow,
    IonCol,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption,
    IonTextarea,
    IonButton,
    IonNote,
    IonSpinner,
    IonBadge,
    IonInput
} from '@ionic/vue';
import DatePicker from '@/components/DatePicker.vue';
import { PatientProgramService } from '@/services/patient_program_service';
import { ProgramService } from '@/services/program_service';
import { useDemographicsStore } from '@/stores/DemographicStore';
import { toastSuccess, toastWarning } from '@/utils/Alerts';
import { toDisplayFmt } from '@/utils/his_date';
import dayjs from 'dayjs';

// Store and services
const demographicsStore = useDemographicsStore();
const patientId = computed(() => demographicsStore.patient.patientID);
const programId = computed(() => ProgramService.getProgramID());

// Reactive data
const isLoading = ref(false);
const errorMessage = ref('');
const formData = ref({
    programState: '',
    endDate: null as any,
    reason: ''
});

const previousOutcomes = ref<any[]>([]);
const programStartDate = ref('');

// Program states options
const programStates = ref([
    { value: 'On treatment', label: 'On treatment' },
    { value: 'Died', label: 'Died' },
    { value: 'Defaulted', label: 'Defaulted' },
    { value: 'Transferred out', label: 'Transferred out' },
    { value: 'Treatment stopped', label: 'Treatment stopped' },
    { value: 'Lost to follow-up', label: 'Lost to follow-up' },
    { value: 'Treatment completed', label: 'Treatment completed' }
]);

// Date picker properties for end date only
const endDateDataValue = ref(null as any);

const endDateProps = ref({
    placeHolder: { default: "Select end date" },
    dataValue: endDateDataValue,
    dataHandler: (data: any) => {
        endDateDataValue.value = data;
        formData.value.endDate = data;
    }
});

// Computed properties
const isFormValid = computed(() => {
    return formData.value.programState;
});

const displayStartDate = computed(() => {
    return programStartDate.value ? formatDate(programStartDate.value) : '';
});

// Methods
const loadProgramData = async () => {
    try {
        if (!patientId.value) return;

        const programService = new PatientProgramService(patientId.value);

        // Load program information to get start date
        const program = await programService.getProgram();
        if (program && program.startDate) {
            programStartDate.value = program.startDate;
        }

        // Load previous outcomes/states
        const states = await programService.getProgramStates();
        if (states && Array.isArray(states)) {
            previousOutcomes.value = states.map((state: any) => ({
                id: state.patient_state_id || state.id,
                stateName: state.name || state.state_name,
                startDate: state.start_date,
                endDate: state.end_date,
                stateId: state.state_id || state.program_workflow_state_id
            })).sort((a: any, b: any) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime());
        }
    } catch (error) {
        console.error('Error loading program data:', error);
        errorMessage.value = 'Failed to load program data';
    }
};

const saveOutcome = async () => {
    if (!isFormValid.value) {
        errorMessage.value = 'Please fill in all required fields';
        return;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
        const programService = new PatientProgramService(patientId.value);

        // Use current date as the state date (when the outcome is being recorded)
        const currentDate = new Date().toISOString().split('T')[0];
        programService.setStateDate(currentDate);

        // Find the state ID based on the selected program state
        const stateId = getStateIdByName(formData.value.programState);

        if (!stateId) {
            throw new Error('Invalid program state selected');
        }

        // Set the state ID
        programService.stateId = stateId;

        // Update the program state
        await programService.updateState();

        toastSuccess('Outcome saved successfully');

        // Reset form
        formData.value = {
            programState: '',
            endDate: null,
            reason: ''
        };

        // Reset date picker values
        endDateDataValue.value = null;

        // Reload program data
        await loadProgramData();

    } catch (error) {
        console.error('Error saving outcome:', error);
        errorMessage.value = 'Failed to save outcome. Please try again.';
        toastWarning('Failed to save outcome');
    } finally {
        isLoading.value = false;
    }
};

const getStateIdByName = (stateName: string): number | null => {
    // This mapping would typically come from the backend or a configuration file
    const stateMapping: { [key: string]: number } = {
        'On treatment': 7,
        'Died': 3,
        'Defaulted': 6,
        'Transferred out': 2,
        'Treatment stopped': 9,
        'Lost to follow-up': 6,
        'Treatment completed': 8
    };

    return stateMapping[stateName] || null;
};

const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    return toDisplayFmt(dateString);
};

const calculateDuration = (startDate: string, endDate?: string): string => {
    if (!startDate) return '';

    const start = dayjs(startDate);
    const end = endDate ? dayjs(endDate) : dayjs();
    const diffInDays = end.diff(start, 'day');

    if (diffInDays < 30) {
        return `${diffInDays} days`;
    } else if (diffInDays < 365) {
        const months = Math.floor(diffInDays / 30);
        return `${months} month${months > 1 ? 's' : ''}`;
    } else {
        const years = Math.floor(diffInDays / 365);
        const remainingMonths = Math.floor((diffInDays % 365) / 30);
        return `${years} year${years > 1 ? 's' : ''}${remainingMonths > 0 ? ` ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}` : ''}`;
    }
};

// Lifecycle
onMounted(() => {
    loadProgramData();
});
</script>

<style scoped>
.table-container {
    overflow-x: auto;
}

.outcomes-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.outcomes-table th,
.outcomes-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--ion-color-light);
}

.outcomes-table th {
    background-color: var(--ion-color-light);
    font-weight: 600;
    color: var(--ion-color-dark);
}

.outcomes-table tr:hover {
    background-color: var(--ion-color-light-tint);
}

.outcomes-table td {
    color: var(--ion-color-dark);
}

@media (max-width: 768px) {
    .outcomes-table {
        font-size: 0.9rem;
    }

    .outcomes-table th,
    .outcomes-table td {
        padding: 8px;
    }
}
</style>