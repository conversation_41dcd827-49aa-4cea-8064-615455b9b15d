<template>
  <ion-modal :is-open="isOpen" @didDismiss="closeModal" class="guardian-modal">
    <ion-header>
      <ion-toolbar>
        <ion-title class="modal-title">Search/Add Guardian</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeModal" class="close-button">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding modal-content">
      <div class="form-container">
        <div class="search-section">
          <ion-item class="search-item" lines="full">
            <ion-input
                v-model="searchQuery"
                placeholder="Search Guardian"
                @ionInput="handleSearchInput"
                class="search-input"
            >
              <ion-icon :icon="searchOutline" slot="start"></ion-icon>
            </ion-input>
            <ion-button
                slot="end"
                fill="clear"
                @click="showCreateForm = true"
                title="Create new guardian"
            >
              <ion-icon :icon="addOutline" />
              <ion-icon :icon="personOutline" />
            </ion-button>
          </ion-item>
          <div v-if="searchQuery && !showCreateForm" class="search-results">
            <div v-if="isSearching" class="search-loading">
              Searching...
            </div>
            <div v-else-if="combinedResults.length > 0">
              <div v-for="guardian in paginatedResults" :key="guardian.id" class="result-item">
                <div class="result-item-content" @click="toggleRelationshipSelect(guardian)">
                  <div class="guardian-name">
                    {{ guardian.firstName }} {{ guardian.lastName }}
                    <span v-if="guardian.relationship" class="relationship-badge">({{ guardian.relationship }})</span>
                  </div>
                  <div class="guardian-address" v-if="guardian.address">
                    {{ typeof guardian.address === 'object' ?
                      `${guardian.address.city_village}${guardian.address.township_division ? ', ' + guardian.address.township_division : ''}${guardian.address.state_province ? ', ' + guardian.address.state_province : ''}`
                      : 'Address available' }}
                  </div>
                </div>

                <!-- Relationship selector that appears when guardian is selected -->
                <div v-if="selectedGuardian?.id === guardian.id" class="relationship-selector">
                  <VueMultiselect
                      v-model="selectedRelationshipForSearch"
                      :options="relationshipOptions"
                      label="label"
                      track-by="value"
                      placeholder="Select relationship*"
                      :searchable="true"
                      :class="{ 'error-state': show_relationship_select_error }"
                      @input="show_relationship_select_error = false"
                  />
                  <ion-label
                      v-if="show_relationship_select_error"
                      class="error-label"
                      style="display: block; margin-top: 5px;"
                  >
                    Relationship is required
                  </ion-label>
                  <div class="relationship-actions">
                    <ion-button @click="confirmRelationshipSelection" size="small" class="confirm-btn">
                      Confirm
                    </ion-button>
                    <div class="spacer"></div>
                    <ion-button @click="cancelRelationshipSelection" fill="outline" size="small" class="cancel-btn">
                      Cancel
                    </ion-button>
                  </div>
                </div>
              </div>

              <div class="pagination">
                <button
                    @click="currentPage = Math.max(1, currentPage - 1)"
                    :disabled="currentPage === 1"
                    class="pagination-button"
                >
                  &lt;
                </button>
                <span class="page-info">Page {{ currentPage }} of {{ totalPages }}</span>
                <button
                    @click="currentPage = Math.min(totalPages, currentPage + 1)"
                    :disabled="currentPage === totalPages"
                    class="pagination-button"
                >
                  &gt;
                </button>
              </div>
            </div>

            <div v-else class="no-results">
              <p>No guardians found matching "{{searchQuery}}"</p>
            </div>
          </div>
        </div>
        <div v-if="showCreateForm" class="create-form-section">
          <ion-grid class="form-grid">
            <ion-row class="form-row">
              <ion-col class="form-column">
                <BasicInputField
                    inputHeader="First name*"
                    :inputValue="newGuardian.firstName"
                    @update:inputValue="handleFirstNameUpdate($event)"
                    inputType="text"
                    :error="show_first_name_error"
                />
                <ion-label v-if="show_first_name_error" class="error-label">
                  <span v-if="firstNameErrorReason === 'required'">First name is required</span>
                  <span v-else-if="firstNameErrorReason === 'invalid_chars'">First name can only contain letters and apostrophes</span>
                  <span v-else-if="firstNameErrorReason === 'too_short'">First name must be at least 3 characters</span>
                </ion-label>
              </ion-col>
              <ion-col class="form-column">
                <BasicInputField
                    inputHeader="Last name*"
                    :inputValue="newGuardian.lastName"
                    @update:inputValue="handleLastNameUpdate"
                    inputType="text"
                    :required="true"
                    :error="show_last_name_error"
                />
                <ion-label v-if="show_last_name_error" class="error-label">
                  <span v-if="lastNameErrorReason === 'required'">First name is required</span>
                  <span v-else-if="lastNameErrorReason === 'invalid_chars'">First name can only contain letters and apostrophes</span>
                  <span v-else-if="lastNameErrorReason === 'too_short'">First name must be at least 3 characters</span>                </ion-label>
              </ion-col>
            </ion-row>
            <div class="form-row">
              <ion-col class="form-column">
              <div class="form-group radio-group">
                <div class="radio-label-container">
                  <label class="label-title">Gender <span class="required">*</span></label>
                  <ion-radio-group
                      :value="newGuardian.gender"
                      @ionChange="handleGenderChange($event.detail.value)"
                      class="inline-radio-group"
                  >
                    <ion-item lines="none" class="inline-radio-item" style="--background: white">
                      <ion-radio slot="start" value="M"  style="margin-right: 16px"></ion-radio>
                      <ion-label>Male</ion-label>
                    </ion-item>
                    <ion-item lines="none" class="inline-radio-item" style="--background: white">
                      <ion-radio slot="start" value="F" style="margin-right : 16px" class="custom-radio"></ion-radio>
                      <ion-label>Female</ion-label>
                    </ion-item>
                  </ion-radio-group>
                  <ion-label v-if="show_gender_error" class="error-label">
                    Gender is required
                  </ion-label>
                </div>
              </div>
              </ion-col>
              <ion-col class="form-column">
                <ion-label position="stacked" style="font-weight: 500" class="labl-cls">District<span class="required"> *</span></ion-label>
                <VueMultiselect
                    v-model="selectedDistrict"
                    @update:model-value="handleDistrictChange"
                    :options="districtOptions"
                    label="name"
                    track-by="district_id"
                    placeholder="Select district"
                    :searchable="true"
                    :close-on-select="true"
                    :class="{ 'error-state': show_district_error }"
                />
                <ion-label v-if="show_district_error" class="error-label">
                  District is required
                </ion-label>
              </ion-col>
            </div>
            <!-- District and Traditional Authority Row -->
            <ion-row class="form-row">

              <ion-col class="form-column">
                <ion-label position="stacked" style="font-weight: 500" class="labl-cls">T/A or Area<span class="required"> *</span></ion-label>
                <VueMultiselect
                    v-model="selectedTraditionalAuthority"
                    @update:model-value="handleTraditionalAuthorityChange"
                    :options="traditionalAuthorityOptions"
                    label="name"
                    track-by="traditional_authority_id"
                    placeholder="Select traditional authority"
                    :searchable="true"
                    :close-on-select="true"
                    :disabled="!selectedDistrict"
                    :class="{ 'error-state': show_ta_error }"
                />
                <ion-label v-if="show_ta_error" class="error-label">
                  Traditional Authority/Area is required
                </ion-label>
              </ion-col>
              <ion-col class="form-column">
                <ion-label position="stacked" style="font-weight: 500" class="labl-cls">Village or Township<span class="required"> *</span></ion-label>
                <VueMultiselect
                    v-model="selectedVillage"
                    @update:model-value="handleVillageChange"
                    :options="villageOptions"
                    label="name"
                    track-by="village_id"
                    placeholder="Select village"
                    :searchable="true"
                    :close-on-select="true"
                    :disabled="!selectedTraditionalAuthority"
                    :class="{ 'error-state': show_village_error }"
                    :error="show_village_error"
                />
                <ion-label v-if="show_village_error" class="error-label">
                  Village/Township is required
                </ion-label>
              </ion-col>
            </ion-row>
            <!-- Village and Phone Row -->
            <ion-row class="form-row">
              <ion-col class="form-column">
                  <BasicPhoneInputField
                      inputHeader="Phone number"
                      :inputValue="newGuardian.phone"
                      @update:inputValue="handlePhoneUpdate"
                      inputType="tel"
                      :required="true"
                      :class="{ 'error-state': show_phone_error }"
                      :error="show_phone_error"
                  />
                <ion-label v-if="show_phone_error" class="error-label">
                  Phone must be 9 digits starting with 88, 99, 98, or 89
                </ion-label>
                </ion-col>
              <ion-col class="form-column">
                <ion-label position="stacked" style="font-weight: 500" class="labl-cls">Relationship<span class="required"> *</span></ion-label>
                <VueMultiselect
                    v-model="selectedRelationship"
                    @update:model-value="handleRelationshipChange"
                    :multiple="false"
                    :taggable="false"
                    :hide-selected="true"
                    :close-on-select="true"
                    openDirection="bottom"
                    placeholder="Select relationship"
                    selectLabel=""
                    label="label"
                    :searchable="true"
                    track-by="value"
                    :options="relationshipOptions"
                    :disabled="false"
                    :class="{ 'error-state': show_relationship_error }"
                    :error="show_relationship_error"
                />
                <ion-label v-if="show_relationship_error" class="error-label">
                  Relationship is required
                </ion-label>
              </ion-col>
            </ion-row>

          </ion-grid>
        </div>
        <ion-button
            v-if="showCreateForm"
            expand="block"
            @click="createGuardian"
            class="save-button"
        >
          <ion-icon :icon="saveOutline" slot="start"></ion-icon>
          Save
        </ion-button>
      </div>
    </ion-content>
  </ion-modal>
</template>
<script setup lang="ts">
import {ref, computed, watch, onMounted} from "vue";
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonItem,
  IonInput,
  IonGrid,
  IonRow,
  IonCol,
  IonLabel, IonSegment, IonSegmentButton
} from "@ionic/vue";
import {addOutline, personOutline, saveOutline, searchOutline} from "ionicons/icons";
import BasicInputField from "@/components/BasicInputField.vue";
import BasicPhoneInputField from "@/components/BasicPhoneInputField.vue";
import VueMultiselect from "vue-multiselect";
import { PatientService } from "@/services/patient_service";
import { RelationsService } from "@/services/relations_service";
import { PatientRegistrationService } from "@/services/patient_registration_service";
import { toastWarning, toastSuccess } from "@/utils/Alerts";
import {useDemographicsStore} from "@/stores/DemographicStore";
import {storeToRefs} from "pinia";
import {LocationService} from "@/services/location_service";
import {getOfflineRecords} from "@/services/offline_service";
import {useStatusStore} from "@/stores/StatusStore";

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
});
//store
const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;
const statusStore = useStatusStore();
const { apiStatus } = storeToRefs(statusStore);
const emit = defineEmits(['close', 'whenSaved','guardianSelected']);

// Search state
const searchQuery = ref("");
const searchResults = ref<any[]>([]);
const isSearching = ref(false);
const showCreateForm = ref(false);
const currentPage = ref(1);
const itemsPerPage = 10;
const combinedResults = ref<any[]>([]);
const existingRelationships = ref<any[]>([]);
const patientSearchResults = ref<any[]>([]);
const selectedRelationshipForSearch = ref<any>(null);
const selectedGuardian = ref<any>(null);


// Guardian form state
const newGuardian = ref({
  firstName: "",
  lastName: "",
  gender:"",
  phone: "",
  address: {},
  relationship: "",
});
const selectedDistrict = ref(null);
const selectedTraditionalAuthority = ref(null);
const selectedVillage = ref(null);
const districtOptions = ref<Array<{name: string, district_id: number}>>([]);
const traditionalAuthorityOptions = ref<Array<{name: string, traditional_authority_id: number}>>([]);
const villageOptions = ref<Array<{name: string, village_id: number}>>([]);
const show_district_error = ref(false);
const show_ta_error = ref(false);
const show_village_error = ref(false);
const show_gender_error = ref(false);
const relationshipOptions = ref<any[]>([]);
const selectedRelationship = ref(null);
const show_relationship_error = ref(false);
const show_first_name_error = ref(false);
const show_last_name_error = ref(false);
const show_phone_error = ref(false);
const show_relationship_select_error = ref(false);
const firstNameErrorReason = ref('');
const lastNameErrorReason = ref('');


// Computed properties
const totalPages = computed(() => Math.ceil(combinedResults.value.length / itemsPerPage));
const paginatedResults = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return combinedResults.value.slice(start, end);
});
const isNewGuardianValid = computed(() => {
  return (
      newGuardian.value.firstName.trim() !== "" &&
      newGuardian.value.lastName.trim() !== "" &&
      newGuardian.value.relationship
  );
});

//Mounted
onMounted(async () => {
  await loadDistricts();
  await fetchRelationships();
});

// Methods
const closeModal = () => {
  resetForm();
  emit('close');
};

const handleSearchInput = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    showCreateForm.value = false;
    searchGuardians();
  }, 300);
};

let searchTimeout: ReturnType<typeof setTimeout>;
const searchGuardians = async () => {
  if (!searchQuery.value.trim()) {
    combinedResults.value = [];
    currentPage.value = 1;
    return;
  }

  isSearching.value = true;

  try {
    // Search existing relationships/guardians
    const relationships = await PatientService.getJson(`people/${patient.value.patientID}/relationships`);
    existingRelationships.value = relationships.map((rel: any) => {
      // Skip if the relation is the patient themselves
      if (rel.relation?.person_id?.toString() === patient.value.patientID) {
        return null;
      }

      const name = rel.relation?.names?.[0] || {};
      const address = rel.relation?.addresses?.[0] || {};
      return {
        id: rel.relation?.person_id?.toString(),
        firstName: name.given_name || 'Unknown',
        lastName: name.family_name || '',
        phone: rel.relation?.person_attributes?.find((attr: any) => attr.value)?.value || '',
        relationship: rel.type?.b_is_to_a || 'Other',
        address: {
          city_village: address.city_village || '',
          township_division: address.township_division || '',
          state_province: address.state_province || ''
        },
        source: 'existing'
      };
    }).filter(Boolean);

    // Search all patients
    const searchTerms = searchQuery.value.split(" ");
    const searchPayload = {
      given_name: searchTerms[0],
      family_name: searchTerms.length >= 2 ? searchTerms[1] : "",
      page: "1",
      per_page: "20",
      exclude_patient_id: patient.value.patientID
    };

    const patients = await PatientService.search(searchPayload);
    patientSearchResults.value = patients
        .filter((p: any) => p.person.person_id.toString() !== patient.value.patientID)
        .map((patient: any) => {
          const name = patient.person.names[0];
          const address = patient.person.addresses[0] || {};
          return {
            id: patient.person.person_id.toString(),
            firstName: name.given_name,
            lastName: name.family_name,
            phone: patient.person.attributes?.find((a: any) => a.attribute_type === 'Cell Phone Number')?.value || '',
            relationship: '',
            address: {
              city_village: address.city_village || '',
              township_division: address.township_division || '',
              state_province: address.state_province || ''
            },
            source: 'search'
          };
        });

    combinedResults.value = [
      ...existingRelationships.value,
      ...patientSearchResults.value.filter(
          patient => !existingRelationships.value.some(rel => rel.id === patient.id)
      )
    ].sort((a, b) => {
      const nameA = `${a.firstName} ${a.lastName}`.toUpperCase();
      const nameB = `${b.firstName} ${b.lastName}`.toUpperCase();
      return nameA.localeCompare(nameB);
    });

    currentPage.value = 1;
  } catch (error) {
    toastWarning("Failed to search guardians");
  } finally {
    isSearching.value = false;
  }
};


const toggleRelationshipSelect = (guardian: any) => {
  if (guardian.relationship) {
    // If already has relationship, select immediately
    selectGuardian(guardian);
  } else {
    // Otherwise show relationship selector
    selectedGuardian.value = guardian;
    selectedRelationshipForSearch.value = null;
  }
};

const selectGuardian = async (guardian: any) => {
  try {
    const relationshipType = relationshipOptions.value.find(
        opt => opt.value === (guardian.relationship || selectedRelationshipForSearch.value?.value)
    );

    if (relationshipType) {
      await RelationsService.createRelation(
          patient.value.patientID,
          guardian.id,
          relationshipType.other.relationship_type_id
      );

      emit('guardianSelected', {
        ...guardian,
        relationship: relationshipType.value,
        address: guardian.address || {
          city_village: '',
          township_division: '',
          state_province: ''
        }
      });
      closeModal();
      toastSuccess("Guardian relationship created successfully");
    }
  } catch (error) {
    toastWarning("Failed to create guardian relationship");
  } finally {
    resetRelationshipSelection();
  }
};

const confirmRelationshipSelection = () => {
  show_relationship_select_error.value = !selectedRelationshipForSearch.value;

  if (!selectedRelationshipForSearch.value) {
    toastWarning("Please select a relationship");
    return;
  }
  selectGuardian(selectedGuardian.value);
};

const cancelRelationshipSelection = () => {
  resetRelationshipSelection();
};

const resetRelationshipSelection = () => {
  selectedGuardian.value = null;
  selectedRelationshipForSearch.value = null;
  show_relationship_select_error.value = false;
};

const loadDistricts = async () => {
  try {
    const offlineResponse = await getOfflineRecords("districts");
    const offlineDistricts = Array.isArray(offlineResponse)
        ? offlineResponse
        : offlineResponse?.records || [];

    districtOptions.value = offlineDistricts.map((d: any) => ({
      name: d.name,
      district_id: d.district_id || d.id
    }));

    if (districtOptions.value.length === 0 && apiStatus.value) {
      const onlineDistricts = await LocationService.getAllDistricts();
      districtOptions.value = onlineDistricts.map((d: any) => ({
        name: d.name,
        district_id: d.district_id || d.id
      }));
    }
  } catch (error) {
    console.error("District loading error:", error);
    districtOptions.value = [];
  }
};

const loadTraditionalAuthorities = async (district: any) => {
  try {
    const offlineResponse = await getOfflineRecords("TAs", {
      whereClause: { district_id: district.district_id }
    });
    const offlineTAs = Array.isArray(offlineResponse)
        ? offlineResponse
        : offlineResponse?.records || [];

    traditionalAuthorityOptions.value = offlineTAs.map((ta: any) => ({
      name: ta.name,
      traditional_authority_id: ta.traditional_authority_id || ta.id
    }));

    if (traditionalAuthorityOptions.value.length === 0 && apiStatus.value) {
      const onlineTAs = await LocationService.getTraditionalAuthorities(district.district_id);
      traditionalAuthorityOptions.value = onlineTAs.map((ta: any) => ({
        name: ta.name,
        traditional_authority_id: ta.traditional_authority_id || ta.id
      }));
    }
  } catch (error) {
    console.error("TA loading error:", error);
    traditionalAuthorityOptions.value = [];
  }
};

const loadVillages = async (ta: any) => {
  try {
    const offlineResponse = await getOfflineRecords("villages", {
      whereClause: { traditional_authority_id: ta.traditional_authority_id }
    });
    const offlineVillages = Array.isArray(offlineResponse)
        ? offlineResponse
        : offlineResponse?.records || [];

    villageOptions.value = offlineVillages.map((v: any) => ({
      name: v.name,
      village_id: v.village_id || v.id
    }));

    if (villageOptions.value.length === 0 && apiStatus.value) {
      const onlineVillages = await LocationService.getVillages(ta.traditional_authority_id);
      villageOptions.value = onlineVillages.map((v: any) => ({
        name: v.name,
        village_id: v.village_id || v.id
      }));
    }
  } catch (error) {
    console.error("Village loading error:", error);
    villageOptions.value = [];
  }
};
const validateAllFields = () => {
  // Check each field and set its error state
  const firstNameValidation = validateName(newGuardian.value.firstName);
  show_first_name_error.value = !firstNameValidation.valid;
  firstNameErrorReason.value = firstNameValidation.reason;
  const lastNameValidation = validateName(newGuardian.value.lastName);
  show_last_name_error.value = !lastNameValidation.valid;
  lastNameErrorReason.value = lastNameValidation.reason;
  show_gender_error.value = !newGuardian.value.gender;
  show_district_error.value = !selectedDistrict.value;
  show_ta_error.value = !selectedTraditionalAuthority.value;
  show_village_error.value = !selectedVillage.value;
  show_relationship_error.value = !newGuardian.value.relationship;

  // Return true only if ALL fields are valid

  return !(show_first_name_error.value ||
      show_last_name_error.value ||
      show_gender_error.value ||
      show_district_error.value ||
      show_ta_error.value ||
      show_village_error.value ||
      show_relationship_error.value);
};

const validateName = (name: string) => {
  const trimmed = name.trim();
  if (trimmed === '') return { valid: false, reason: 'required' };
  if (!/^[A-Za-z']+$/.test(trimmed)) return { valid: false, reason: 'invalid_chars' };
  if (trimmed.length < 3) return { valid: false, reason: 'too_short' };
  return { valid: true, reason: '' };
};

const handleFirstNameUpdate = (event: any) => {
  newGuardian.value.firstName = event.target.value;
  const validation = validateName(newGuardian.value.firstName);
  firstNameErrorReason.value = validation.reason;
  show_first_name_error.value = !validation.valid;
  return { valid: true, reason: '' };
};

const handleLastNameUpdate = (event: any) => {
  newGuardian.value.lastName = event.target.value;
  const validation = validateName(newGuardian.value.lastName);
  lastNameErrorReason.value = validation.reason;
  show_last_name_error.value = !validation.valid;
  return { valid: true, reason: '' };
};

const validatePhone = (phone: string) => {
  const regex = /^(88|99|98|89)\d{7}$/;
  return regex.test(phone.trim());
};

const handlePhoneUpdate = (event: any) => {
  newGuardian.value.phone = event.target.value;
  show_phone_error.value = !validatePhone(newGuardian.value.phone);
};

const handleGenderChange = (value: string) => {
  newGuardian.value.gender = value;
  show_gender_error.value = !value;
};

const handleDistrictChange = (value: any) => {
  selectedDistrict.value = value;
  show_district_error.value = !value;
  if (value) loadTraditionalAuthorities(value);
};

const handleTraditionalAuthorityChange = (value: any) => {
  selectedTraditionalAuthority.value = value;
  show_ta_error.value = !value;
  if (value) loadVillages(value);
};

const handleVillageChange = (value: any) => {
  selectedVillage.value = value;
  show_village_error.value = !value;
};

const handleRelationshipChange = (selectedOption: any) => {
  selectedRelationship.value = selectedOption;
  newGuardian.value.relationship = selectedOption?.value || '';
  show_relationship_error.value = !newGuardian.value.relationship;
};

const createGuardian = async () => {
  if (!validateAllFields()) {
    toastWarning("Please fill all required fields");
    return;
  }

  try {
    const villageName = (selectedVillage.value as any)?.name || '';
    const taName = (selectedTraditionalAuthority.value as any)?.name || '';
    const districtName = (selectedDistrict.value as any)?.name || '';

    // 1. Prepare guardian data without address
    const guardianData = {
      given_name: newGuardian.value.firstName,
      family_name: newGuardian.value.lastName,
      gender: newGuardian.value.gender,
      birthdate: "",
      birthdate_estimated: true,
      attributes: {
        cell_phone_number: newGuardian.value.phone,
      }
    };

    const guardianService = new PatientRegistrationService();
    const response = await guardianService.registerGuardian(guardianData);

    if (!response || !response.person_id) {
      throw new Error("Failed to create guardian - no person ID returned");
    }

    const guardianID = response.person_id;
    console.log('Guardian created with ID:', guardianID);

    // 2. Create address using the proper endpoint and format
    const addressData = {
      person_id: guardianID,
      address1: villageName,
      address2: taName,
      city_village: villageName,
      township_division: taName,
      state_province: districtName,
      country: "Malawi",
      preferred: 1,
      latitude: "",
      longitude: ""
    };

    let addressResponse;
    try {
      addressResponse = await LocationService.createAddress(addressData);
      if (!addressResponse) {
        addressResponse = await LocationService.postJson("person/" + guardianID + "/address", addressData);
      }

    } catch (addressError) {

    }

    // 3. Create relationship
    const selectedRel = relationshipOptions.value.find(
        r => r.value === newGuardian.value.relationship
    );

    if (selectedRel) {
      await RelationsService.createRelation(
          patient.value.patientID,
          guardianID,
          selectedRel.other.relationship_type_id
      );
    }

    // Prepare the saved guardian object
    const savedGuardian = {
      id: guardianID.toString(),
      firstName: newGuardian.value.firstName,
      lastName: newGuardian.value.lastName,
      phone: newGuardian.value.phone,
      relationship: newGuardian.value.relationship,
      address: {
        city_village: villageName,
        township_division: taName,
        state_province: districtName,
        country: "Malawi"
      }
    };

    emit('whenSaved', savedGuardian);
    closeModal();
    toastSuccess("Guardian created successfully");
  } catch (error) {
    toastWarning(`Failed to complete guardian creation: `);
  }
};

const fetchRelationships = async () => {
  try {
    const relationships = await RelationsService.getRelations();
    relationshipOptions.value = relationships.map((r: any) => ({
      label: r.b_is_to_a,
      value: r.b_is_to_a,
      other: r
    }));
  } catch (error) {
    console.error("Failed to load relationships", error);
  }
};

const resetForm = () => {
  newGuardian.value = {
    firstName: "",
    lastName: "",
    gender: "",
    phone: "",
    address: {},
    relationship: ""
  };

  selectedDistrict.value = null;
  selectedTraditionalAuthority.value = null;
  selectedVillage.value = null;
  selectedRelationship.value = null;
  searchQuery.value = "";
  searchResults.value = [];
  combinedResults.value = [];
  currentPage.value = 1;
  show_first_name_error.value = false;
  show_last_name_error.value = false;
  show_phone_error.value = false;
  show_gender_error.value = false;
  show_district_error.value = false;
  show_ta_error.value = false;
  show_village_error.value = false;
  show_relationship_error.value = false;
  show_relationship_select_error.value = false;
  firstNameErrorReason.value = '';
  lastNameErrorReason.value = '';
  showCreateForm.value = false;
  selectedGuardian.value = null;
  selectedRelationshipForSearch.value = null;
};

// Initialize
fetchRelationships();

watch(showCreateForm, (newVal) => {
  if (newVal) {
    searchResults.value = [];
  } else if (searchQuery.value) {
    searchGuardians();
  }
});
</script>

<style scoped>
.guardian-modal {
  --height: 800px;
  --max-height: 90vh;
  --border-radius: 8px;
  --width: 95%;
  --max-width: 800px;
}
.modal-content {
  height: 100%;
  overflow-y: auto;
}
.search-loading {
  padding: 16px;
  text-align: center;
  color: #666;
}

.search-results {
  max-height: 1000px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-top: 8px;
  transition: all 0.3s ease;
}

.result-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-item:hover {
  background-color: #f5f5f5;
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  border-top: 1px solid #eee;
}

.pagination-button {
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  margin: 0 8px;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 0.9rem;
  color: #555;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
  gap: 20px;
}


.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.required {
  color: red;
}


ion-modal {
  --width: 100%;
  --max-width: 800px;
  --height: 85%;
  --max-height: 1000px;
  --border-radius: 4px;
}
.modal-content {
  --padding-top: 16px;
  --padding-bottom: 16px;
}

.modal-title {
  font-size: 1.3rem;
  font-weight: 500;
  padding-left: 15px;
}

.close-button {
  --color: var(--ion-color-primary);
}

.modal-content {
  padding: 16px;
}

.section-header ion-label {
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--ion-color-primary);
  text-transform: uppercase;
}
.save-button {
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 500;
  width: 20%;
  margin-top: 100px;
}
ion-input::part(label),
ion-select::part(label) {
  font-weight: 600;
}
.form-container {
  background: white;
  border-radius: 4px;
  padding: 2px;
  height: 100%;
}

.form-grid {
  padding: 0;
}

.form-row {
  margin-bottom: 10px;
}

.form-column {
  padding: 0 5px;
}
.save-button {
  --border-radius: 3px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --background: #d4edda;
  margin-top: 20px;
  margin-left: auto;
  font-weight: 500;
  width: fit-content;
  display: block;
  color: darkgreen;
  height: 40px;
}
.search-section {
  margin-bottom: 40px;
  transition: all 0.3s ease;
}


.search-item ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.search-item ion-button ion-icon {
  font-size: 25px;
  margin: 0 2px;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  margin: 0;
  color: #666;
}

.no-results ion-button {
  --padding-start: 0;
  --padding-end: 0;
  margin: 0;
}

.result-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-item:hover {
  background-color: #f5f5f5;
}

.result-item:last-child {
  border-bottom: none;
}

.create-form-section {
  margin-top: 20px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.form-row {
  margin-bottom: 15px;
}

ion-segment-button {
  --color: #495057;
  --color-checked: #fff;
  --background-checked: #d4edda;
  --background: #f3f0f0;
  --indicator-color: transparent;
  --border-radius: 4px;
  --padding-top: 6px;
  --padding-bottom: 8px;
  --padding-right: 100px;
  --margin-start: 10px;
  --margin-end: 10px;
  min-width: 50px;
  font-size: 0.7rem;
  font-weight: 600;
  margin: 0 2px;
}

.required {
  color: #dc3545;
}


@media (max-width: 768px) {
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 16px;
    padding: 12px;
  }

  .pagination-button {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .pagination-button:hover:not(:disabled) {
    background: #e9ecef;
  }

  .pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .page-info {
    font-size: 0.9rem;
    color: #555;
  }
}
.error-label {
  color: #b42318;
  text-transform: none;
  padding: 3%;
  padding-top: 1%;
  padding-bottom: 1%;
  margin-top: 2px;
  display: flex;
  text-align: center;
}

.new-tag {
  color: #666;
  font-style: italic;
  font-size: 0.8em;
}


.result-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  justify-content: space-between;
}

.result-item:hover {
  background-color: #f5f5f5;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  border-top: 1px solid #eee;
}
.result-item-content {
  display: grid;
  grid-template-columns: 0.5fr 1.5fr;
  gap: 16px;
  width: 100%;
}

.guardian-name {
  font-weight: 500;
  text-align: left;
  height: 30px;
  background: #efefef;
}

.guardian-address {
  text-align: left;
  font-size: 0.9em;
  color: #666;
  word-break: break-word;
}

@media (max-width: 768px) {
  .result-item-content {
    grid-template-columns: 1fr;
    gap: 4px;
  }
}
.radio-group {
  display: flex;
  width: 100%;
}



.inline-radio-group {
  display: flex;
  gap: 30px;
}

.inline-radio-item {
  --inner-padding-end: 0;
  --padding-start: 0;
  margin: 0;
}
.relationship-selector {
  padding: 10px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
  width: 45%;
}

.relationship-dropdown {
  margin-bottom: 10px;
}

.relationship-actions {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.confirm-btn {
  --background: var(--ion-color-primary);
}

.cancel-btn {
  --color: var(--ion-color-primary);

}

.relationship-badge {
  font-size: 0.8em;
  color: #666;
  margin-left: 8px;
  font-style: italic;
}



.relationship-dropdown.error-state .multiselect__tags {
  border-color: #eb445a !important;
}

.error-label {
  color: #eb445a;
  font-size: 12px;
  margin-left: 5px;
}
.error-state {
  border: 1px solid #eb445a !important;
  border-radius: 4px;
}
.spacer {
  flex-grow: 1;
}

</style>