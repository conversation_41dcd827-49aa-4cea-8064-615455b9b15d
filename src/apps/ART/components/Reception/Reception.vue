<template>
  <div class="reception-form">
    <div class="form-section">
      <div class="form-row">
        <div class="form-group segment-group">
          <div class="segment-label-container">
            <label class="label-title">Patient present? <span class="required">*</span></label>
            <ion-label v-if="show_patient_present_error" class="error-label">
              Patient present is required
            </ion-label>
          </div>
          <ion-segment :value="patientPresent" @ionChange="handlePatientPresentChange($event.detail.value as string)"
            class="custom-segment">
            <ion-segment-button value="yes">
              <ion-label>Yes</ion-label>
            </ion-segment-button>
            <ion-segment-button value="no" :disabled="disablePatientNo">
              <ion-label>No</ion-label>
            </ion-segment-button>
          </ion-segment>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group segment-group">
          <div class="segment-label-container">
            <label class="label-title">Guardian present? <span class="required">*</span></label>
            <ion-label v-if="show_guardian_present_error" class="error-label">
              Guardian present is required
            </ion-label>
          </div>
          <ion-segment :value="guardianPresent" @ionChange="handleGuardianPresentChange($event.detail.value as string)"
            class="custom-segment">
            <ion-segment-button value="yes">
              <ion-label>Yes</ion-label>
            </ion-segment-button>
            <ion-segment-button value="no">
              <ion-label>No</ion-label>
            </ion-segment-button>
          </ion-segment>
        </div>
      </div>

      <div class="form-row" v-if="showCaptureARTNumber">
        <div class="form-group segment-group">
          <div class="segment-label-container">
            <label class="label-title">Capture ART number? <span class="required">*</span></label>
            <ion-label v-if="show_dispense_arv_error" class="error-label">
              Capture ART number is required
            </ion-label>
          </div>
          <ion-segment :value="dispenseARV" @ionChange="handleDispenseARVChange($event.detail.value as string)"
            class="custom-segment">
            <ion-segment-button value="yes">
              <ion-label>Yes</ion-label>
            </ion-segment-button>
            <ion-segment-button value="no">
              <ion-label>No</ion-label>
            </ion-segment-button>
          </ion-segment>
        </div>
      </div>
      <div class="form-row" v-if="dispenseARV === 'yes' && !hasARVNumber">
        <div class="form-group segment-group">
          <div class="segment-label-container">
            <label class="label-title">ART Number <span class="required">*</span></label>
            <ion-label v-if="show_arv_number_error" class="error-label">
              {{ arvNumberError }}
            </ion-label>
          </div>
          <div class="arv-input-container">
            <div class="input-group" :class="{ 'input-group-error': arvNumberHasError }">
              <input type="text" class="form-control" :class="{ 'has-error': arvNumberHasError }" v-model="arvNumber"
                @input="handleArvNumberInput" required />
              <span class="input-group-text" :class="{ 'has-error': arvNumberHasError }">
                {{ sitePrefix }}-ARV
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ManageGuardian :isOpen="showGuardianModal" :patientID="patient.patientID" @close="handleCloseGuardianModal"
      @guardianSelected="handleGuardianSelected" @whenSaved="handleGuardianSaved" />
  </div>
</template>

<script setup lang="ts">
import {
  IonButton,
  IonLabel,
  IonSegment,
  IonSegmentButton
} from "@ionic/vue";
import { Service } from "@/services/service";
import { ObsValue } from "@/services/observation_service";
import { toastWarning, toastSuccess } from "@/utils/Alerts";
import { computed, ref, watch } from "vue";
import { ReceptionService } from "@/apps/ART/services/reception_service";
import { storeToRefs } from "pinia";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useGlobalPropertyStore } from "@/stores/GlobalPropertyStore";
import { ProgramService } from "@/services/program_service";
import { PatientTypeService } from "@/apps/ART/services/patient_type_service";
import ManageGuardian from "@/apps/ART/components/Reception/ManageGuardian.vue";
import { PatientService } from "@/services/patient_service";


const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;
const globalPropertyStore = useGlobalPropertyStore();
const patientPresent = ref<string>("");
const guardianPresent = ref<string>("");
const dispenseARV = ref<string>("");
const showGuardianModal = ref(false);
const arvNumber = ref("");
const hasARVNumber = ref(true);
const sitePrefix = ref("");
const patientType = ref("");
const selectedGuardian = ref<any>(null);
const show_patient_present_error = ref(false);
const show_guardian_present_error = ref(false);
const show_dispense_arv_error = ref(false);
const show_arv_number_error = ref(false);
const arvNumberError = ref("");
const arvNumberHasError = ref(false);

watch(dispenseARV, (newValue) => {
  if (newValue === "no") {
    arvNumber.value = "";
    show_arv_number_error.value = false;
  }
});
watch([patientPresent, guardianPresent], ([newPatientPresent, newGuardianPresent]) => {
  if (newPatientPresent === "no" && newGuardianPresent === "no") {
    guardianPresent.value = "yes";
  }
});

//computed properties
const showCaptureARTNumber = computed(() => {
  return !hasARVNumber.value && patientType.value === "New patient";
});
const disablePatientNo = computed(() => guardianPresent.value === "no");


const handlePatientPresentChange = (value: string | undefined) => {
  if (value === undefined) return;
  patientPresent.value = value;
  show_patient_present_error.value = false;
  if (patientPresent.value === "no" && guardianPresent.value === "no") {
    patientPresent.value = "yes";
  }
};

const handleGuardianPresentChange = async (value: string | undefined) => {
  if (value === undefined) return;
  guardianPresent.value = value;
  show_guardian_present_error.value = false;

  if (value === "no" && patientPresent.value === "no") {
    patientPresent.value = "yes";
    toastWarning("Both patient and guardian cannot be absent.");
  }


  if (guardianPresent.value === "yes") {
    // Check if patient already has guardians
    try {
      const relationships = await PatientService.getJson(`people/${patient.value.patientID}/relationships`);
      const hasExistingGuardians = relationships.length > 0;

      if (!hasExistingGuardians) {
        showGuardianModal.value = true;
      } else {
        // Patient already has guardians, no need to show modal
        selectedGuardian.value = relationships[0].relation;
      }
    } catch (error) {
      console.error("Error checking relationships:", error);
      toastWarning("Failed to check existing guardians");
    }
  }
};

const handleDispenseARVChange = (value: string | undefined) => {
  if (value === undefined) return;
  dispenseARV.value = value;
  show_dispense_arv_error.value = false;
};

const handleCloseGuardianModal = () => {
  showGuardianModal.value = false;
  if (!selectedGuardian.value) {
    guardianPresent.value = "";
  }
};

const handleGuardianSelected = (guardian: any) => {
  selectedGuardian.value = guardian;
};
const handleGuardianSaved = (guardian: any) => {
  selectedGuardian.value = guardian;
  guardianPresent.value = "yes";
};
const handleArvNumberInput = () => {
  arvNumberHasError.value = false;
  show_arv_number_error.value = false;
  validateArvNumber();
};

const validateArvNumber = (): boolean => {
  show_arv_number_error.value = false;
  arvNumberError.value = "";
  arvNumberHasError.value = false;

  if (!arvNumber.value.trim()) {
    show_arv_number_error.value = true;
    arvNumberError.value = "ART number is required";
    arvNumberHasError.value = true;
    return false;
  }

  if (!/^\d+$/.test(arvNumber.value)) {
    show_arv_number_error.value = true;
    arvNumberError.value = "Only numbers are allowed";
    arvNumberHasError.value = true;
    return false;
  }

  if (parseInt(arvNumber.value) <= 0) {
    show_arv_number_error.value = true;
    arvNumberError.value = "Number must be positive";
    arvNumberHasError.value = true;
    return false;
  }

  return true;
};

const validateForm = (): boolean => {
  let isValid = true;

  if (!patientPresent.value) {
    show_patient_present_error.value = true;
    isValid = false;
  }

  if (!guardianPresent.value) {
    show_guardian_present_error.value = true;
    isValid = false;
  }

  if (showCaptureARTNumber.value) {
    if (!dispenseARV.value) {
      show_dispense_arv_error.value = true;
      isValid = false;
    }

    if (dispenseARV.value === 'yes' && !validateArvNumber()) {
      isValid = false;
    }
  }

  return isValid;
};
const formatArvNumber = (number: string): string => {
  const numericOnly = number.replace(/^.*-/, '');
  return `${sitePrefix.value}-ARV-${numericOnly}`;
};

const loadArvData = async () => {
  await globalPropertyStore.loadSitePrefix();
  sitePrefix.value = globalPropertyStore.globalPropertyStore.sitePrefix || "MPC";
  const typeService = new PatientTypeService(patientID, providerID);
  await typeService.loadPatientType();
  patientType.value = typeService.getType();

  const hasExistingArv = patient.value.identifiers?.some((i: any) =>
    i.identifier_type === 4 && i.identifier?.trim() !== ""
  );

  hasARVNumber.value = hasExistingArv;

  if (!hasExistingArv && patientType.value === "New patient") {
    const nextNum = await ProgramService.getNextSuggestedARVNumber();
    arvNumber.value = nextNum.arv_number.replace(/^\D+|\s/g, "");
  }
};

loadArvData();

const patientID = patient.value.patientID;
const providerID: any = Service.getUserID() || -1;
const locationID: any = Service.getUserLocation()
const receptionService = new ReceptionService(patientID, providerID, locationID);


const saveReception = async () => {
  if (!validateForm()) {
    toastWarning("Please correct the form errors");
    return false;
  }

  try {
    // Prepare observations data first
    const observations: ObsValue[] = [
      await receptionService.buildValueCoded(
        "Patient present",
        patientPresent.value === "yes" ? "Yes" : "No"
      ),
      await receptionService.buildValueCoded(
        "Responsible person present",
        guardianPresent.value === "yes" ? "Yes" : "No"
      )
    ];

    // Save ARV number first since it's most likely to fail
    if (dispenseARV.value === "yes") {
      const formattedArvNumber = formatArvNumber(arvNumber.value);
      const arvSaved = await receptionService.createArvNumber(formattedArvNumber);
      if (!arvSaved) {
        show_arv_number_error.value = true;
        arvNumberHasError.value = true;
        arvNumberError.value = "This ART number is already assigned to another patient";
        toastWarning("Failed to save ARV number");
        return false;
      }
    }

    // Create encounter only after ARV is saved
    const encounter = await receptionService.createEncounter();
    if (!encounter) {
      toastWarning("Failed to create encounter");
      return false;
    }

    // Save observations last
    const savedObs = await receptionService.saveObservationList(observations);
    if (!savedObs) {
      toastWarning("Failed to save observations");
      return false;
    }

    toastSuccess("Reception data saved successfully");
    return true;
  } catch (error: any) {
    if (error.response?.data?.error?.includes("already assigned")) {
      show_arv_number_error.value = true;
      arvNumberHasError.value = true;
      arvNumberError.value = "This ART number is already assigned to another patient";
      toastWarning("This ART number is already assigned to another patient");
    } else {
      console.error("Save error:", error);
      toastWarning("Failed to save reception");
    }
    return false;
  }
};

defineExpose({
  onSubmit: saveReception,
  validateForm
});
</script>

<style lang="css" scoped>
.reception-form {
  margin-bottom: 7px;
}

.form-section {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
  gap: 20px;
}

.form-group {
  flex: 1;
  min-width: 200px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.required {
  color: red;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  flex: 1;
}

.input-group-text {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-left: none;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  color: #555;
}

.form-footer {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
}

ion-modal {
  --width: 90%;
  --max-width: 700px;
  --height: 50%;
  --border-radius: 4px;
}

.section-header ion-label {
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--ion-color-primary);
  text-transform: uppercase;
}

ion-input::part(label),
ion-select::part(label) {
  font-weight: 600;
}

.form-row {
  margin-bottom: 10px;
}

.no-results p {
  margin: 0;
  color: #666;
}

.no-results ion-button {
  --padding-start: 0;
  --padding-end: 0;
  margin: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.label-title {
  font-weight: bold;
  font-size: 1rem;
  color: #555;
}

.form-row {
  margin-bottom: 15px;
}

.segment-group {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 4px;
  gap: 15px;
  position: relative;
}

.segment-group::after {
  content: "";
  flex: 0 0 30%;
  min-width: 300px;
  visibility: hidden;
}

.segment-label-container {
  min-width: 300px;
  font-weight: 800;
  flex: 0 40% 0;
}


ion-segment-button {
  --color: #495057;
  --color-checked: #fff;
  --background-checked: #d4edda;
  --background: #f3f0f0;
  --indicator-color: transparent;
  --border-radius: 4px;
  --padding-top: 6px;
  --padding-bottom: 8px;
  --padding-right: 100px;
  --margin-start: 10px;
  --margin-end: 10px;
  min-width: 50px;
  font-size: 0.7rem;
  font-weight: 600;
  margin: 0 2px;
}

.label-title {
  margin: 0;
  color: grey;
  font-size: 17px;
  font-weight: 600;
}

.required {
  color: #dc3545;
}

@media (max-width: 576px) {
  .segment-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .custom-segment {
    width: 50%;
    height: 20%;
    padding: 0 4px;
  }
}

.segment-group {
  display: flex;
  align-items: center;
  width: 100%;
}

.segment-label-container {
  width: 40%;
  min-width: 200px;
}


.arv-input-container {
  width: 100%;
}

.arv-input-container .input-group {
  width: 100%;
  height: 45px;
}

.arv-input-container .form-control {
  height: 100%;
  border-radius: 2px 0 0 2px;
}

.arv-input-container .input-group-text {
  height: 100%;
  border-radius: 0 4px 4px 0;
  padding: 0 16px;
}

@media (max-width: 768px) {
  .segment-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .segment-group::after {
    display: none;
  }

  .segment-label-container {
    width: 100%;
    margin-bottom: 8px;
  }

  .arv-input-container {
    margin-left: 0;
    width: 100%;
  }
}

.error-label {
  color: #b42318;
  font-size: 0.875rem;
  margin-top: 4px;
  display: block;
  font-weight: 300 !important;
}

.arv-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.arv-input-container .input-group-text {
  padding: 0 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  height: 45px;
  display: flex;
  align-items: center;
}

.has-error {
  border: 1px solid #b42318 !important;
  box-shadow: 0 0 0 1px #b42318 !important;
}

.input-group-error {
  border-radius: 4px;
  box-shadow: 0 0 0 1px #b42318;
}

.input-group-text.has-error {
  border-left: 1px solid #b42318 !important;
  border-color: #b42318 !important;
}

.error-label {
  color: #b42318;
  font-size: 0.875rem;
  margin-top: 4px;
  display: block;
  font-weight: 300 !important;
}

.input-group {
  border-radius: 4px;
  overflow: hidden;
}

.input-group .form-control {
  border: 1px solid #ddd;
  border-right: none;
}

.input-group .input-group-text {
  border: 1px solid #ddd;
  border-left: none;
}
</style>
