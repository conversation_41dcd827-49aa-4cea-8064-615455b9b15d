<template>
    <ion-card style="height: 65vh;">
        <ion-card-content>
            <div>
                <h2 style="font-weight: bold; margin:12px;">Patient Type <span style="color: red">*</span></h2>
                <ion-radio-group v-model="formData.patientType">
                    <ion-item v-for="(option, index) in patientTypeOptions" :key="index">
                        <ion-radio slot="start" :value="option.value"></ion-radio>
                        <ion-label style="margin: 10px;">{{ option.label }}</ion-label>
                    </ion-item>
                </ion-radio-group>
            </div>
            <div v-if="formData.prevType !== formData.patientType && !/new patient/i.test(formData.patientType)"
                class="ion-padding">
                <h2 style="font-weight: bold; margin:12px;">Facility <span style="color: red">*</span></h2>
                <VueMultiselect v-model="formData.transerFacility" :multiple="false" :taggable="false"
                    :hide-selected="true" :close-on-select="true" openDirection="bottom"
                    @search-change="searchFacilities" tag-placeholder="Find and select facility"
                    placeholder="Find and select facility" selectLabel="" label="name" :searchable="true"
                    track-by="code" :options="facilityList" />
            </div>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import {
    IonItem,
    IonLabel,
    IonCard,
    IonCardContent,
    IonRadio,
    IonRadioGroup
} from "@ionic/vue"
import { onMounted, reactive, ref } from "vue";
import { LocationService } from "@/services/location_service";
import VueMultiselect from "vue-multiselect";
import { PatientTypeService } from "../services/patient_type_service";
import { toastSuccess, toastWarning } from "@/utils/Alerts";
import { PatientService } from "@/services/patient_service";

const isLoading = ref(false)
const patientTypeOptions = ref<any>([]);
const facilityList = ref([]);
const patientService: any = new PatientService()
const patientTypeService = new PatientTypeService(patientService.getID(), -1);

const formData = reactive<any>({
    patientType: null,
    prevType: null,
    transerFacility: null
})

// Search facilities
async function searchFacilities(query = "") {
    try {
        const facilities = await LocationService.getFacilities({ name: query });
        facilityList.value = facilities;
    } catch (error) {
        console.error("Error fetching facilities:", error);
    }
}

async function onSubmit(): Promise<boolean> {
    if (formData.patientType === formData.prevType) return true;
    if (!formData.patientType) {
        toastWarning("Patient type is required");
        return false;
    }
    if (!/new patient/i.test(formData.patientType) && formData.patientType !== formData.prevType && !formData.transerFacility) {
        toastWarning("Facility is required");
        return false;
    }

    patientTypeService.setPatientType(formData.patientType);
    patientTypeService.setLocationName(formData.transerFacility?.name || "");

    try {
        const res = await patientTypeService.save();
        if (res) {
            toastSuccess("Patient type saved successfully");
            return true;
        } else {
            toastWarning("Failed to save patient type");
            return false;
        }
    } catch (e) {
        toastWarning(`Failed to save patient type: ${e}`);
        return false;
    }
}

onMounted(() => {
    isLoading.value = true;
    patientTypeOptions.value = PatientTypeService.getPatientTypes();
    patientTypeService.loadPatientType()
        .then(() => {
            const type = patientTypeService.getType()
            if (type && !/n\/a/i.test(type)) {
                formData.prevType = patientTypeService.getType()
                formData.patientType = patientTypeService.getType();
            }
        }).finally(() => {
            isLoading.value = false;
        });
})

defineExpose({
    onSubmit
})
</script>