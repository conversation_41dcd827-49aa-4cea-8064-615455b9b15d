<template>
    <!-- Confirmatory Test & Location -->
    <ion-row>
        <ion-col size="12" size-md="6">
            <ion-label position="stacked" class="labl-cls"> Confirmatory Test </ion-label>
            <VueMultiselect
                v-model="confirmatoryTest"
                @update:model-value="handleConfirmatoryTestChange($event)"
                :multiple="false"
                :taggable="false"
                :hide-selected="true"
                :close-on-select="true"
                openDirection="bottom"
                tag-placeholder=""
                placeholder=""
                selectLabel=""
                label="name"
                :searchable="true"
                @search-change=""
                track-by="name"
                :options="confirmatoryTestOptions"
                :disabled="false"
                :class="{ 'error-state': show_confirmatory_test_error }"
            />
            <div>
                <ion-label v-if="show_confirmatory_test_error" class="error-label">
                    {{ "Select Confirmatory Test" }}
                </ion-label>
            </div>
        </ion-col>

        <ion-col size="12" size-md="6">
            <ion-label class="labl-cls">Location of ART Initiation</ion-label>
            <div style="margin-top: -5px;">
                <SelectFacility
                    :show_error="show_location_error"
                    @facility-selected="facilitySelected"
                    :selected_district_ids="selectedDistrictIds"
                    :selected_location="selected_location"
                />
            </div>
        </ion-col>
    </ion-row>

    <!-- Confirmatory HIV Test Date -->
    <ion-row>
        <ion-col size="12" size-md="6">
            <ion-label position="stacked" class="labl-cls"> Confirmatory HIV Test Date </ion-label>
            <DatePicker :place_holder="'enter date'" @date-up-dated="dateUpdated" :date_prop="''" :error="show_date_error"/>
            <div>
                <ion-label v-if="show_date_error" class="error-label">
                    {{ date_error_message }}
                </ion-label>
            </div>
        </ion-col>
    </ion-row>
</template>

<script setup lang="ts">
import { ref } from "vue";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";
import VueMultiselect from "vue-multiselect";
import {
    IonLabel,
    IonRow,
    IonCol,
} from "@ionic/vue";
import { ARTClinicRegistrationService } from '../../services/art_clinic_registration_service';
import { toastWarning, toastSuccess } from "@/utils/Alerts";

const show_location_error = ref(false) as any;
const FacilityData = ref(null) as any
const selectedDistrictIds = ref([]) as any;
const selected_location = ref({}) as any;

const confirmatoryTest = ref(null) as any;
const show_confirmatory_test_error = ref(false) as any;

const date = ref({}) as any;
const show_date_error = ref(false);
const date_error_message = "Date is required";

const confirmatoryTestOptions =
    [{ name: 'Rapid antibody test', value: 'HIV rapid test'},
    { name: 'DNA PCR', value: 'HIV DNA polymerase chain reaction'},
    { name: 'Not done', value: 'Not done', disabled: "f.has_linkage_code === 'Yes'" }]

const facilitySelected = (data: any) => {
    FacilityData.value = data.selected_location;
    validateFacility();
};

function validateFacility() {
    if (FacilityData.value) {
        show_location_error.value = false
    } else {
        show_location_error.value = true    
    }
}

function handleConfirmatoryTestChange(event: any) {
    // console.log("Selected confirmatory test:", event);
    confirmatoryTest.value = event;
    show_confirmatory_test_error.value = false;
    validateConfirmatoryTest();
}

function validateConfirmatoryTest() {
    if (confirmatoryTest.value === null || confirmatoryTest.value === "") {
        show_confirmatory_test_error.value = true;
    } else {
        show_confirmatory_test_error.value = false;
    }
}

function dateUpdated(data: any) {
    const date_data = {
        day: data.value.day,
        month: data.value.month,
        year: data.value.year,
        formattedDate: data.value.formattedDate,
        standardDate: data.value.standardDate,
    };
    date.value = date_data;
    show_date_error.value = false;
    validateDate()
}

function validateDate() {
    show_date_error.value = false;
    if (Object.keys(date.value).length === 0) {
        // console.log('The date object is empty');
        show_date_error.value = true;
    } 

    if (Object.keys(date.value).length > 0) {
        show_date_error.value = false;
    }
}

// Add service initialization
const props = defineProps<{
    patientId: number;
    providerId: number;
    allvalid: boolean;
}>();

const artService = new ARTClinicRegistrationService(props.patientId, props.providerId);

const computeConfirmation = async () => {
    if (!confirmatoryTest.value || !FacilityData.value || !date.value) return;

    try {
        return {
            testType: await artService.computeConfirmatoryHIVTestType({
                value: confirmatoryTest.value.value
            } as any),
            location: await artService.computeConfirmatoryHIVTestLocation({
                label: FacilityData.value.code
            } as any),
            testDate: await artService.computeConfirmatoryHIVTestDate(
                date.value.standardDate,
                false
            )
        };
    } catch (error) {
        console.error("Error computing HIV confirmation:", error);
        throw error;
    }
};

const saveValues = async () => {
    try {
        const confirmationData = await computeConfirmation();
        if (!confirmationData) return false;

        // Create encounter and submit
        await artService.createEncounter();
        await artService.onSubmit([
            confirmationData.testType.obs,
            confirmationData.location.obs,
            confirmationData.testDate.obs
        ]);

        toastSuccess("HIV confirmation details saved successfully");
        return true;
    } catch (error) {
        console.error("Error saving HIV confirmation:", error);
        toastWarning("Error saving HIV confirmation. Please try again.");
        throw error;
    }
};

const validateAndGetValues = async () => {
    validateConfirmatoryTest();
    validateFacility();
    validateDate();

    const isValid = !show_confirmatory_test_error.value && 
                   !show_location_error.value &&
                   !show_date_error.value;

    if (!isValid) {
        toastWarning("Please complete all required fields correctly");
    }

    let computedData = null;
    if (isValid) {
        try {
            computedData = await computeConfirmation();
        } catch (error) {
            console.error('Error computing confirmation:', error);
        }
    }

    return {
        isValid,
        values: {
            confirmatoryTest: confirmatoryTest.value,
            facilityData: FacilityData.value,
            date: date.value
        },
        computedObs: computedData
    };
};

defineExpose({
    validateAndGetValues,
    saveValues
});

</script>

<style scoped lang="css">

/* White background styles */
.white-background {
    background-color: white;
}

.radio-item {
    --background: white;
    --border-color: transparent;
}

.left-label-item {
    --background: white;
    --border-color: transparent;
}

/* Label styles */
.left-label-item ion-label {
    width: 100%;
    text-align: left;
    margin-right: 10px;
}

/* Required field indicator */
.required {
    color: red;
}

/* Radio group spacing */
.radio-group {
    margin-left: 15px;
}

.radio-label {
    font-size: 16px;
    font-weight: 600;
}

.radio-btn {
    margin-left: 20px;
    margin-right: 10px;
}

.labl-cls {
    margin: 10px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 10px;
    color: grey;
    font-size: 17px;
    font-weight: 600;
}

/* VueMultiselect custom styles */
::v-deep(.multiselect__input) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__placeholder) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__single) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__option) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

.yn-row {
    margin-top: 10px;
    margin-bottom: 5px;
}

.error-label {
  color: #b42318;
  text-transform: none;
  padding: 3%;
  padding-top: 1%;
  padding-bottom: 1%;
  margin-top: 2px;
  display: flex;
  text-align: center;
}

.error-state {
    border: none !important;  /* Remove default border first */
    outline: none !important; /* Remove any outline */
    box-shadow: 0 0 0 1px #b42318 !important; /* Use box-shadow instead of border */
    border-radius: 4px;
}

/* Add these new selectors to override VueMultiselect's default styles */
.error-state .multiselect__tags {
    border: none !important;
    box-shadow: none !important;
}

.error-state .multiselect__select {
    border: none !important;
}

.error-state.multiselect--active {
    border: none !important;
    box-shadow: 0 0 0 1px #b42318 !important;
}
</style>