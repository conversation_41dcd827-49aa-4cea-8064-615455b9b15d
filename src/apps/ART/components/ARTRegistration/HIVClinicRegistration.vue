<template>
    <ion-card>
        <ion-card-content>
            <ion-grid>
                <ARTIdentification :patient-id="patientID" :provider-id="providerId" :allvalid="allValid"
                    ref="artIdentificationRef" />
                <!-- Agrees to follow up & ARVs History -->
                <ARTHistory :patient-id="patientID" :provider-id="providerId" :allvalid="allValid"
                    ref="artHistoryRef" />
                <!-- Date started ART, Initial Weight, Initial Height -->
                <InitialHealthStatus :patient-id="patientID" :provider-id="providerId" :allvalid="allValid"
                    ref="initiialHealthStatusRef" />
                <!-- HTS Linkage Number -->
                <HTSLinkageNumber :patient-id="patientID" :provider-id="providerId" :allvalid="allValid"
                    ref="linkageNumberRef" />
                <!-- HIV Confirmation -->
                <HIVConfirmation :patient-id="patientID" :provider-id="providerId" :allvalid="allValid"
                    ref="HIVConfirmationRef" />


                <!-- Submit Button -->
                <ion-row class="ion-margin-top" v-if="false">
                    <ion-col>
                        <ion-button expand="block" color="success" @click="onSubmit">Submit
                            Registration</ion-button>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </ion-card-content>
    </ion-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useDemographicsStore } from "@/stores/DemographicStore";
import HTSLinkageNumber from "./HTSLinkageNumber.vue";
import ARTIdentification from "./ARTIdentification.vue";
import ARTHistory from "./ARTHistory.vue";
import InitialHealthStatus from "./InitialHealthStatus.vue";
import HIVConfirmation from "./HIVConfirmation.vue";
import { Service } from "@/services/service";
import { toastSuccess, toastWarning } from "@/utils/toasts";

import {
    IonCard,
    IonCardContent,
    IonRow,
    IonCol,
    IonGrid,
} from "@ionic/vue";

// store references
const demographicsStore = useDemographicsStore();

// Destructure store refs
const { patient } = storeToRefs(demographicsStore) as any;

const patientID = patient.value.patientID;
const providerId = Service.getUserID() || -1;


const linkageNumberRef = ref();
const artIdentificationRef = ref();
const artHistoryRef = ref();
const initiialHealthStatusRef = ref();
const HIVConfirmationRef = ref();
const tbRef = ref();

const allValid = ref(false);

async function onSubmit() {
    try {
        // Run all validations and collect results
        const [
            linkageResult,
            identificationResult,
            historyResult,
            healthStatusResult,
            hivConfirmationResult
        ] = await Promise.all([
            linkageNumberRef.value.validateLinkageCode(),
            artIdentificationRef.value.validateAndGetValues(),
            artHistoryRef.value.validateAndGetValues(),
            initiialHealthStatusRef.value.validateAndGetValues(),
            HIVConfirmationRef.value.validateAndGetValues()
        ]);

        // Check if all validations passed
        allValid.value =
            linkageResult.isValid &&
            identificationResult.isValid &&
            historyResult.isValid &&
            healthStatusResult.isValid &&
            hivConfirmationResult.isValid;

        if (allValid.value) {
            // All validations passed - save the data
            linkageNumberRef.value.saveLinkageCode()
            artIdentificationRef.value.saveValues();
            artHistoryRef.value.saveValues();
            initiialHealthStatusRef.value.saveValues();
            HIVConfirmationRef.value.saveValues();
            toastSuccess("Registration completed successfully");

        } else {
            // Show error message if any validation failed
            toastWarning("Please complete all required fields correctly");
        }

        return allValid.value;

    } catch (error) {
        console.error('Validation error:', error);
        // toastError("An error occurred during validation");
        return false;
    }
}

defineExpose({
    onSubmit
});
</script>

<style scoped>
.required {
    color: #eb445a;
}

ion-card {
    max-width: 1200px;
    margin: 0 auto;
}

ion-card-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

ion-item {
    --padding-start: 0;
}

ion-radio-group ion-item {
    --padding-start: 16px;
}

ion-button {
    --background: #3880ff;
    --color: white;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    ion-label {
        font-size: 0.9rem;
    }

    ion-card-title {
        font-size: 1.2rem;
    }
}

/* White background styles */
.white-background {
    background-color: white;
}

.radio-item {
    --background: white;
    --border-color: transparent;
}

.left-label-item {
    --background: white;
    --border-color: transparent;
}

/* Label styles */
.left-label-item ion-label {
    width: 100%;
    text-align: left;
    margin-right: 10px;
}

/* Required field indicator */
.required {
    color: red;
}

/* Radio group spacing */
.radio-group {
    margin-left: 15px;
}

.radio-label {
    font-size: 16px;
    font-weight: 600;
}

.radio-btn {
    margin-left: 20px;
    margin-right: 10px;
}

.labl-cls {
    margin: 10px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 10px;
    color: grey;
    font-size: 17px;
    font-weight: 600;
}

/* VueMultiselect custom styles */
:deep(.multiselect__input) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

:deep(.multiselect__placeholder) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

:deep(.multiselect__single) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

:deep(.multiselect__option) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

.yn-row {
    margin-top: 10px;
    margin-bottom: 5px;
}

.error-label {
    color: #b42318;
    text-transform: none;
    padding: 3%;
    padding-top: 1%;
    padding-bottom: 1%;
    margin-top: 2px;
    display: flex;
    text-align: center;
}
</style>