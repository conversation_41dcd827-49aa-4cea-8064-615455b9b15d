<template>
  <div>
    <ion-card-header>
        <ion-card-title class="ion-text-left">
            <span style="color: gray; font-size: 20px; font-weight: 600; margin-left: 10px;">
                HTS Linkage Number
            </span>
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-row>
        <ion-col size="12" size-sm="3">
          <div class="relative">
            <ion-label position="stacked" class="labl-cls">Book No.</ion-label>
            <BasicInputField
                :placeholder="'Enter Book No.'"
                :inputValue="bookNo"
                @update:inputValue="bookNoChange"
                :error="!isBookNoValid"
            />
            <div>
                <ion-label v-if="!isBookNoValid" class="error-label">
                    {{ bookNoError }}
                </ion-label>
            </div>
          </div>
        </ion-col>
        
        <ion-col size="12" size-sm="3">
          <div class="relative">
            <ion-label position="stacked" class="labl-cls">Page No.</ion-label>
            <BasicInputField
              :placeholder="'Enter Page No.'"
              :inputValue="pageNo"
              @update:inputValue="pageNoChange"
              :error="!isPageNoValid"
            />
            <div>
              <ion-label v-if="!isPageNoValid" class="error-label">
                {{ pageNoError }}
              </ion-label>
            </div>
          </div>
        </ion-col>

        <ion-col size="12" size-sm="3">
          <div class="relative">
            <ion-label position="stacked" class="labl-cls">Row No.</ion-label>
            <BasicInputField
              :placeholder="'Enter Row No.'"
              :inputValue="rowNo"
              @update:inputValue="rowNoChange"
              :error="!isRowNoValid"
            />
            <div>
              <ion-label v-if="!isRowNoValid" class="error-label">
                {{ rowNoError }}
              </ion-label>
            </div>
          </div>
        </ion-col>

        <ion-col size="12" size-md="3">
          <ion-label position="stacked" class="labl-cls">Check Digit</ion-label>
          <VueMultiselect
              v-model="selectedCheckDigit"
              @update:model-value="handleCheckDigitChange($event)"
              :multiple="false"
              :taggable="false"
              :hide-selected="true"
              :close-on-select="true"
              openDirection="bottom"
              tag-placeholder=""
              placeholder=""
              selectLabel=""
              label="name"
              :searchable="true"
              @search-change=""
              track-by="name"
              :options="checkDigitOptions"
              :disabled="false"
              :class="{ 'error-state': show_check_digit_error }"
          />
          <div>
              <ion-label v-if="show_check_digit_error" class="error-label">
                  {{ "Please select Check Digit" }}
              </ion-label>
          </div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-label v-if="show_invalid_linkage_number_error" class="error-label">
            {{ "Invalid Linkage Number" }}
          </ion-label>
        </ion-col>
      </ion-row>
    </ion-card-content>
</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  IonCard, 
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonInput,
  IonButton,
  IonIcon,
  IonItem,
  IonLabel,
  IonRow,
  IonCol,
  toastController
} from '@ionic/vue';
import { checkmarkCircleOutline, alertCircleOutline, copyOutline } from 'ionicons/icons';
import { validateScanFormLinkageCode } from "@/utils/Damm";
import BasicInputField from "@/components/BasicInputField.vue";
import { toastWarning, popoverConfirmation, toastSuccess } from "@/utils/Alerts";
import VueMultiselect from "vue-multiselect";
import { ARTClinicRegistrationService } from '../../services/art_clinic_registration_service';

// Input fields with validation
const bookNo = ref('');
const pageNo = ref('');
const rowNo = ref('');

// Validation states
const isBookNoValid = ref(true);
const isPageNoValid = ref(true);
const isRowNoValid = ref(true);
const show_check_digit_error = ref(false);

// Add error message refs
const bookNoError = ref('');
const pageNoError = ref('');
const rowNoError = ref('');

const show_invalid_linkage_number_error = ref(false);

const selectedCheckDigit = ref(null) as any;
const checkDigitOptions = [
  { name: "B", char: "B", digit: 0 },
  { name: "F", char: "F", digit: 1 },
  { name: "G", char: "G", digit: 2 },
  { name: "H", char: "H", digit: 3 },
  { name: "J", char: "J", digit: 4 },
  { name: "P", char: "P", digit: 5 },
  { name: "V", char: "V", digit: 6 },
  { name: "W", char: "W", digit: 7 },
  { name: "X", char: "X", digit: 8 },
  { name: "Z", char: "Z", digit: 9 }
];

const handleCheckDigitChange = (event: any) => {
  selectedCheckDigit.value = event;
  show_check_digit_error.value = false;
  validateCheckDigit();
}

const bookNoChange = (event: any) => {
  const value = event.target.value;
  bookNo.value = value;
  validateBookNo();
}

const pageNoChange = (event: any) => {
  const value = event.target.value;
  pageNo.value = value;
  validatePageNo()
}

const rowNoChange = (event: any) => {
  const value = event.target.value;
  rowNo.value = value;
  validateRowNo();
}

// Input validators
const validateBookNo = () => {
  isBookNoValid.value = false;
  const value = bookNo.value;
  if (!value) {
    bookNoError.value = 'Book number is required';
    isBookNoValid.value = false;
  } else if (value == '') {
    bookNoError.value = 'Please enter numbers only';
    isBookNoValid.value = false;
  } else if (parseInt(value)) {
    isBookNoValid.value = true;
  } else {
    isBookNoValid.value = false;
    bookNoError.value = 'should be a number';
  }
};

const validatePageNo = () => {
  isPageNoValid.value = false;
  const value = pageNo.value;
  if (!value) {
    pageNoError.value = 'Page number is required';
    isPageNoValid.value = false;
  } else if (value == '') {
    pageNoError.value = 'Please enter numbers only';
    isPageNoValid.value = false;
  } else if (parseInt(value)) {
    isPageNoValid.value = true;
  } else {
    pageNoError.value = 'should be a number';
    isPageNoValid.value = false;
  }
};

const validateRowNo = () => {
  isRowNoValid.value = false;
  const value = rowNo.value;
   if (!value) {
    rowNoError.value = 'Row number is required';
    isRowNoValid.value = false;
  } else if (value == '') {
    rowNoError.value = 'Please enter numbers only';
    isRowNoValid.value = false;
  } else if (parseInt(value)) {
    isRowNoValid.value = true;
  } else {
    rowNoError.value = 'should be a number';
    isRowNoValid.value = false;
  }
};

const validateCheckDigit = () => {
  show_check_digit_error.value = true;
  if (selectedCheckDigit.value === null || selectedCheckDigit.value === "") {
    show_check_digit_error.value = true;
  } else {
    show_check_digit_error.value = false;
  }
};

// Check if all required fields are valid to calculate check digit
const areAllInputsValid = computed(() => {
  return isBookNoValid.value && isPageNoValid.value && isRowNoValid.value && !show_check_digit_error.value;
});

// Full linkage number with check digit
const fullLinkageNumber = computed(() => {
  if (show_check_digit_error.value == true) {
    return '';
  }
  const  checkDigit = selectedCheckDigit.value?.char;
  if (bookNo.value && pageNo.value && rowNo.value && checkDigit) {
    // Format the numbers with leading zeros
    const formattedBookNo = bookNo.value
    const formattedPageNo = pageNo.value
    const formattedRowNo = rowNo.value
    return `${formattedBookNo}-${formattedPageNo}-${formattedRowNo}-${checkDigit}`;
  }
  return '';
});

const formattedLinkageNumber = computed(() => {
  if (fullLinkageNumber.value) {
    const code = fullLinkageNumber.value;
    return `${code.slice(0,3)}-${code.slice(3,5)}-${code.slice(5,7)}-${code.slice(7)}`;
  }
  return '';
});

function validateInputs() {
  validateBookNo();
  validatePageNo();
  validateRowNo();
  validateCheckDigit();
}

// Add service initialization
const props = defineProps<{
    patientId: number;
    providerId: number;
    allvalid: boolean;
}>();

const artService = new ARTClinicRegistrationService(props.patientId, props.providerId);

const computeAndSaveHTSNumber = async () => {
  if (!fullLinkageNumber.value) return;

  try {

    // Compute observation
    const HTNData = {
      htsObservation: await artService.computeHTCSerialNumber({
        value: fullLinkageNumber.value
      } as any),
    };
    

    // Create encounter
    await artService.createEncounter();

    // Submit the observation
    await artService.onSubmit([
      HTNData.htsObservation.obs,
    ]);

    return HTNData;

  } catch (error) {
    console.error('Error saving HTS number:', error);
    throw error;
  }
};

const response = {
    isValid: false,
    message: '',
    linkageNumber: fullLinkageNumber.value,
    formattedNumber: formattedLinkageNumber.value,
    computedObs: null as any
};
  
const validateLinkageCode = () => {
  validateInputs();
  if (areAllInputsValid.value) {
    const isValid = validateScanFormLinkageCode(fullLinkageNumber.value);
    response.isValid = isValid;

    if (isValid) {
      response.message = "HTS Linkage Number is valid";
      show_invalid_linkage_number_error.value = false;
      toastSuccess(response.message);
    } else {
      response.message = "Invalid Linkage Number";
      show_invalid_linkage_number_error.value = true;
      toastWarning(response.message);
    }
  }
  return response;
}

// Update the validateLinkageCode method
const saveLinkageCode = async () => {
  validateInputs();

  if (areAllInputsValid.value) {
    const isValid = validateScanFormLinkageCode(fullLinkageNumber.value);
    response.isValid = isValid;
    if (isValid) {
      try {
        // Save the HTS number if valid
        response.computedObs = await computeAndSaveHTSNumber();
        response.isValid = true;
        response.message = "HTS Linkage Number saved successfully";
        show_invalid_linkage_number_error.value = false;
        toastSuccess(response.message);
      } catch (error) {
        response.message = "Failed to save HTS Linkage Number";
        toastWarning(response.message);
      }
    } else {
      response.message = "Invalid Linkage Number";
      show_invalid_linkage_number_error.value = true;
      toastWarning(response.message);
    }
  } else {
    response.message = "Please fill all fields correctly";
    toastWarning(response.message);
  }

  return response;
};

// Export for use in other components
defineExpose({
  validateLinkageCode,
  saveLinkageCode,
  fullLinkageNumber,
  formattedLinkageNumber
});

</script>

<style scoped>
ion-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

ion-card-title {
  font-size: 1.5rem;
}

ion-input {
  --padding-start: 8px;
  --padding-end: 32px;
  margin-bottom: 8px;
}

ion-input.ion-invalid {
  --border-color: var(--ion-color-danger);
  --highlight-color: var(--ion-color-danger);
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.8rem;
  margin: 4px 0;
}

.error-label {
  color: #b42318;
  text-transform: none;
  padding: 3%;
  padding-top: 1%;
  padding-bottom: 1%;
  margin-top: 2px;
  display: flex;
  text-align: center;
}

.error-state {
    border: none !important;  /* Remove default border first */
    outline: none !important; /* Remove any outline */
    box-shadow: 0 0 0 1px #b42318 !important; /* Use box-shadow instead of border */
    border-radius: 4px;
}

/* Add these new selectors to override VueMultiselect's default styles */
.error-state .multiselect__tags {
    border: none !important;
    box-shadow: none !important;
}

.error-state .multiselect__select {
    border: none !important;
}

.error-state.multiselect--active {
    border: none !important;
    box-shadow: 0 0 0 1px #b42318 !important;
}
</style>