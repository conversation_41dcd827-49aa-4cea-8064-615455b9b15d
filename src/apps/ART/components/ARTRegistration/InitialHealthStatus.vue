<template>
    <ion-row>
    <ion-col size="12" size-md="4">
        <ion-label position="stacked" class="labl-cls">Date started ART</ion-label>
        <DatePicker :place_holder="'enter date'" @date-up-dated="dateUpdated" :date_prop="''" :error="show_date_error"/>
        <div>
            <ion-label v-if="show_date_error" class="error-label">
                {{ date_error_message }}
            </ion-label>
        </div>
    </ion-col>
    <ion-col size="12" size-md="4">
        <ion-label position="stacked" class="labl-cls">Initial Weight</ion-label>
        <BasicInputField
            :placeholder="'Weight (Kg)'"
            :inputValue="initialWeight"
            @update:inputValue="initialWeightHandler"
            :unit="'KG'"
            :icon="scaleOutline"
            :error="show_initial_weight_error"
        />
        <div>
            <ion-label v-if="show_initial_weight_error" class="error-label">
                {{ initial_weight_error_message }}
            </ion-label>
        </div>
    </ion-col>
    <ion-col size="12" size-md="4">
        <ion-label position="stacked" class="labl-cls">Initial Height</ion-label>
        <BasicInputField
            :placeholder="'Height (CM)'"
            :inputValue="initialHeight"
            @update:inputValue="initialHeightHandler"
            :unit="'CM'"
            :icon="icons.height"
            :error="show_initial_height_error"
        />
        <div>
            <ion-label v-if="show_initial_height_error" class="error-label">
                {{ initial_height_error_message }}
            </ion-label>
        </div>
    </ion-col>

    <ion-col size="12">
        <hr style="border: none; border-top: 2px dashed gray; margin-top: 10px" />
    </ion-col>
</ion-row>

<ion-row v-if="initialHeight && initialWeight && !show_initial_height_error && !show_initial_weight_error">
    <ion-col size="12" size-md="12">
        <span>
            <ion-row
                v-if="vitalsWeightHeight.value"
                :style="
                    'border-radius: 5px;  margin-top:10px; margin-bottom:10px;background-color:' +
                    vitalsWeightHeight.backgroundColor
                "
            >
                <span class="position_content alert_content">
                    <ion-icon slot="start" :icon="vitalsWeightHeight.icon" aria-hidden="true"></ion-icon>
                    <span :style="'color:' + vitalsWeightHeight.textColor + '; font-weight:600; margin: 0px 20px;'">
                        {{ vitalsWeightHeight.index }}</span
                    >
                    <span :style="'color:' + vitalsWeightHeight.textColor + ';'"> {{ vitalsWeightHeight.value }} </span>
                </span>
            </ion-row>
        </span>
    </ion-col>
</ion-row>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import { icons } from "@/utils/svg";
import {
    scaleOutline,
} from "ionicons/icons";
import {
    IonLabel,
    IonRadioGroup,
    IonRadio,
    IonRow,
    IonCol,
    IonIcon,
} from "@ionic/vue";
import { ARTClinicRegistrationService } from '../../services/art_clinic_registration_service';
import { toastWarning, toastSuccess } from "@/utils/Alerts";
import { storeToRefs } from "pinia";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { getOfflineFirstObsValue } from "@/services/offline_service";
import { BMIService } from "@/services/bmi_service";
import HisDate from "@/utils/Date";

// store references
const demographicsStore = useDemographicsStore();

// Destructure store refs
const { patient } = storeToRefs(demographicsStore) as any;

const date = ref({}) as any;
const show_date_error = ref(false);
const date_error_message = "Date is required";

const initialWeight = ref("") as any;
const show_initial_weight_error = ref(false);
const initial_weight_error_message = "Initial weight is required";

const initialHeight = ref("") as any;
const show_initial_height_error = ref(false);
const initial_height_error_message = "Initial height is required";

const BMI = ref({}) as any;
const vitalsWeightHeight = ref({
    icon: '',
    backgroundColor: '',
    textColor: '',
    index: '',
    value: ''
}) as any;

const isValidNumber = (value: any) => {
  return value !== '' && !isNaN(value) && isFinite(value) && Number(value) > 0;
};

const setBMI = async (weight: any, height: any) => {
    if (patient.value?.personInformation?.gender && patient.value?.personInformation?.birthdate) {
        BMI.value = await BMIService.getBMI(
            parseInt(weight),
            parseInt(height),
            patient.value?.personInformation?.gender,
            HisDate.calculateAge(patient.value?.personInformation?.birthdate, HisDate.sessionDate())
        );
        updateBMI(); // Call updateBMI after BMI is set
    }
}

const updateBMI = () => {
    const bmiColor = BMI.value?.color ?? [];
    vitalsWeightHeight.value = {
        icon: BMIService.iconBMI(bmiColor),
        backgroundColor: bmiColor[0] || '',
        textColor: bmiColor[1] || '',
        index: "BMI " + (BMI.value?.index ?? ""),
        value: BMI.value?.result ?? ""
    };
}

const checkWeightAndHeight = async () => {
    const patientID = patient.value.patientID;

    if (patientID == props.patientId) {
        const vitals = [...patient.value?.vitals?.saved, ...patient.value?.vitals?.unsaved];
        if (vitals?.length > 0) {
            initialHeight.value = await getOfflineFirstObsValue(vitals, "value_numeric", 5090);
            initialWeight.value = await getOfflineFirstObsValue(vitals, "value_numeric", 5089);
            
            // Call setBMI if both height and weight are retrieved
            if (initialHeight.value && initialWeight.value) {
                setBMI(initialWeight.value, initialHeight.value);
                updateBMI();
            }
        }
    }
}

const initialWeightHandler = (event: any) => {
    const value = event.target.value;
    initialWeight.value = value;
    validateInitialWeight();
    // Call setBMI if both height and weight are present
    if (initialHeight.value && value) {
        setBMI(value, initialHeight.value);
    }
};

const initialHeightHandler = (event: any) => {
    const value = event.target.value;
    initialHeight.value = value;
    validateInitialHeight();
    // Call setBMI if both weight and height are present
    if (initialWeight.value && value) {
        setBMI(initialWeight.value, value);
    }
};

const validateInitialHeight = () => {
    const value = initialHeight.value as any;
    // Check if value is not empty and is a valid number
    const isValid = value !== '' && !isNaN(value) && isFinite(value);
    show_initial_height_error.value = !isValid;

    
};

const validateInitialWeight = () => {
    const value = initialWeight.value as any;
    const isValid = value !== '' && !isNaN(value) && isFinite(value);
    show_initial_weight_error.value = !isValid;
};

function dateUpdated(data: any) {
    const date_data = {
        day: data.value.day,
        month: data.value.month,
        year: data.value.year,
        formattedDate: data.value.formattedDate,
        standardDate: data.value.standardDate,
    };
    date.value = date_data;
    show_date_error.value = false;
    validateDate()
}

function validateDate() {
    show_date_error.value = false;
    if (Object.keys(date.value).length === 0) {
        // console.log('The date object is empty');
        show_date_error.value = true;
    } 

    if (Object.keys(date.value).length > 0) {
        show_date_error.value = false;
    }
}

const validateAndGetValues = async () => {
    // Validate all fields
    validateInitialHeight();
    validateInitialWeight();
    validateDate();
    
    // Check if all validations pass
    const isValid = !show_initial_weight_error.value &&
        !show_initial_height_error.value &&
        !show_date_error.value;

    let computedData = null;
    if (isValid) {
        try {
            computedData = await computeStatus();
        } catch (error) {
            console.error('Error computing initial health status:', error);
        }
    }
  
    // Return validation status and values
    return {
        isValid,
        values: {
            initialWeight: initialWeight.value || '',
            initialHeight: initialHeight.value || '',
            date: Object.keys(date.value).length === 0 ? {} : date.value,
        },
        computedObs: computedData
    };
};

// Add service initialization
const props = defineProps<{
    patientId: number;
    providerId: number;
    allvalid: boolean;
}>();

const artService = new ARTClinicRegistrationService(props.patientId, props.providerId);

const computeStatus = async () => {
    if (!initialWeight.value || !initialHeight.value || !date.value) return;

    return {
        weight: await artService.computeInitialWeight({
            value: parseInt(initialWeight.value)
        } as any),
        height: await artService.computeInitialHeight({ 
            value: parseInt(initialHeight.value)
        } as any),
        startDate: await artService.computeARTStartDate(
            date.value.standardDate,
            false
        )
    };
};

const saveValues = async () => {
    try {
        const statusData = await computeStatus();
        if (!statusData) return false;

        // Create encounter and submit
        await artService.createEncounter();
        await artService.onSubmit([
            statusData.weight.obs,
            statusData.height.obs,
            statusData.startDate.obs
        ]);

        toastSuccess("Initial health status saved successfully");
        return true;
    } catch (error) {
        console.error("Error saving initial health status:", error);
        toastWarning("Error saving initial health status. Please try again.");
        throw error;
    }
};

watch(() => props.patientId, (newId) => {
    if (newId) {
        checkWeightAndHeight();
    }
});

onMounted(async () => {
    await checkWeightAndHeight();
});

watch([initialWeight, initialHeight], ([newWeight, newHeight]) => {
    if (newWeight && newHeight) {
        setBMI(newWeight, newHeight);
    }
}, { immediate: true });

watch(initialWeight, (newWeight) => {
    if (newWeight && initialHeight.value) {
        setBMI(newWeight, initialHeight.value);
    }
});

watch(initialHeight, (newHeight) => {
    if (newHeight && initialWeight.value) {
        setBMI(initialWeight.value, newHeight);
    }
});

defineExpose({
    validateAndGetValues,
    saveValues
});
</script>

<style scoped lang="css">

/* White background styles */
.white-background {
    background-color: white;
}

.radio-item {
    --background: white;
    --border-color: transparent;
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
}

.radio-btn {
    margin-right: 8px;
}

.left-label-item {
    --background: white;
    --border-color: transparent;
}

/* Label styles */
.left-label-item ion-label {
    width: 100%;
    text-align: left;
    margin-right: 10px;
}

/* Required field indicator */
.required {
    color: red;
}

/* Radio group spacing */
.radio-group {
    margin-left: 15px;
}

.radio-label {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.radio-btn {
    margin-left: 20px;
    margin-right: 10px;
}

.labl-cls {
    margin: 10px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 10px;
    color: grey;
    font-size: 17px;
    font-weight: 600;
}

/* VueMultiselect custom styles */
::v-deep(.multiselect__input) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__placeholder) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__single) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__option) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

.yn-row {
    margin-top: 10px;
    margin-bottom: 5px;
}

.error-label {
  color: #b42318;
  text-transform: none;
  padding: 3%;
  padding-top: 1%;
  padding-bottom: 1%;
  margin-top: 2px;
  display: flex;
  text-align: center;
}
</style>