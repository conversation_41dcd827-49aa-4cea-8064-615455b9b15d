<template>
  <!-- Main container -->
  <div>
    <ion-card-header style="margin-bottom: 10px;">
      <ion-card-title class="ion-text-left">
        <span style="color: gray; font-size: 20px; font-weight: 600; margin-left: 10px;">
          Filling Number Management
        </span>
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <!-- Header Row -->
      <ion-grid>
        <ion-row class="header-row">
          <ion-col size="4" class="reuglar-text"></ion-col>
          <ion-col size="3" class="reuglar-text">Name</ion-col>
          <ion-col size="2.5" class="reuglar-text">New Number</ion-col>
          <ion-col size="2.5" class="reuglar-text">Old Number</ion-col>
        </ion-row>
        <!-- Data Rows -->
        <ion-row v-for="(item, index) in filingItems" :key="index" class="data-row">
          <ion-col size="4" class="reuglar-text">{{ item.statusChange }}</ion-col>
          <ion-col size="3" class="reuglar-text">{{ item.name }}</ion-col>
          <ion-col size="2.5" class="new-number-col">
            <div class="new-number">{{ item.newNumber }}</div>
          </ion-col>
          <ion-col size="2.5" class="old-number-col">
            <div class="old-number">{{ item.oldNumber }}</div>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonGrid, IonRow, IonCol, IonButton, IonIcon, IonModal, IonItem, IonLabel, IonInput, IonSelect, IonSelectOption, IonList, IonButtons } from '@ionic/vue';
import { addCircleOutline } from 'ionicons/icons';

// store references
const demographicsStore = useDemographicsStore();

// Destructure store refs
const { patient } = storeToRefs(demographicsStore) as any;

interface FilingItem {
  statusChange: string;
  name: string;
  newNumber: string;
  oldNumber: string;
}

const getPatientFullName = (patient: any): string => {
  const firstName = patient?.personInformation?.given_name || '';
  const lastName = patient?.personInformation?.family_name || '';
  return [firstName, lastName].filter(Boolean).join(' ') || 'N/A';
};

// Initial data
const filingItems = ref<FilingItem[]>([
  {
    statusChange: 'Dormant → Active',
    name: getPatientFullName(patient.value),
    newNumber: '00027',
    oldNumber: 'N/A'
  },
  { statusChange: 'Active → Dormant', name: 'N/A', newNumber: 'N/A', oldNumber: 'N/A' }
]);
</script>

<style scoped>
.header-row {
  font-weight: bold;
  padding: 10px 0;
}

.data-row {
  border-bottom: 1px solid #eee;
  padding: 12px 0;
}

.new-number-col, .old-number-col {
  padding: 0;
}

.new-number {
  background-color: #d4edda;
  padding: 8px;
  border-radius: 4px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  height: 100%;
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.old-number {
  background-color: #fff3cd;
  padding: 8px;
  border-radius: 4px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  height: 100%;
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reuglar-text {
  font-size: 16px;
  font-weight: 600;
}
</style>