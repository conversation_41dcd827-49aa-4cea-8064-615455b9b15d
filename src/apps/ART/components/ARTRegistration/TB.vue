<template>
    <!-- Initial TB Status & TPT History -->
    <ion-row>
        <ion-col size="12" size-md="6">
            <ion-label position="stacked" class="labl-cls">Initial TB Status</ion-label>
            <VueMultiselect
                v-model="SelectedInitialTBStatus"
                @update:model-value="handleInitialTBStatusChange($event)"
                :multiple="false"
                :taggable="false"
                :hide-selected="true"
                :close-on-select="true"
                openDirection="bottom"
                tag-placeholder=""
                placeholder=""
                selectLabel=""
                label="label"
                track-by="value"
                :searchable="true"
                @search-change=""
                :options="tbStatusOptions"
                :disabled="false"
                :class="{ 'error-state': show_initial_TB_status_error }"
            />
            <div>
                <ion-label v-if="show_initial_TB_status_error" class="error-label">
                    {{ "Select Initial TB Status" }}
                </ion-label>
            </div>
        </ion-col>
        <ion-col size="12" size-md="6">
            <ion-label position="stacked" class="labl-cls">TPT History</ion-label>
            <VueMultiselect
                v-model="SelectedTPTHistory"
                @update:model-value="handleTPTHistoryChange($event)"
                :multiple="false"
                :taggable="false"
                :hide-selected="true"
                :close-on-select="true"
                openDirection="bottom"
                tag-placeholder=""
                placeholder=""
                selectLabel=""
                label="label"
                track-by="value"
                :searchable="true"
                @search-change=""
                :options="tptHistoryOptions"
                :disabled="false"
                :class="{ 'error-state': show_tpt_history_error }"
            />
            <div>
                <ion-label v-if="show_tpt_history_error" class="error-label">
                    {{ "Select Initial TB Status" }}
                </ion-label>
            </div>
        </ion-col>

        <ion-col size="12">
            <hr style="border: none; border-top: 2px dashed gray; margin-top: 10px" />
        </ion-col>
    </ion-row>
</template>

<script setup lang="ts">
import { ref } from "vue";
import VueMultiselect from "vue-multiselect";
import {
    IonLabel,
    IonRow,
    IonCol,
} from "@ionic/vue";
import { ARTClinicRegistrationService } from '../../services/art_clinic_registration_service';
import { toastWarning, toastSuccess } from "@/utils/Alerts";

const tbStatusOptions = [
    { name: "No signs or symptoms of TB", value: "NO_SIGNS_OR_SYMPTOMS_OF_TB", label: "No signs or symptoms of TB" },
    { name: "Suspected TB", value: "SUSPECTED_TB", label: "Suspected TB" },
    { name: "TB Confirmed, treatment not started", value: "TB_CONFIRMED_TREATMENT_NOT_STARTED", label: "TB Confirmed, treatment not started" },
    { name: "Currently on TB Treatment", value: "CURRENTLY_ON_TB_TREATMENT", label: "Currently on TB Treatment" }
];

const tptHistoryOptions = [
    { name: "Never taken TPT", value: "NEVER_TAKEN_TPT", label: "Never taken TPT" },
    { name: "Currently on TPT", value: "CURRENTLY_ON_TPT", label: "Currently on TPT" },
    { name: "Completed TPT", value: "COMPLETED_TPT", label: "Completed TPT" },
    { name: "Stopped TPT", value: "STOPPED_TPT", label: "Stopped TPT" }
];

const SelectedInitialTBStatus = ref(null) as any;
const SelectedTPTHistory = ref(null) as any;

const show_tpt_history_error = ref(false);
function handleTPTHistoryChange(event: any) {
    // console.log("Selected confirmatory test:", event);
    SelectedTPTHistory.value = event;
    show_tpt_history_error.value = false;
}

function validateTPTHistory() {
    if (SelectedTPTHistory.value === null || SelectedTPTHistory.value === "") {
        show_tpt_history_error.value = true;
    } else {
        show_tpt_history_error.value = false;
    }
}

const show_initial_TB_status_error = ref(false);

function handleInitialTBStatusChange(event: any) {
    // console.log("Selected confirmatory test:", event);
    SelectedInitialTBStatus.value = event;
    show_initial_TB_status_error.value = false;
    validateInitialTBstatus();
}

function validateInitialTBstatus() {
    if (SelectedInitialTBStatus.value === null || SelectedInitialTBStatus.value === "") {
        show_initial_TB_status_error.value = true;
    } else {
        show_initial_TB_status_error.value = false;
    }
}

const validateAndGetValues = async () => {
    validateInitialTBstatus();
    validateTPTHistory();

    // Check if all validations pass
    const isValid = !show_initial_TB_status_error.value
        && !show_tpt_history_error.value;

    // Return validation status and values
    return {
        isValid: isValid,
        values: {
            initialTBStatus: SelectedInitialTBStatus.value,
            tptHistory: SelectedTPTHistory.value
        },
        computedObs: isValid ? await computeAndSaveTB() : null
    };
}

// Add service initialization
const props = defineProps<{
    patientId: number;
    providerId: number;
    allvalid: boolean;
}>();

const artService = new ARTClinicRegistrationService(props.patientId, props.providerId);

const computeAndSaveTB = async () => {
    //   if (!tbStatus.value || !tptHistory.value) return;

    console.log("Selected Initial TB Status:", SelectedInitialTBStatus.value.value);

  const tbData = {
    inintialTB: await artService.computeTBStatus({
      value: SelectedInitialTBStatus.value.value
    } as any),
    
    // tptHistory: await artService.computeTPTHistory({
    //   value: tptHistory.value  
    // })
  };

  console.log("Computed TB Data:", tbData.inintialTB.obs);

  // Create encounter and submit
  await artService.createEncounter();
    //   await artService.onSubmit([
    //     tbData.status.obs,
    //     // tbData.tptHistory.obs
    //   ]);

  return tbData;
};

defineExpose({
  validateAndGetValues
});
</script>

<style scoped lang="css">

/* White background styles */
.white-background {
    background-color: white;
}

.radio-item {
    --background: white;
    --border-color: transparent;
}

.left-label-item {
    --background: white;
    --border-color: transparent;
}

/* Label styles */
.left-label-item ion-label {
    width: 100%;
    text-align: left;
    margin-right: 10px;
}

/* Required field indicator */
.required {
    color: red;
}

/* Radio group spacing */
.radio-group {
    margin-left: 15px;
}

.radio-label {
    font-size: 16px;
    font-weight: 600;
}

.radio-btn {
    margin-left: 20px;
    margin-right: 10px;
}

.labl-cls {
    margin: 10px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 10px;
    color: grey;
    font-size: 17px;
    font-weight: 600;
}

/* VueMultiselect custom styles */
::v-deep(.multiselect__input) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__placeholder) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__single) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__option) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

.yn-row {
    margin-top: 10px;
    margin-bottom: 5px;
}

.error-label {
  color: #b42318;
  text-transform: none;
  padding: 3%;
  padding-top: 1%;
  padding-bottom: 1%;
  margin-top: 2px;
  display: flex;
  text-align: center;
}

.error-state {
    border: none !important;  /* Remove default border first */
    outline: none !important; /* Remove any outline */
    box-shadow: 0 0 0 1px #b42318 !important; /* Use box-shadow instead of border */
    border-radius: 4px;
}

/* Add these new selectors to override VueMultiselect's default styles */
.error-state .multiselect__tags {
    border: none !important;
    box-shadow: none !important;
}

.error-state .multiselect__select {
    border: none !important;
}

.error-state.multiselect--active {
    border: none !important;
    box-shadow: 0 0 0 1px #b42318 !important;
}
</style>