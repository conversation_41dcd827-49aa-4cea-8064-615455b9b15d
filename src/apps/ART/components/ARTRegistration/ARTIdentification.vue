<template>
    <!-- ARV Number & Registration Date -->
    <ion-row>
        <ion-col>
            <ion-label position="stacked" class="labl-cls"> ARV Number<span style="color: #b42318">*</span> </ion-label>
            <BasicInputField
                :placeholder="'e.g 192'"
                :inputValue="arvNumber"
                @update:inputValue="arvNumberHandler"
                :unit="sitePrefix"
                :error="show_arv_num_error"
            />
            <div>
                <ion-label v-if="show_arv_num_error" class="error-label">
                    {{ arv_num_error_message }}
                </ion-label>
            </div>
        </ion-col>
        <ion-col>
            <ion-label position="stacked" class="labl-cls">
                Registration Date <span class="required">*</span>
            </ion-label>
            <DatePicker :place_holder="'enter data'" @date-up-dated="dateUpdated" :date_prop="''" :error="show_date_error"/>
            <div>
                <ion-label v-if="show_date_error" class="error-label">
                    {{ date_error_message }}
                </ion-label>
            </div>
        </ion-col>

        <ion-col size="12">
            <hr style="border: none; border-top: 2px dashed gray; margin-top: 10px" />
        </ion-col>
    </ion-row>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import {
    IonLabel,
    IonRow,
    IonCol,
} from "@ionic/vue";
import { ARTClinicRegistrationService } from '../../services/art_clinic_registration_service';
import { ArvNumberService } from '../../services/arv_number_service';
import { toastWarning, toastSuccess } from "@/utils/Alerts";
import { storeToRefs } from "pinia";
import { useGlobalPropertyStore } from "@/stores/GlobalPropertyStore";

const globalPropertyStore = useGlobalPropertyStore();
const _site_prefix_ = globalPropertyStore.$state.globalPropertyStore.sitePrefix || '';

const sitePrefix = computed(() => {
    // Return only site prefix if it contains "set_site_prefix"
    if (_site_prefix_.includes("set_site_prefix")) {
        return _site_prefix_;
    }
    // Otherwise return with -ARV suffix
    return `${_site_prefix_}-ARV`;
});

const arvNumberService = new ArvNumberService();
const _arv_number = ref('');

const arvNumber = ref('');
const show_arv_num_error = ref(false);
const arv_num_error_message = "Input required, Only letters allowed";

const date = ref({}) as any;
const show_date_error = ref(false);
const date_error_message = "Date is required";

const isValid = ref(false);

const arvNumberHandler = (event: any) => {
    const value = event.target.value;
    arvNumber.value = value;
    validateArvNumber()
};

const validateArvNumber = () => {
  const value = arvNumber.value as any;
  // Check if value is not empty and is a valid number
  const isValid = value !== '' && !isNaN(value) && isFinite(value);
  show_arv_num_error.value = !isValid;
};

function dateUpdated(data: any) {
    const date_data = {
        day: data.value.day,
        month: data.value.month,
        year: data.value.year,
        formattedDate: data.value.formattedDate,
        standardDate: data.value.standardDate,
    };
    date.value = date_data;
    show_date_error.value = false;
    validateDate()
}

function validateDate() {
    show_date_error.value = false;
    if (Object.keys(date.value).length === 0) {
        // console.log('The date object is empty');
        show_date_error.value = true;
    } 

    if (Object.keys(date.value).length > 0) {
        show_date_error.value = false;
    }
}

const validateFields = () => {
  validateArvNumber();
  validateDate();
  
  isValid.value = !show_arv_num_error.value && !show_date_error.value;
  
  return {
    isValid: isValid.value,
    values: {
      arvNumber: arvNumber.value || '',
      date: Object.keys(date.value).length === 0 ? {} : date.value
    }
  };
};

const saveValues = async () => {
  if (!isValid.value || !props.allvalid) {
    toastWarning("Please complete all required fields");
    return null;
  }

  try {
    // Save ARV number if valid
    await saveARVNumber();
    // Compute and save registration date
    const registrationData = await computeAndSaveRegistrationDate();
    return registrationData;
  } catch (error) {
    console.error('Error saving values:', error);
    return null;
  }
};

const validateAndGetValues = async () => {
  const validationResult = validateFields();
  
  return {
    ...validationResult,
    registrationData: null
  };
};

// Add service initialization
const props = defineProps<{
    patientId: number;
    providerId: number;
    allvalid: boolean;
}>();

const artService = new ARTClinicRegistrationService(props.patientId, props.providerId);

const computeAndSaveRegistrationDate = async () => {
  if (show_arv_num_error.value) return;

  try {

    // Compute observation
    const ARTRegData = {
        ARTRegDate: await artService.computeARTRegistrationDate(
            date.value.standardDate,
            false
        )
    };

    // Create encounter
    await artService.createEncounter();

    // Submit the observation
    await artService.onSubmit([
        ARTRegData.ARTRegDate.obs,
    ]);

    toastSuccess("Registration date saved successfully.");
    return ARTRegData;

  } catch (error) {
    toastWarning("An error occurred while saving the registration date. Please try again.");
    // throw error;
  }
};

// Function to fetch ARV number
const fetchARVNumber = async () => {
    try {
        _arv_number.value = await arvNumberService.getARVnumber();
        arvNumber.value = _arv_number.value;
    } catch (error) {
        console.error('Error fetching ARV number:', error);
    }
};

const saveARVNumber = async () => {
    try {
        await arvNumberService.saveARVNumber(arvNumber.value);
    } catch (error) {
        toastWarning("An error occurred while saving the ARV number. Please try again.");
    }
};

// Watch for patientId changes
watch(() => props.patientId, (newId) => {
    if (newId) {
        fetchARVNumber();
    }
});

// Call on component mount
onMounted(async () => {
    await fetchARVNumber();
});

defineExpose({
    validateAndGetValues,
    saveValues,
});

</script>

<style scoped lang="css">

/* White background styles */
.white-background {
    background-color: white;
}

.radio-item {
    --background: white;
    --border-color: transparent;
}

.left-label-item {
    --background: white;
    --border-color: transparent;
}

/* Label styles */
.left-label-item ion-label {
    width: 100%;
    text-align: left;
    margin-right: 10px;
}

/* Required field indicator */
.required {
    color: red;
}

/* Radio group spacing */
.radio-group {
    margin-left: 15px;
}

.radio-label {
    font-size: 16px;
    font-weight: 600;
}

.radio-btn {
    margin-left: 20px;
    margin-right: 10px;
}

.labl-cls {
    margin: 10px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 10px;
    color: grey;
    font-size: 17px;
    font-weight: 600;
}

/* VueMultiselect custom styles */
::v-deep(.multiselect__input) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__placeholder) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__single) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__option) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

.yn-row {
    margin-top: 10px;
    margin-bottom: 5px;
}

.error-label {
  color: #b42318;
  text-transform: none;
  padding: 3%;
  padding-top: 1%;
  padding-bottom: 1%;
  margin-top: 2px;
  display: flex;
  text-align: center;
}
</style>