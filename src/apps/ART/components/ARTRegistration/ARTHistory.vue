<template>
    <ion-row class="white-background yn-row">
        <ion-col size="12" size-md="6">
            <ion-row>
                <ion-col>
                    <div class="left-label-item">
                        <ion-label position="fixed" class="labl-cls">Agrees to follow up? <span
                                class="required">*</span></ion-label>
                    </div>
                </ion-col>

                <ion-col>
                    <ion-radio-group v-model="agreesToFollowUp" class="radio-group" @ionChange="handleFollowUpChange">
                        <span>
                            <ion-label class="radio-label">Yes</ion-label>
                            <ion-radio class="radio-btn" slot="start" value="Yes"></ion-radio>
                        </span>
                        <span>
                            <ion-label class="radio-label">No</ion-label>
                            <ion-radio class="radio-btn" slot="start" value="No"></ion-radio>
                        </span>
                    </ion-radio-group>
                </ion-col>
            </ion-row>

            <ion-row>
                <BasicPhoneInputField v-if="isPhoneMissing" :inputHeader="phone_properties.inputHeader"
                    :sectionHeaderFontWeight="'20'" :bold="''" :unit="''" :input="'input'" :disabled="false"
                    :icon="phone_properties.icon" :placeholder="'Enter phone'" :iconRight="''" :leftText="''"
                    :inputWidth="'100%'" :inputValue="phone_properties.value" :eventType="'input'"
                    :p_country="currentCountryObj" @update:phone="valueChange($event)"
                    @countryChanged="countryChanged($event)" :popOverData="phone_properties.popOverData"
                    @handleInnerActionBtnPropetiesFn="$emit('click:innerBtn', phone_properties)"
                    :InnerActionBtnPropeties="phone_properties.InnerBtn" />
            </ion-row>

            <div v-if="show_agrees_to_follow_up_error">
                <ion-label class="error-label">
                    {{ agrees_to_follow_up_error_message }}
                </ion-label>
            </div>
        </ion-col>
        <ion-col size="12" size-md="6">
            <ion-row>
                <ion-col>
                    <div class="left-label-item">
                        <ion-label position="fixed" class="labl-cls">Ever received ARVs for treatment or prophylaxis?
                            <span class="required">*</span></ion-label>
                    </div>
                </ion-col>
                <ion-col>
                    <ion-radio-group v-model="everReceivedARVs" class="radio-group"
                        @ionChange="handleEverReceivedARTTreatmentChange">
                        <span class="radio-item">
                            <ion-label class="radio-label">Yes</ion-label>
                            <ion-radio class="radio-btn" slot="start" value="Yes"></ion-radio>
                        </span>
                        <span class="radio-item">
                            <ion-label class="radio-label">No</ion-label>
                            <ion-radio class="radio-btn" slot="start" value="No"></ion-radio>
                        </span>
                    </ion-radio-group>
                </ion-col>
            </ion-row>
            <div v-if="show_ever_received_arvs_error">
                <ion-label class="error-label">
                    {{ ever_received_arvs_error_message }}
                </ion-label>
            </div>
        </ion-col>

        <ion-col size="12">
            <hr style="border: none; border-top: 2px dashed gray; margin-top: 10px" />
        </ion-col>
    </ion-row>

    <!-- Date last taken ARVs -->
    <ion-row>
        <ion-col size="12" size-md="6">
            <ion-label position="stacked" class="labl-cls">Date last taken ARVs</ion-label>
            <DatePicker :place_holder="'enter date'" @date-up-dated="dateUpdated" :date_prop="''"
                :error="show_date_error" />
            <div>
                <ion-label v-if="show_date_error" class="error-label">
                    {{ date_error_message }}
                </ion-label>
            </div>
        </ion-col>
        <ion-col size="12">
            <hr style="border: none; border-top: 2px dashed gray; margin-top: 10px" />
        </ion-col>
    </ion-row>

    <!-- ART Clinic Registration -->
    <ion-row>
        <ion-col size="12" size-md="6">
            <ion-col>
                <div class="left-label-item">
                    <ion-label position="fixed" class="labl-cls">Ever registered at an ART clinic:<span
                            class="required">*</span></ion-label>
                </div>
            </ion-col>
            <ion-col>
                <ion-radio-group v-model="everRegisteredAtARTClinic" class="radio-group"
                    :value="everRegisteredAtARTClinic" @ionChange="handleEverRegisteredAtARTClinicChange">
                    <span class="radio-item">
                        <ion-label class="radio-label">Yes</ion-label>
                        <ion-radio class="radio-btn" slot="start" value="Yes"></ion-radio>
                    </span>
                    <span class="radio-item">
                        <ion-label class="radio-label">No</ion-label>
                        <ion-radio class="radio-btn" slot="start" value="No"></ion-radio>
                    </span>
                </ion-radio-group>
            </ion-col>
            <div v-if="show_ever_registered_at_art_clinic_error">
                <ion-label class="error-label">
                    {{ ever_registered_at_art_clinic_error_message }}
                </ion-label>
            </div>
        </ion-col>
        <ion-col size="12">
            <hr style="border: none; border-top: 2px dashed gray; margin-top: 10px" />
        </ion-col>
    </ion-row>

    <!-- ART Staging -->
    <ion-row v-if="everRegisteredAtARTClinic == 'Yes'">
        <Staging :date="date.altFormat" ref="stagingRef" style="width: 100%;" :hide-submit="true" />
    </ion-row>

    <!-- Location of ART Initiation & ART Number -->
    <ion-row>
        <ion-col size="12" size-md="6">
            <ion-label class="labl-cls">Location of ART Initiation</ion-label>
            <SelectFacility :show_error="show_location_error" @facility-selected="facilitySelected"
                :selected_district_ids="selectedDistrictIds" :selected_location="selected_location" />
        </ion-col>
        <ion-col size="12" size-md="6">
            <ion-label position="stacked" class="labl-cls">ART Number at Previous Location</ion-label>
            <div style="margin-top: 5px">
                <BasicInputField :placeholder="'Enter previous ART number'" :inputValue="previousARTNumber"
                    @update:inputValue="previousARTNumberHandler" :error="show_previous_arv_num_error" />
            </div>

            <div>
                <ion-label v-if="show_previous_arv_num_error" class="error-label">
                    {{ show_previous_arv_num_error_message }}
                </ion-label>
            </div>
        </ion-col>

        <ion-col size="12">
            <hr style="border: none; border-top: 2px dashed gray; margin-top: 10px" />
        </ion-col>
    </ion-row>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import { icons } from "@/utils/svg";
import { storeToRefs } from "pinia";
import { useDemographicsStore } from "@/stores/DemographicStore";
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";
import BasicPhoneInputField from "@/components/BasicPhoneInputField.vue";
import Staging from "@/apps/ART/components/Staging.vue";
import {
    IonLabel,
    IonRadioGroup,
    IonRadio,
    IonRow,
    IonCol,
} from "@ionic/vue";
import { ARTClinicRegistrationService } from '../../services/art_clinic_registration_service';
import { toastWarning, toastSuccess } from "@/utils/Alerts";
import { ArvNumberService } from '../../services/arv_number_service';

// Add service initialization
const props = defineProps<{
    patientId: number;
    providerId: number;
    allvalid: boolean;
}>();

const artService = new ARTClinicRegistrationService(props.patientId, props.providerId);

// store references
const demographicsStore = useDemographicsStore();

const stagingRef = ref<InstanceType<typeof Staging> | null>(null);

// Destructure store refs
const { patient } = storeToRefs(demographicsStore) as any;

const show_agrees_to_follow_up_error = ref(false);
const agrees_to_follow_up_error_message = "Input required, must select Yes or No";

const show_ever_received_arvs_error = ref(false);
const ever_received_arvs_error_message = "Input required, must select Yes or No";

const show_ever_registered_at_art_clinic_error = ref(false);
const ever_registered_at_art_clinic_error_message = "Input required, must select Yes or No";

const date = ref<any>({});
const show_date_error = ref(false);
const date_error_message = "Date is required";

const FacilityData = ref(null) as any

const phone_properties = ref({
    inputHeader: "Phone number *",
    icon: icons.phone,
    value: "",
    name: "phoneNumber",
    InnerBtn: "" as any,
    popOverData: {
        filterData: false,
        data: [],
    },
});

const valueChange = (event: any) => { };
const countryChanged = (event: any) => { };
const isPhoneMissing = ref(false)
const currentCountryObj = ref([{ dialCode: "265", iso2: "MW", name: "Malawi" }]) as any;

const show_location_error = ref(false) as any;
const selectedDistrictIds = ref([]) as any;
const selected_location = ref({}) as any;

const agreesToFollowUp = ref("") as any;
const everReceivedARVs = ref("") as any;
const everRegisteredAtARTClinic = ref("") as any;

const previousARTNumber = ref("") as any;
const show_previous_arv_num_error = ref(false) as any;
const show_previous_arv_num_error_message = "Input required";

const handleEverRegisteredAtARTClinicChange = (event: any) => {
    console.log("Ever registered at ART clinic selection changed to:", event.target.value);
    validateEverRegisteredAtARTClinic();
    const value = event.target.value;
}

const handleEverReceivedARTTreatmentChange = (event: any) => {
    console.log("Ever received ARVs selection changed to:", event.target.value);
    validateEverReceivedARVs();
    const value = event.target.value;
}

const previousARTNumberHandler = (event: any) => {
    const value = event.target.value;
    previousARTNumber.value = value;
    validatePreviousArvNumber()
};

const validatePreviousArvNumber = () => {
    const value = previousARTNumber.value;
    // Check if value is not empty and doesn't contain spaces, tabs, or line breaks
    const hasWhitespace = /[\s\t\n\r]/.test(value);
    const isValid = value !== '' && !hasWhitespace;
    show_previous_arv_num_error.value = !isValid;
};

const handleFollowUpChange = (event: any) => {
    validateAgreesToFollowUp();
    try {
        const value = event.target.value;
        if (value === "No") {
            isPhoneMissing.value = false
        }

        if (value === "Yes") {
            const phoneNumber = patient.value?.personInformation.cell_phone_number;
            const normalizedPhone = phoneNumber?.toLowerCase() || "";

            const isInvalidPhone =
                !phoneNumber ||
                normalizedPhone === "" ||
                normalizedPhone === "na" ||
                normalizedPhone === "n/a" ||
                normalizedPhone === "unknown" ||
                normalizedPhone.trim() === "";

            if (isInvalidPhone) {
                isPhoneMissing.value = true;
                toastWarning("Phone number is required for follow-up");
            }
        }
    } catch (error) {
        console.error("Error in handleFollowUpChange:", error);
        toastWarning("Error processing follow-up selection");
    }
};

const facilitySelected = (data: any) => {
    FacilityData.value = data.selected_location;
    validateFacility();
};

function validateFacility() {
    if (FacilityData.value) {
        show_location_error.value = false
    } else {
        show_location_error.value = true
    }
}

function validateAgreesToFollowUp() {
    const value = agreesToFollowUp.value;
    const isValid = value === 'Yes' || value === 'No';
    show_agrees_to_follow_up_error.value = !isValid;
}

function validateEverReceivedARVs() {
    const value = everReceivedARVs.value;
    const isValid = value === 'Yes' || value === 'No';
    show_ever_received_arvs_error.value = !isValid;
}

function validateEverRegisteredAtARTClinic() {
    const value = everRegisteredAtARTClinic.value;
    const isValid = value === 'Yes' || value === 'No';
    show_ever_registered_at_art_clinic_error.value = !isValid;
}

function dateUpdated(data: any) {
    const date_data = {
        day: data.value.day,
        month: data.value.month,
        year: data.value.year,
        formattedDate: data.value.formattedDate,
        altFormat: `${data.value.year}-${data.value.month}-${data.value.day}`,
        standardDate: data.value.standardDate,
    };
    date.value = date_data;
    show_date_error.value = false;
    validateDate()
}

function validateDate() {
    show_date_error.value = false;
    if (Object.keys(date.value).length === 0) {
        // console.log('The date object is empty');
        show_date_error.value = true;
    }

    if (Object.keys(date.value).length > 0) {
        show_date_error.value = false;
    }
}


const computeHistory = async () => {
    try {
        return {
            followup: await artService.computeFollowupAgreements({
                value: agreesToFollowUp.value
            } as any),
            everReceived: await artService.computeEverReceivedART({
                value: everReceivedARVs.value
            } as any),
            lastTaken: await artService.computeARTLastTakenDate(
                date.value.standardDate,
                false
            ),
            location: await artService.computeARTInitiationLocation({
                label: FacilityData.value.code
            } as any),
            previousNumber: await artService.computeARTNumberAtPreviousLocation({
                value: previousARTNumber.value
            } as any)
        };
    } catch (error) {
        console.error("Error computing ART history:", error);
        throw error;
    }
};

const saveValues = async () => {
    try {
        const historyData = await computeHistory();
        
        // Create encounter and submit
        await artService.createEncounter();
        await artService.onSubmit([
            historyData.followup.obs,
            historyData.everReceived.obs,
            historyData.lastTaken.obs,
            historyData.location.obs,
            historyData.previousNumber.obs
        ]);

        toastSuccess("ART history saved successfully");
        return true;
    } catch (error) {
        console.error("Error saving ART history:", error);
        toastWarning("Error saving ART history. Please try again.");
        throw error;
    }
};
const validateAndGetValues = async () => {
    validateAgreesToFollowUp();
    validateEverReceivedARVs();
    validateEverRegisteredAtARTClinic();
    validateDate();
    validateFacility();
    validatePreviousArvNumber();
    
    const stagingValid = !stagingRef.value?.validateStagingForm() && everRegisteredAtARTClinic.value == 'Yes' ? false : true;

    const isValid = !show_agrees_to_follow_up_error.value &&
        !show_ever_received_arvs_error.value &&
        !show_ever_registered_at_art_clinic_error.value &&
        !show_date_error.value &&
        !show_location_error.value &&
        !show_previous_arv_num_error.value &&
        stagingValid;

    if (!isValid) {
        toastWarning("Please complete all required fields correctly");
    }

    let computedData = null;
    if (isValid) {
        try {
            computedData = await computeHistory();
        } catch (error) {
            console.error('Error computing history:', error);
        }
    }

    return {
        isValid,
        values: {
            agreesToFollowUp: agreesToFollowUp.value,
            everReceivedARVs: everReceivedARVs.value,
            everRegisteredAtARTClinic: everRegisteredAtARTClinic.value,
            date: date.value,
            facilityData: FacilityData.value,
            previousARTNumber: previousARTNumber.value
        },
        computedObs: computedData
    };
};

// Function to fetch ARV number
const fetchARVNumber = async () => {
    try {
        const arvNumberService = new ArvNumberService();
        previousARTNumber.value = await arvNumberService._getArvNumber_();
    } catch (error) {
        console.error('Error fetching ARV number:', error);
    }
};

// Watch for patientId changes
watch(() => props.patientId, (newId) => {
    if (newId) {
        fetchARVNumber();
    }
});

// Call on component mount
onMounted(async () => {
    await fetchARVNumber();
});

defineExpose({
    validateAndGetValues,
    saveValues
});
</script>

<style scoped lang="css">
/* White background styles */
.white-background {
    background-color: white;
}

.radio-item {
    --background: white;
    --border-color: transparent;
}

.left-label-item {
    --background: white;
    --border-color: transparent;
}

/* Label styles */
.left-label-item ion-label {
    width: 100%;
    text-align: left;
    margin-right: 10px;
}

/* Required field indicator */
.required {
    color: red;
}

/* Radio group spacing */
.radio-group {
    margin-left: 15px;
}

.radio-label {
    font-size: 16px;
    font-weight: 600;
}

.radio-btn {
    margin-left: 20px;
    margin-right: 10px;
}

.labl-cls {
    margin: 10px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 10px;
    color: grey;
    font-size: 17px;
    font-weight: 600;
}

/* VueMultiselect custom styles */
::v-deep(.multiselect__input) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__placeholder) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__single) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

::v-deep(.multiselect__option) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

.yn-row {
    margin-top: 10px;
    margin-bottom: 5px;
}

.error-label {
    color: #b42318;
    text-transform: none;
    padding: 3%;
    padding-top: 1%;
    padding-bottom: 1%;
    margin-top: 2px;
    display: flex;
    text-align: center;
}
</style>