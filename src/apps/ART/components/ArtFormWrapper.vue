<template>
    <ion-page>
        <ion-header>
            <ion-toolbar>
                <ion-title>{{ title }}</ion-title>
                <ion-buttons slot="end">
                    <ion-button @click="onCancel">
                        <ion-icon size="large" :icon="close"></ion-icon>
                    </ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        <ion-content>
            <div v-if="useComponent" class="ion-padding">
                <component ref="formComponent" :is="useComponent" />
            </div>
        </ion-content>
        <ion-footer>
            <ion-toolbar>
                <ion-button color="success" @click="onSubmit" slot="end">Finish</ion-button>
            </ion-toolbar>
        </ion-footer>
    </ion-page>
</template>
<script lang="ts" setup>
import { toastDanger } from "@/utils/Alerts"
import {
    IonPage,
    IonHeader,
    IonTitle,
    IonContent,
    IonFooter,
    IonToolbar,
    IonButton,
    IonIcon,
    IonButtons
} from "@ionic/vue"
import { close } from "ionicons/icons"
import { ref } from "vue"

const props = defineProps({
    title: {
        type: String,
        required: true
    },
    useComponent: {
        required: true
    },
    onClose: {
        required: false
    }
})

const formComponent = ref<any>(null)

function onCancel() {
    if (typeof props.onClose === 'function') {
        props.onClose()
    }
}

function onSubmit() {
    if (formComponent.value && typeof formComponent.value.onSubmit === 'function') {
        formComponent.value.onSubmit()
            .then((ok: boolean) => ok && onCancel())
            .catch((e: any) => {
                toastDanger(`An error has occured while saving form`)
                console.error(e)
            })
    } else {
        onCancel()
    }
}
</script>