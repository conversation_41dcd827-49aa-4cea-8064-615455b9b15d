<template>

      <div class="container">

        <ion-card class="adherence-card">
          <ion-card-content>
            <ion-accordion-group @ion-change="buildAdherenceReport" value="pills">
              <!-- Pills remaining section -->
              <ion-accordion value="pills" :toggle-icon="chevronDownOutline">
                <ion-item slot="header" color="light">
                  <ion-label>Pills remaining (brought to clinic)</ion-label>
                </ion-item>
                <div class="ion-padding" slot="content">
                  <div class="medication-section">
                    <div class="medication-header">Medication (enter amount remaining for each drug)</div>
                    <div class="medication-inputs">
                      <div class="medication-row" v-for="(medication, index) in adherenceStore.medications" :key="index">
                        <div class="medication-name">{{ medication.name }}</div>
                        <ion-input
                          type="number"
                          placeholder="0"
                          class="pill-input"
                          v-model="medication.remaining"
                          @ionChange="updateRemaining(index, $event)"
                        ></ion-input>
                      </div>
                    </div>
                  </div>
                </div>
              </ion-accordion>

              <!-- ARV adherence section -->
              <ion-accordion value="arv" :toggle-icon="chevronDownOutline">
                <ion-item slot="header" color="light">
                  <ion-label>ARV adherence</ion-label>
                </ion-item>
                <div class="ion-padding" slot="content">
                  <report-data-table
                    :columns="tableHeaders"
                    :rows="tableRows"
                    :cellColors="tableCellColors"
                    :rowColors="tableRowColors"/>
                </div>
              </ion-accordion>
            </ion-accordion-group>

          </ion-card-content>
        </ion-card>
      </div>

</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import {
  IonPage,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonAccordion,
  IonAccordionGroup,
  IonItem,
  IonLabel,
  IonInput,
  IonRadio,
  IonRadioGroup,
  IonButton,
  IonIcon,
} from "@ionic/vue";
import { chevronBackOutline, chevronDownOutline } from "ionicons/icons";
import DemographicBar from "@/components/DemographicBar.vue";
import { useARTAdherenceStore } from "../store/ARTAdherenceStore";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { AdherenceService } from "@/apps/ART/services/adherence_service";
import { PatientService } from '@/services/patient_service';
import { useUserStore } from "@/stores/userStore";
import { Service } from "@/services/service";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import ReportDataTable from "./SimpleTable/ReportDataTable.vue";

export default defineComponent({
  name: "ARTAdherence",
  components: {
    ReportDataTable,
    IonPage,
    IonContent,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonAccordion,
    IonAccordionGroup,
    IonItem,
    IonLabel,
    IonInput,
    IonRadio,
    IonRadioGroup,
    IonButton,
    IonIcon,
  },
  setup() {
    const router = useRouter();
    const adherenceStore = useARTAdherenceStore();
    const demographicsStore = useDemographicsStore();
    const userStore = useUserStore();
    const patientService = new PatientService();
    const providerId = Service.getUserID() ?? -1;
    const locationID = userStore.getfacilityLocation()?.code || "";
    const patientId = patientService.getID();
    const adherenceService = new AdherenceService(patientId, providerId, locationID);
    const tableHeaders = ref<any>([])
    const tableRows = ref<any>([])
    const tableRowColors = ref<any>([])
    const tableCellColors = ref<any>([])
    // Initialize adherence data when component is mounted
    onMounted(async () => {
      try {
     

        // Default provider ID, adjust as needed

        if (patientId) {
       
          await adherenceService.loadPreviousDrugs();

          if (adherenceService.receivedDrugsBefore()) {
             adherenceStore.processMedicationData(adherenceService)
            // Update store with data from adherence service if needed
            // This would be implemented when backend logic is added
            // For now, we're using the mock data in the store
          }
        }
      } catch (error) {
        console.error("Error initializing adherence data:", error);
      }
    });

    const goBackToProfile = () => {
      router.push("/patientProfile");
    };

    const updateRemaining = (index: number, event: CustomEvent) => {
      const value = event.detail.value ? parseInt(event.detail.value) : 0;
      adherenceStore.updateMedicationRemaining(index, value);
    };

    const updateExpected = (index: number, event: CustomEvent) => {
      const value = event.detail.value ? parseInt(event.detail.value) : 0;
      adherenceStore.updateExpected(index, value);
    };

    const updateActual = (index: number, event: CustomEvent) => {
      const value = event.detail.value ? parseInt(event.detail.value) : 0;
      adherenceStore.updateActual(index, value);
    };

    const updateAgreeWithCalculation = (event: CustomEvent) => {
      adherenceStore.setAgreeWithCalculation(event.detail.value === 'yes');
    };

    function calcPillsExpected(d: any) {
            return adherenceService.calculateExpected(
                d.quantity, 
                d.equivalent_daily_dose, 
                d.order.start_date,
                formatFrequency(d.frequency) as 'QOD' | 'QW'
            )
    }

    function formatFrequency(frequency: string) {
            return `${frequency}`.match(/qod/i) 
                    ? 'QOD'
                    : `${frequency}`.match(/weekly/i) 
                    ? 'QW'
                    : frequency
    }

    function buildAdherenceReport() {
            const data = adherenceStore.medications.map((val: any) => ({
              ...val.drug, pillsBrought: val.remaining
            }))
            const lastVisit = adherenceService.getReceiptDate()
            const daysElapsed = adherenceService.calcTimeElapsed(lastVisit, 'day')
            const timeElapse = ` Last visit: ${lastVisit} 
                (${daysElapsed} Days Elapsed)`
            const rowColors = [{ indexes: [0, 3, 6], class: 'adherence-col-bg' }]
            const cellColors: any = []
            const columns = [timeElapse]
            const rows = [
                ['Prescription'],
                ['Tabs given'],
                ['Tabs per'],
                ['Tabs remaining'],
                ['Expected'],
                ['Actual (counted)'],
                ['Adherence'],
                ['Doses missed/ Unaccounted for'],
                ['Doses consumed'],
                ['Art Adherence']
            ]        
            data.forEach((order: any, index: number) => {
                const frequency = formatFrequency(order.frequency)
                const expectedPills: any = calcPillsExpected(order)
                const adherence = adherenceService.calculateAdherence(
                    order.quantity, order.pillsBrought, expectedPills
                )
                const adherenceStatus = adherenceService.isAdherenceGood(adherence) 
                    ? 'Good adherence' 
                    : 'Explore problem'
                const unAccountedDoses =adherenceService.calculateUnaccountedOrMissed(
                    expectedPills, order.pillsBrought
                )
                columns.push(order.drug.name)
                rows[0].push('')
                rows[1].push(order.quantity)
                rows[2].push(`${order.equivalent_daily_dose} <b>${frequency}</b>`)
                rows[3].push('')
                rows[4].push(expectedPills < 0 ? 0 : expectedPills)
                rows[5].push(order.pillsBrought)
                rows[6].push('')
                rows[7].push(unAccountedDoses)
                rows[8].push(`${adherence}%`)
                rows[9].push(adherenceStatus)

                cellColors.push({ 
                    index: index+1,
                    row: 9, 
                    class: adherenceStatus.match(/good/i) ? 'adherence-txt-good' : 'adherence-txt-bad' 
                })
            })
            tableHeaders.value = columns
            tableRows.value = rows
            tableCellColors.value = cellColors
            tableRowColors.value = rowColors
        }
        
    const onSubmit = async () => {
      console.log(buildAdherenceReport())
      try {
        const patientId = patientService.getID();
        if (!patientId) {
          toastDanger("Patient ID required")
          console.error('Patient ID not found');
          return false;
        }
        const drugObs: any = []
        adherenceStore.medications.forEach(async(val: any) => {
            const {drug, order } = val.drug
            const data = { ...val.drug, pillsBrought: val.remaining }
            const adherence = adherenceService.calculateAdherence(
              data.quantity, data.pillsBrought, calcPillsExpected(data)
            )
            drugObs.push(
              adherenceService.buildAdherenceObs(order.order_id, drug.drug_id, adherence)
            )
            drugObs.push(
              adherenceService.buildPillCountObs(order.order_id, val.remaining)
            )
        })

        await adherenceService.createEncounter()
        const dataObs = await Promise.all([...drugObs])
        const obs = await adherenceService.saveObservationList(dataObs)

        if (obs) {
          toastSuccess("Adherence saved!")
          return true
        }
      } catch (error) {
        console.error('Error saving adherence data:', error);
        toastDanger("An exception has occured")
       }
      return false
    };

    return {
      tableRows,
      tableHeaders,
      tableCellColors,
      tableRowColors,
      adherenceStore,
      chevronBackOutline,
      chevronDownOutline,
      buildAdherenceReport,
      goBackToProfile,
      updateRemaining,
      updateExpected,
      updateActual,
      updateAgreeWithCalculation,
      onSubmit
    };
  },
});
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  padding: 20px;
  margin: 0 auto;
}

.back_profile {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  cursor: pointer;
}

.back_profile ion-icon {
  margin-right: 5px;
  font-size: 20px;
}

.adherence-card {
  width: 100%;
  margin-bottom: 20px;
}

.medication-section {
  margin-bottom: 20px;
}

.medication-header {
  font-weight: bold;
  margin-bottom: 10px;
}

.medication-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.medication-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.medication-name {
  flex: 1;
}

.pill-input {
  width: 100px;
  border: 1px solid #ccc;
  border-radius: 4px;
  --padding-start: 10px;
}

.last-visit {
  margin-bottom: 20px;
  font-weight: bold;
}

.adherence-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background-color: #f0f0f0;
  font-weight: bold;
  padding: 10px 0;
}

.header-cell {
  padding: 8px;
  text-align: center;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  border-bottom: 1px solid #eee;
}

.header-row {
  background-color: #f0f0f0;
  font-weight: bold;
}

.row-label {
  padding: 8px;
}

.row-value {
  padding: 8px;
  text-align: center;
}

.adherence-input {
  width: 80px;
  margin: 0 auto;
  border: 1px solid #ccc;
  border-radius: 4px;
  --padding-start: 10px;
}

.error-text {
  color: red;
}

.agree-section {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.agree-label {
  margin-right: 20px;
}

.required {
  color: red;
}

.radio-options {
  display: flex;
}

.radio-options ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-right: 20px;
}

.save-button-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
}

.save-button {
  --background: #006400;
  --color: white;
}
</style>
