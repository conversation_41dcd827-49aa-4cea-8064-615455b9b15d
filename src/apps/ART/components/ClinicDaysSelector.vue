<template>
    <div class="clinic-days-selector">
        <div class="days-container">
            <div v-for="day in weekDays" :key="day.value" class="day-checkbox">
                <ion-checkbox 
                    :value="day.value" 
                    :checked="selectedDays.includes(day.value)"
                    @ion-change="toggleDay(day.value)"
                    labelPlacement="end">
                    {{ day.label }}
                </ion-checkbox>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { IonCheckbox } from '@ionic/vue';
import { ref, watch, onMounted } from 'vue';

const props = defineProps<{
    modelValue: string;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string): void;
}>();

const weekDays = [
    { label: 'Monday', value: 'Monday' },
    { label: 'Tuesday', value: 'Tuesday' },
    { label: 'Wednesday', value: 'Wednesday' },
    { label: 'Thursday', value: 'Thursday' },
    { label: 'Friday', value: 'Friday' },
    { label: 'Saturday', value: 'Saturday' },
    { label: 'Sunday', value: 'Sunday' }
];

const selectedDays = ref<string[]>([]);

// Parse the initial value
onMounted(() => {
    if (props.modelValue) {
        try {
            // Try to parse as JSON first
            try {
                const parsed = JSON.parse(props.modelValue);
                if (Array.isArray(parsed)) {
                    selectedDays.value = parsed;
                } else {
                    // If it's not an array, try comma-separated string
                    selectedDays.value = props.modelValue.split(',').map(day => day.trim());
                }
            } catch {
                // If JSON parsing fails, assume it's a comma-separated string
                selectedDays.value = props.modelValue.split(',').map(day => day.trim());
            }
        } catch (error) {
            console.error('Error parsing clinic days:', error);
            selectedDays.value = [];
        }
    }
});

// Toggle a day selection
const toggleDay = (day: string) => {
    const index = selectedDays.value.indexOf(day);
    if (index === -1) {
        selectedDays.value.push(day);
    } else {
        selectedDays.value.splice(index, 1);
    }
    
    // Emit the updated value
    const daysString = selectedDays.value.join(',');
    emit('update:modelValue', daysString);
    emit('change', daysString);
};

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        try {
            // Try to parse as JSON first
            try {
                const parsed = JSON.parse(newValue);
                if (Array.isArray(parsed)) {
                    selectedDays.value = parsed;
                } else {
                    // If it's not an array, try comma-separated string
                    selectedDays.value = newValue.split(',').map(day => day.trim());
                }
            } catch {
                // If JSON parsing fails, assume it's a comma-separated string
                selectedDays.value = newValue.split(',').map(day => day.trim());
            }
        } catch (error) {
            console.error('Error parsing clinic days:', error);
            selectedDays.value = [];
        }
    } else {
        selectedDays.value = [];
    }
});
</script>

<style scoped>
.clinic-days-selector {
    width: 100%;
}

.days-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.day-checkbox {
    min-width: 120px;
}
</style>
