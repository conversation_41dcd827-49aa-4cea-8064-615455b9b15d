import { idCardOutline } from "ionicons/icons";
import { RouteRecordRaw, RouterView } from "vue-router";

export const clinicReports: RouteRecordRaw = {
  path: "clinic",
  component: RouterView,
  meta: {
    title: "Clinic Reports",
    img: 'reports/clinic.png',
  },
  children: [
    {
      path: "appointments",
      component: () => import("@/apps/ART/views/reports/clinic/AppointmentReport.vue"),
      meta: {
        title: 'Appointments',
        img: 'reports/appointments.png',
      }
    },
    {
      path: "defaulters",
      component: () => import("@/apps/ART/views/reports/clinic/DefaultersReport.vue"),
      meta: {
        title: 'Defaulters',
        img: 'reports/defaulter-list.png',
      }
    },
    {
      path: "missed_appointments",
      component: () => import("@/apps/ART/views/reports/clinic/MissedAppointment.vue"),
      meta: {
        title: 'Missed Appointments',
        img: 'reports/appointment-missed.png',
      }
    },
    {
      path: "tx_rtt",
      component: () => import("@/apps/ART/views/reports/clinic/TxRTT.vue"),
      meta: {
        title: 'Tx RTT',
        img: 'reports/restart.png',
      }
    },
    {
      path: "regimen_dispensation",
      component: () => import("@/apps/ART/views/reports/clinic/RegimenDispensation.vue"),
      meta: {
        title: 'Regimen Dispensation',
        img: 'reports/regimen-give.png',
      }
    },
    {
      path: "regimen_switch",
      component: () => import("@/apps/ART/views/reports/clinic/RegimenSwitch.vue"),
      meta: {
        title: 'Regimen Switch',
        img: 'reports/regimen-switch.png',
      }
    },
    {
      path: "regimen_formulation",
      component: () => import("@/apps/ART/views/reports/clinic/RegimenFormulation.vue"),
      meta: {
        title: 'Regimens and formulation - patient level',
        img: 'reports/medical.png',
      }
    },
    {
      path: "pregnant_patients",
      component: () => import("@/apps/ART/views/reports/clinic/PregnantPatients.vue"),
      meta: {
        title: 'Pregnant Patients',
        img: 'reports/pregnant.png',
      }
    },
    {
      path: "incomplete_visits",
      component: () => import("@/apps/ART/views/reports/clinic/IncompleteVisits.vue"),
      meta: {
        title: 'Incomplete visits',
        img: 'reports/refill.png',
      }
    },
    {
      path: "clients_due_for_vl",
      component: () => import("@/apps/ART/views/reports/clinic/ClientsDueForVL.vue"),
      meta: {
        title: 'Clients due for VL',
        img: 'reports/viral_load.png',
      }
    },
    {
      path: "tpt_outcomes",
      component: () => import("@/apps/ART/views/reports/clinic/TptOutcomes.vue"),
      meta: {
        title: 'TPT Outcomes',
        img: 'reports/stats.png',
      }
    },
    {
      path: "retention",
      component: () => import("@/apps/ART/views/reports/clinic/RetentionReport.vue"),
      meta: {
        title: 'Retention',
        img: 'reports/retention.png',
      }
    },
    {
      path: "viral_load",
      component: () => import("@/apps/ART/views/reports/clinic/ViralLoadReport.vue"),
      meta: {
        title: 'Viral Load',
        img: 'reports/vl.png',
      }
    },
    {
      path: "other_outcomes",
      component: () => import("@/apps/ART/views/reports/clinic/OtherOutcomesReport.vue"),
      meta: {
        title: 'Other Outcomes',
        img: 'reports/defaulter-list.png',
      }
    },
    {
      path: "lab_results",
      component: () => import("@/apps/ART/views/reports/clinic/LabResultsReport.vue"),
      meta: {
        title: 'Lab Results',
        img: 'reports/lab-results.png',
      }
    },
    {
      path: "vl_suppression",
      component: () => import("@/apps/ART/views/reports/clinic/VlSuppression.vue"),
      meta: {
        title: 'VL Suppression',
        img: 'reports/vl_suppression.png',
      }
    },
    {
      path: "nid_utilization",
      component: () => import("@/apps/ART/views/reports/clinic/NidUtilization.vue"),
      meta: {
        title: 'National ID Utilization',
        icon: idCardOutline,
      }
    },
    {
      path: "nid_cumulative",
      component: () => import("@/apps/ART/views/reports/clinic/NidCumulative.vue"),
      meta: {
        title: 'National ID Cumulative',
        icon: idCardOutline,
      }
    },
    {
      path: "htn_enrollment",
      component: () => import("@/apps/ART/views/reports/clinic/HtnEnrollment.vue"),
      meta: {
        title: 'HTN Enrollment',
        img: 'reports/appointments.png',
      }
    },
  ]
} 