:root {
    --his-card-primary-color: white;
    --his-card-primary-padding: 0.5em;
    --his-card-primary-border-radius: 6px;
    --his-card-primary-border: 1px solid #ccc;
    --his-card-primary-shadow: 0px -2px 19px -2px rgba(196, 190, 196, 1);
    --his-card-primary-shadow-webkit: 0px -2px 19px -2px rgba(196, 190, 196, 1);
    --his-card-primary-shadow-moz: 0px -2px 19px -2px rgba(196, 190, 196, 1);
}

.position_content {
    max-width: 88vw;
    margin: 0 auto;
}
.primary_color_background {
    background-color: var(--ion-color-primary);
    --background: var(--ion-color-primary);
}
.dashed_bottom_border {
    border-bottom: 1px solid #b3b3b3;
    border-bottom-style: dashed;
    --border-color: transparent;
}
.solid_bottom_border {
    border-bottom: 1px solid #eee;
}
.display_center {
    display: flex;
    align-items: center;
}
.popover {
    --bs-popover-max-width: none;
}
.ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.dash_box {
    border: 1px solid #b3b3b3;
    border-style: dashed;
    padding: 15px;
    width: 100%;
    text-align: center;
    border-radius: 5px;
    color: #b3b3b3;
}
.add_item {
    color: var(--ion-color-primary);
    font-weight: 700;
    margin-top: 20px;
    font-size: inherit;
}

.input_item {
    border: 1px solid #b3b3b3;
    --border-width: 0 0 0 0;
    border-radius: 5px;
    --background: #fff;
}
.input_item:focus {
    border: 1px solid var(--ion-color-primary);
    --border-width: 0 0 0 0;
    border-radius: 5px;
}
/* wizard start */
.wizard_title {
    color: #00190e;
    text-align: center;
    width: inherit;
    font-size: 18px;
    padding-top: 20px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.checked_step {
    font-size: 17px;
    font-weight: 700;
    margin-top: 1px;
    margin-left: -8px;
    position: absolute;
}
.open_step {
    border: 2px solid var(--ion-color-primary) !important;
    background-color: var(--ion-color-primary);
    color: #fff !important;
}

.color_white {
    color: #fff !important;
}
.wizard_verticle ul.wizard_steps li a .common_step {
    width: 25px;
    height: 25px;
    line-height: 21px;
    border: 1px solid #b4b4b4;
    border-radius: 100px;
    display: block;
    margin: 0 auto 5px;
    font-size: 13px;
    text-align: center;
    position: relative;
    color: #b4b4b4;
    z-index: 5;
}

.wizard_verticle ul.wizard_steps {
    display: table;
    list-style: none;
    position: relative;
    width: 20%;
    float: left;
    margin: 0 0 20px;
}
.list-unstyled {
    padding-left: 0;
}

.wizard_verticle ul.wizard_steps li a:before {
    content: "";
    position: absolute;
    height: 28px;
    background: #b4b4b4;
    top: 27px;
    width: 1px;
    z-index: 4;
    left: 49%;
}
.wizard_verticle ul.wizard_steps {
    display: table;
    list-style: none;
}
.wizard_verticle ul.wizard_steps li a {
    height: 80px;
}
a {
    text-decoration: none;
}

.wizard_verticle ul.wizard_steps li a,
.wizard_verticle ul.wizard_steps li:hover {
    display: block;
    position: relative;
    -moz-opacity: 1;
    filter: alpha(opacity=100);
    opacity: 1;
    color: #666;
}

.wizard_verticle ul.wizard_steps li a {
    height: 30px;
}
.wizard_verticle ul.wizard_steps li a:first-child {
    margin-top: 27px;
}

.wizard_verticle ul.wizard_steps li a,
.wizard_verticle ul.wizard_steps li:hover {
    display: block;
    position: relative;
    -moz-opacity: 1;
    filter: alpha(opacity=100);
    opacity: 1;
    color: #666;
}
.wizard_text {
    position: absolute;
    width: 14vw;
    font-size: 16px;
    text-align: left;
    margin-left: 20px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #636363;
}

.last_step ::before {
    display: none;
}
/* wizard end */

.orange_background {
    --background: #f9a639;
    color: #fff;
}
.position_center_vetical {
    display: flex;
    align-items: center;
}
.primary_btn {
    font-weight: 600;
    font-size: var(--ion-button-font);
}

ion-toast.custom-toast {
    --start: unset;
    --border-color: red;
    --width: 370px;
}
ion-toast.custom-toast::part(container) {
    border: solid 1px;
    border-radius: 4px;
}
.diplay_space_between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.modal_title {
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 15px;
}
.text_header_16 {
    color: #00190e;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    padding-bottom: 10px;
}
.text_header_14 {
    font-weight: 400;
    font-size: 14px;
    padding: 6px 0px;
    color: #00190e;
}
.modal_wrapper {
    padding: 2px;
    background-color: #fff;
}
ion-modal > .ion-page {
    background: #fff;
}

.module-picker-modal {
    --background: transparent;
    backdrop-filter: blur(10px) !important;
    --width: 100% !important;
    --height: 100% !important;
    position: fixed;
    background: transparent !important;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999; /* Make sure it's on top of other elements */
}

.module-picker-modal > *,
.module-picker-modal > * > * {
    background: transparent !important; /* Transparent background */
    backdrop-filter: blur(10px); /* Blurry effect */
    box-shadow: none !important;
    border: none !important;
}

.modal-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.module-picker-modal .ion-page,
.module-picker-modal .ion-page ion-content {
    --background: transparent !important;
    backdrop-filter: blur(10px) !important;
}

/* vitals */
.vitals_overview {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    padding-top: 10px;
}
.notes_content {
    color: #00190e;
    font-size: 14px;
}
.notes_content li {
    padding-bottom: 15px;
    line-height: 19px;
}
.v_result {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
}
.obese {
    padding: 0.7vw;
    border-radius: 8px;
    padding-right: 3vw;
    padding-left: 30px;
    display: flex;
    color: #016302;
    align-items: center;
}
.bmi {
    background-color: #e6e6e6;
    padding: 0.7vw;
    border-radius: 8px;
    padding-right: 3vw;
    padding-left: 30px;
    display: flex;
    color: #00190e;
    align-items: center;
}

/* medications */
.m_name {
    font-weight: 400;
    color: #00190e;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.m_name_dosage {
    border-bottom: 1px solid #ccc;
    border-bottom-style: dashed;
    padding-bottom: 20px;
}
.m_btns {
    justify-content: space-between;
    display: flex;
    width: 50px;
    padding-top: 5px;
    align-items: center;
}

/* investigations */
.laboratory {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #00190e;
    border-bottom: solid 1px #ccc;
    border-bottom-style: dashed;
    line-height: 60px;
}

.large-modal {
    --border-radius: 10px;
    --width: 35vw;
    --height: 700px;
}
.otherVitalsModal {
    /* --height: 530px; */
    --width: 95vw;
    --max-width: 550px;
    --max-height: 95vh;
    --border-radius: 10px;
    --overflow: auto;
}
.large-modal {
    --width: 98%;
    --height: 90%;
    --border-radius: 8px;
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.activity-modal {
    --width: 58%;
    --height: 60%;
    --border-radius: 8px;
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.action-sheet-modal::part(content) {
    --width: 50%;
    --height: 34%;
}
.large-modal-x10 {
    --width: 98%;
    --height: 90%;
    --border-radius: 8px;
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.lab-results-modal {
    --height: 95vh;
    --border-radius: 8px;
}
@media (min-width: 768px) {
    .large-modal {
        --width: 98%;
        --height: 90%;
    }
}
@media (max-width: 937px) {
    .form-wizard-vue .fw-body-list {
        display: none;
    }
}

@media (min-width: 1024px) {
    .large-modal {
        --width: 98%;
        --height: 90%;
    }
}
.vaccineHistoryModal {
    --height: 90vh;
    --max-width: 500px;
}
.qr_code_modal {
    background-color: #fff;
}
ion-modal > .ion-page {
    overflow: auto;
}
.content_manager {
    justify-content: space-around;
    display: flex;
    margin-top: 20px;
}
.content_width {
    max-width: 1620px;
}
.small-modal {
    --border-radius: 16px;
    --width: 24vw;
}
.visit-modal {
    --border-radius: 8px;
    --width: 594px;
}
.small-confirm-modal {
    --width: 50%;
    --height: 30vh;
}
.delete-popover {
    --border-radius: 16px;
    --width: 235px;
    --offset-y: 15px;
}
ion-button {
    text-transform: none;
}
.nationalIDModal {
    /* --width: 50vw; */
    --border-radius: 16px;
}

ion-card {
    background-color: #fff;
    border-radius: 5px;
}
video {
    border-radius: 16px;
}

/* registration */
.input_fields {
    text-align: left;
}
.card_content {
    width: 360px;
    max-width: 0 auto;
    text-align: left;
}
.card_hearder {
    font-weight: 600;
    color: #00190e;
    font-size: 16px;
    text-align: center;
}
.sub_title {
    font-weight: 400;
    font-size: 14px;
    color: #636363;
    text-align: left;
    display: flex;
}
.demographics {
    display: block;
    width: 570px;
    /* margin: 0 auto; */
    text-align: center;
    margin-top: 0px;
}
.registration_ion_card {
    display: flex;
    justify-content: center;
    padding: 1.5vw;
    margin-bottom: 20px;
    contain: unset;
    overflow: unset;
}
.no_content {
    justify-content: center;
    align-items: center;
    align-content: center;
    display: flex;
    height: 250px;
}
.start_consultation {
    height: 21px;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    text-align: center;
    text-decoration-line: underline;
    color: #00190e;
    cursor: pointer;
}
.no_content_title {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    text-align: center;
    color: #636363;
}
.previousView {
    width: 100%;
    border-radius: 5px;
    margin-top: 10px;
}
.previousLabel {
    font-weight: 600;
    color: #000;
}
ion-icon {
    font-size: 25px;
}
ion-select .select-wrapper .select-wrapper-inner {
    width: 100%;
    color: red;
}
ion-accordion {
    z-index: unset;
}
.multiselect__content-wrapper {
    position: absolute;
    width: 100%;
    z-index: 999999 !important;
}
/* #null-0 {
    display: none;
} */

.multiselect__input,
.multiselect__single {
    min-height: 40px;
    line-height: 40px;
}
.groupInput .multiselect__tags {
    padding-left: revert;
    border: none;
    border-left: solid 1px #ccc;
    border-radius: unset;
    background: unset;
}
.groupInput .multiselect {
    width: 110px;
}
.multiselect__tags {
    min-height: 56px;
    border: 1px solid #b3b3b3;
    padding-left: 35px;
}
.multiselect--disabled .multiselect__tags {
    background: #ededed !important;
}
/* .multiselect--disabled .multiselect__input {
    background: #ededed !important;
} */
.multiselect--disabled .multiselect__input,
.multiselect--disabled .multiselect__single {
    background: #ededed !important;
}
.multiselect__input {
    background: unset !important;
}
.multiselect__select::before {
    top: 95%;
}
.multiselect::before {
    content: url("/svg/searchIcon.svg");
    position: absolute;
    font-size: 40px;
    margin-left: 15px;
    top: -7px;
}
.multiselect__placeholder {
    padding-top: 9px;
    padding-left: 10px;
}
.custom_card {
    border-radius: 5px;
    background-color: rgb(255, 255, 255);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 1px -2px, rgba(0, 0, 0, 0.14) 0px 2px 2px 0px, rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;
}
.bold {
    font-weight: 700;
}
.footer2 {
    color: #000;
    display: flex;
    justify-content: right;
    padding: 5px 0px 5px 0px;
    margin-right: 40px;
}
.searchField {
    --border-radius: 4px;
    --box-shadow: inset 0 -3em 3em #fff, 0 0 0 2px white, 0.3em 0.3em 1em rgba(44, 44, 44, 0.6);
    width: 60vw;
    padding: 10px;
    text-align: left;
    /* max-width: 800px; */
    color: #000;
    background-color: #fff;
    border-radius: 5px;
    margin: 5px;
}
.search_card {
    width: 1290px;
    --padding: 5px;
    --background: #fff;
    left: 10px;
}
ion-modal {
    --height: none;
}
.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon {
    display: none;
}
#cbtn {
    --background: #b42318;
}
ion-button {
    --border-width: 1px;
}
.alert_content {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.followUpVisitModal {
    --height: 80vh;
    --width: 95vw;
    --max-width: 550px;
    --border-radius: 10px;
    overflow: auto;
}
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 9999;
}

ion-spinner {
    width: 80px;
    height: 80px;
}

.loading-text {
    margin-top: 20px;
    font-size: 18px;
    color: #333;
}

.loading {
    pointer-events: none;
}
.modalTitle {
    padding: 10px;
}
.largeModal {
    --height: 95vh;
}
ion-header {
    background: #fff;
}
ion-footer {
    background: #fff;
}
.carousel__pagination-button--active::after {
    background-color: #cfcfcf;
}
.carousel__pagination-button--active::after {
    background-color: #cfcfcf;
}
.carousel__pagination-button::after {
    display: block;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #39bca4;
}
.carousel__pagination-button--active {
    background-color: #e7e7e7;
    border-radius: 50%;
}
.fullScreenModal {
    --height: 100vh;
    --width: 100vh;
}
.mediumModal {
    --height: 50vh;
    --width: 50vh;
    --border-radius: 10px;
}

.toast-button {
    margin-left: 8px;
    padding: 4px 8px;
    background-color: #36d827;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 11px;
    transition: background-color 0.2s ease;
}

.toast-button:hover {
    background-color: #91eb1c;
}

@media only screen and (max-width: 480px) {
    .Toastify__toast-container {
        left: unset;
        margin: 0;
        padding: 0;
        width: unset;
    }
}
.Toastify__toast-container--top-right {
    right: 0.3em;
    top: 0.5em;
}
.Toastify__toast {
    padding: 3px;
    margin-bottom: 5px;
    border-radius: 5px;
    border: solid 1px transparent;
    box-shadow: 0 0 0 1px rgba(230, 230, 230, 0.5);
}
.Toastify__toast-body > div:last-child {
    font-size: 12px;
}
.barcode-scanner-active {
    background: transparent;
    --background: transparent;
}

.scanning-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.scanning-frame {
    width: 80vw;
    height: 80vw;
    max-width: 300px;
    max-height: 300px;
    border: 2px solid white;
    position: relative;
}

.zoom-slider {
    width: 80vw;
    max-width: 300px;
    margin-top: 20px;
}

.stop-scanning-btn {
    margin-top: 20px;
    padding: 10px 20px;
    background: white;
    border: none;
    border-radius: 5px;
}

.torch-btn {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: black;
}

.torch-btn.torch-on {
    background: yellow;
}

.torch-btn svg {
    width: 24px;
    height: 24px;
}
.vti__dropdown-list {
    z-index: 10;
}
.vue-tel-input {
    height: 56px;
}

.fw-step-title {
    font-size: 17px;
    font-weight: 600;
}

.fw-list-wrapper-icon {
    font-size: 1.2rem;
}

:root {
    --sx-color-primary: green;
    --sx-color-on-primary: #fff;
    --sx-color-primary-container: #dcfce7;
    --sx-color-on-primary-container: green;
    --sx-color-secondary: #bbf7d0;
    --sx-color-on-secondary: #fff;
    --sx-color-secondary-container: #def8e9;
    --sx-color-on-secondary-container: #192b23;
    --sx-color-tertiary: #527d68;
    --sx-color-on-tertiary: #fff;
    --sx-color-tertiary-container: #d8fff2;
    --sx-color-on-tertiary-container: #0b3726;
    --sx-color-surface: #fef7ff;
    --sx-color-surface-dim: #ded8e1;
    --sx-color-surface-bright: #fef7ff;
    --sx-color-on-surface: #1c1b1f;
    --sx-color-surface-container: #f3edf7;
    --sx-color-surface-container-low: #f7f2fa;
    --sx-color-surface-container-high: #ece6f0;
    --sx-color-background: #fff;
    --sx-color-on-background: #1b1f1d;
    --sx-color-outline: #747e77;
    --sx-color-outline-variant: #c4c7c5;
    --sx-color-shadow: #000;
    --sx-color-surface-tint: green;
    --sx-color-neutral: var(--sx-color-outline);
    --sx-color-neutral-variant: var(--sx-color-outline-variant);

    --sx-internal-color-gray-ripple-background: #e0e0e0;
    --sx-internal-color-light-gray: #fafafa;
    --sx-internal-color-text: #000;
}
* {
    padding: revert-layer;
}
/* Remove number input arrows if rendered */
ion-input input::-webkit-outer-spin-button,
ion-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

ion-input input[type="number"] {
    -moz-appearance: textfield !important;
}
.back_profile {
    display: flex;
    justify-content: space-between;
    width: 140px;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    /* position: fixed; */
    z-index: 1000;
}
.sx__month-agenda-day {
    color: #000;
}
.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-responsive {
    width: 100%;
    overflow-x: auto;
}
div.dt-buttons > .dt-button:first-child {
    border: 1px solid #fff;
    background: #046c04;
    border-radius: 5px;
}
div.dt-buttons > .dt-button:hover:not(.disabled) {
    background: #188907 !important;
    border: 1px solid #fff !important;
}
.dt-type-date {
    text-align: left !important;
}
.dt-type-numeric {
    text-align: center !important;
}
.dp__theme_light {
    --dp-background-color: #fff;
    --dp-text-color: #212121;
    --dp-hover-color: #f3f3f3;
    --dp-hover-text-color: #212121;
    --dp-hover-icon-color: #959595;
    --dp-primary-color: green;
    --dp-primary-disabled-color: #6bacea;
    --dp-primary-text-color: #f8f5f5;
    --dp-secondary-color: #c0c4cc;
    --dp-border-color: #ddd;
    --dp-menu-border-color: #ddd;
    --dp-border-color-hover: #aaaeb7;
    --dp-border-color-focus: #aaaeb7;
    --dp-disabled-color: #f6f6f6;
    --dp-scroll-bar-background: #f3f3f3;
    --dp-scroll-bar-color: #959595;
    --dp-success-color: #76d275;
    --dp-success-color-disabled: #a3d9b1;
    --dp-icon-color: #959595;
    --dp-danger-color: #ff6f60;
    --dp-marker-color: #ff6f60;
    --dp-tooltip-color: #fafafa;
    --dp-disabled-color-text: #8e8e8e;
    --dp-highlight-color: rgb(25 118 210 / 10%);
    --dp-range-between-dates-background-color: var(--dp-hover-color, #f3f3f3);
    --dp-range-between-dates-text-color: var(--dp-hover-text-color, #212121);
    --dp-range-between-border-color: var(--dp-hover-color, #f3f3f3);
}
