<template>
    <ion-card style="padding: 0 !important">
        <ion-card-header style="border-bottom: 1px solid #c2c2c2; font-weight: 500; color: #000">
            <ion-card-title v-html="title"></ion-card-title>
            <ion-card-subtitle v-html="subtitle" v-if="subtitle" style="color: #818181"></ion-card-subtitle>
            <ion-card-subtitle v-if="totalClients?.length">
                Total Clients: <a href="#" @click="drillTotalClients">{{ totalClients.length }}</a>
            </ion-card-subtitle>
        </ion-card-header>
        <ion-card-content class="ion-no-padding" style="padding-bottom: 30px; min-height: 55vh">
            <data-table
                :rows="rows"
                :async-rows="asyncRows"
                :columns="columns"
                :actions-buttons="actionBtns"
                :row-actions-buttons="rowActionButtons"
                :custom-filters="filters"
                :config="{ showIndices }"
                @custom-filter="onCustomFilter"
                @drilldown="onDrilldown"
                color="light"
            >
                <template v-for="(_, name) in $slots" #[name]="{ filter }">
                    <slot :name="name" :filter="{ filter }"></slot>
                </template>
                <template #datePicker="{ filter }">
                    <vue-date-picker
                        v-model="pickerDates[filter.id]"
                        :enable-time-picker="false"
                        model-type="dd/MMM/yyyy"
                        format="dd/MMM/yyyy"
                        :placeholder="filter.placeholder ?? filter.label ?? 'Select Date'"
                        auto-apply
                        text-input
                    />
                </template>
            </data-table>
        </ion-card-content>
    </ion-card>
</template>

<script setup lang="ts">
import { PropType, computed, ref } from "vue";
import { getCsvExportBtn, getPdfExportBtn } from "@/utils/exports";
import { parseARVNumber, sanitizeStr, toDisplayGenderFmt } from "@/utils/common";
import useFacility from "@/composables/useFacility";
import { getReportQuarters, isValidDateRange, toDisplayFmt } from "@/utils/his_date";
import VueDatePicker from "@vuepic/vue-datepicker";
import { IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle } from "@ionic/vue";

import { ActionButtonInterface, CustomFilterInterface, DataTable, RowActionButtonInterface, TableColumnInterface } from "@uniquedj95/vtable";
import { toastWarning } from "@/utils/toasts";
import { sync } from "ionicons/icons";
import { modal } from "@/utils/modal";
import PatientDrillTable from "./DrilldownTable.vue";
import { chunk } from "lodash";
import { Patient } from "emr-api-client";
import router from "@/router";
import { getSelectButton } from "@/utils/datatable";
import { get, isEmpty } from "lodash";

export interface DrilldownData {
    column: TableColumnInterface;
    row: any;
}

export type FilterData = Record<string, any>;

const emit = defineEmits<{
    (e: "generate", filters: FilterData, rebuildCache: boolean): void;
}>();

const props = defineProps({
    title: {
        type: String,
        default: "Report",
    },
    subtitle: {
        type: String,
        default: "",
    },
    period: {
        type: String,
        default: "",
    },
    date: {
        type: String,
        default: "",
    },
    quarter: {
        type: Object as PropType<any>,
        default: () => ({}),
    },
    totalClients: {
        type: Array as PropType<Array<any>>,
    },
    columns: {
        type: Array as PropType<TableColumnInterface[]>,
        default: () => [],
    },
    rows: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    asyncRows: {
        type: Function as PropType<() => Promise<any[]>>,
    },
    actionButtons: {
        type: Array as PropType<ActionButtonInterface[]>,
        default: () => [],
    },
    rowActionButtons: {
        type: Array as PropType<RowActionButtonInterface[]>,
        default: () => [],
    },
    canExportCsv: {
        type: Boolean,
        default: true,
    },
    canExportPDF: {
        type: Boolean,
        default: true,
    },
    useSecureExport: {
        type: Boolean,
        default: false,
    },
    showRefreshButton: {
        type: Boolean,
        default: true,
    },
    useDateRangeFilter: {
        type: Boolean,
        default: false,
    },
    useQuarterFilter: {
        type: Boolean,
        default: false,
    },
    useDateFilter: {
        type: Boolean,
        default: false,
    },
    customFilters: {
        type: Array as PropType<CustomFilterInterface[]>,
        default: () => [],
    },
    showIndices: {
        type: Boolean,
        default: false,
    },
    filename: {
        type: String,
        default: "",
    },
    reportType: {
        type: String,
        default: "",
    },
    drillTitle: {
        type: Function as PropType<(data: DrilldownData) => string>,
        default: () => "Drill-down table",
    },
    customDrillColumns: {
        type: Array as PropType<Array<TableColumnInterface>>,
    },
    drillRowParser: {
        type: Function as PropType<(data: DrilldownData) => Array<any> | Promise<Array<any>>>,
    },
    customDrillHandler: {
        type: Function as PropType<(data: DrilldownData) => void>,
    },
    showDrillSelectButton: {
        type: Boolean,
        default: true,
    },
});

const pickerDates = ref({} as Record<string, string>);
const filterValues = ref({} as Record<string, any>);
const filename = computed(() =>
    sanitizeStr(`
  ${props.reportType} 
  ${useFacility().facilityName} 
  ${(props.filename || props.title).replace(props.reportType, "")} 
  ${props.period ? props.period : props.date}
`)
);

const filters = computed<CustomFilterInterface[]>(() => {
    const f = [...props.customFilters];
    if (props.useDateRangeFilter) {
        f.push({
            id: "startDate",
            label: "Start Date",
            type: "date",
            placeholder: "Start Date",
            slotName: "datePicker",
            value: props.period.split("-")[0],
        });
        f.push({
            id: "endDate",
            label: "End Date",
            placeholder: "End Date",
            type: "date",
            slotName: "datePicker",
            value: props.period.split("-")[1],
        });
    } else if (props.useQuarterFilter) {
        f.push({
            id: "quarter",
            label: "Quarter:",
            type: "select",
            value: props.quarter,
            options: getReportQuarters(10).map((q) => ({
                label: q.name,
                value: q.name,
                other: q,
            })),
        });
    } else if (props.useDateFilter) {
        f.push({
            id: "date",
            label: "Date",
            type: "date",
            slotName: "datePicker",
            value: props.date,
        });
    }
    return f;
});

const actionBtns = computed<ActionButtonInterface[]>(() => {
    const btns = [...props.actionButtons];
    if (props.canExportCsv) btns.push(getCsvExportBtn(filename.value, props.quarter?.label, props.period));
    if (props.canExportPDF) btns.push(getPdfExportBtn(filename.value, props.useSecureExport, props.quarter?.label, props.period));
    if (props.showRefreshButton) btns.push(getRefreshBtn());
    return btns;
});

const drillColumns = computed<Array<TableColumnInterface>>(
    () =>
        props.customDrillColumns ?? [
            { path: "identifier", label: "ARV Number", preSort: parseARVNumber, initialSort: true },
            { path: "birthdate", label: "Date of Birth", formatter: toDisplayFmt },
            { path: "gender", label: "Gender", formatter: toDisplayGenderFmt },
        ]
);

function getRefreshBtn(): ActionButtonInterface {
    return {
        label: "Refresh/Rebuild",
        icon: sync,
        color: "primary",
        action: () => {
            if (isEmpty(filterValues.value)) return toastWarning("Invalid filters");
            emit("generate", filterValues.value, true);
        },
    };
}

function hasValidFilterValues(values: Record<string, any>) {
    return filters.value.every((f) => {
        if (f.required === false) return true;
        if (isEmpty(values[f.id])) return false;
        if (typeof values[f.id] === "object") {
            return Object.values(values[f.id]).every(Boolean);
        }
        return true;
    });
}

function onCustomFilter(values: Record<string, any>) {
    if ("startDate" in values && "endDate" in values) {
        const { startDate, endDate } = pickerDates.value;
        if (!isValidDateRange(startDate, endDate)) {
            return toastWarning("Invalid date range");
        }
        (values.startDate = startDate), (values.endDate = endDate);
        values.dateRange = { startDate, endDate };
    }

    if ("date" in values) {
        if (isEmpty(pickerDates.value.date)) return toastWarning("Invalid date");
        values.date = pickerDates.value.date;
    }

    if (hasValidFilterValues(values)) {
        filterValues.value = values;
        return emit("generate", values, false);
    }
    toastWarning("Invalid filters");
}

function drillTotalClients(e: Event) {
    e.preventDefault();
    return onDrilldown({
        row: { clients: props.totalClients },
        column: { path: "clients", label: "Total Clients" },
    });
}

async function parseDrillRowData(data: DrilldownData, rows: Array<any>) {
    if (typeof props.drillRowParser === "function") {
        rows.push(...(await props.drillRowParser(data)));
    } else {
        const patientIds = get(data.row, data.column.path, []);
        for (const ids of chunk<number>(patientIds, 100)) {
            const res = await Patient.getAll(ids);
            if (!res.ok) return toastWarning(res.errorMessage ?? "Unable to load patient data");
            rows.push(...(res.data ?? []));
        }
    }
}

async function onDrilldown(data: DrilldownData) {
    if (typeof props.customDrillHandler === "function") {
        return props.customDrillHandler(data);
    }
    const title = props.drillTitle(data);
    const drillRows = ref<Array<any>>([]);
    parseDrillRowData(data, drillRows.value);

    return modal.show(PatientDrillTable, {
        title,
        columns: drillColumns.value,
        rows: drillRows.value,
        rowActionButtons: props.showDrillSelectButton ? [getSelectButton("person_id", router)] : [],
        actionButtons: [
            getCsvExportBtn(title, props.quarter?.label, props.period),
            getPdfExportBtn(title, false, props.quarter?.label, props.period),
        ],
    });
}
</script>

<style>
.no-data-table {
    height: 55vh !important;
}
</style>
