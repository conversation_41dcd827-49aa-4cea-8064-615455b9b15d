
<template>
  <div id="container">
    <h1><strong>Services</strong></h1>
    <div class="centered-content">
        <SearchField v-model="searchQuery" placeholder="Search..." />
        <ProgramGrid :programs="filteredPrograms" :onSelect="setProgram" />
    </div>
  </div>
</template>
<script setup lang="ts">
import SearchField from '@/components/SearchField.vue';
import ProgramGrid from '@/components/ProgramGrid.vue';
import { useProgramStore } from '@/stores/ProgramStore';
import { ref, computed, onMounted } from 'vue';
import { modal } from '@/utils/modal';
import { programIcons } from '@/utils/ProgramIcons';
import { getProgramColor } from '@/utils/programColors';
import { excludedProgramIds } from '@/utils/ProgramsExcluded';

const programStore = useProgramStore();
const searchQuery = ref('');

const authorizedPrograms = computed(() => {
  return programStore.authorizedPrograms.filter((program: any) =>{
    if(excludedProgramIds.includes(program.program_id)) return false;
    return true;
  }).map((program: any) => ({
    ...program,
    icon: programIcons[program.program_id] || null,
    color: getProgramColor(program.program_id),
  }))
});

const filteredPrograms = computed(() => {
  return authorizedPrograms.value.filter((program: any) =>
    program.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});


function setProgram (program: any) {
  programStore.setActiveProgram(program);
  modal.hide();
}

onMounted(() => {
  if(authorizedPrograms.value.length === 1){
    setProgram(authorizedPrograms.value[0]);
  }
});

</script>

<style scoped>
#container {
    text-align: center;
    padding: 50px;
    background: transparent;
}

#container strong {
    font-size: 20px;
    line-height: 26px;
}

#container p {
    font-size: 1em;
    line-height: 22px;
    color: #8c8c8c;
    margin: 0;
}

#container a {
    text-decoration: none;
}
</style>
