<template>
    <ion-fab slot="fixed" horizontal="end" vertical="bottom" class="ion-margin-bottom" edge>
        <ion-fab-button @click="selectProgramActivities"><ion-icon src="/public/svg/exam_multiple_choice.svg"></ion-icon></ion-fab-button>
        <ion-fab-button color="primary" class="ion-margin-vertical"> <ion-icon :icon="grid" @click="changeProgram"></ion-icon> </ion-fab-button>
    </ion-fab>
</template>

<script lang="ts" setup>
import { createModal } from "@/utils/Alerts";
import { IonIcon, IonFab, IonFabButton } from "@ionic/vue";
import ActivitySelectionModal from "./Modal/ActivitySelectionModal.vue";
import ModulePicker from "./DashboardModal/ModulePicker.vue";
import { grid } from "ionicons/icons";
import { modal } from "@/utils/modal";

function selectProgramActivities() {
    return createModal(ActivitySelectionModal, { class: "activity-modal" });
}

async function changeProgram() {
    return modal.show(ModulePicker, {}, "module-picker-modal");
}
</script>
