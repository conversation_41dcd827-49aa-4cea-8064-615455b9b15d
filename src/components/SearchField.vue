<template>
    <div class="search-wrapper">
        <ion-icon :icon="searchIcon" class="search-icon" aria-hidden="true" />
        <ion-input :value="inputValue" @ionInput="onInput" class="search-input" type="text"
            :placeholder="placeholder" />
    </div>
</template>


<script setup>
import { ref, watch } from 'vue'
import { search as searchIcon } from 'ionicons/icons'

const onInput = (event) => {
    inputValue.value = event.target.value;
};

const props = defineProps({
    modelValue: String,
    placeholder: String
})
const emit = defineEmits(['update:modelValue'])

const inputValue = ref(props.modelValue || '')

watch(inputValue, (val) => {
    emit('update:modelValue', val)
})
watch(() => props.modelValue, (val) => {
    inputValue.value = val
})
</script>

<style scoped>
.search-wrapper {
    display: flex;
    align-items: center;
    border: 1px solid #b3b3b3;
    border-radius: 10px;
    padding: 8px 12px;
    background-color: white;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-wrapper:focus-within {
    border-color: #1e4d1e;
}

.search-icon {
    font-size: 24px;
    color: #1e4d1e;
    margin-right: 10px;
}

.search-input {
    flex: 1;
    font-size: 18px;
    caret-color: #000;
    --padding-start: 0;
    --padding-end: 0;
    --background: transparent;
    --color: #000;
    --placeholder-color: #999;
    --highlight-color: transparent;
    --highlight-height: 0px;
    border: none;
    background: transparent;
    text-align: left;
}
</style>
