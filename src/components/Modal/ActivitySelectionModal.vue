<template>
    <ion-page>
        <ion-header style="display: flex; justify-content: space-between" color="success">
            <ion-title class="modalTitle">Select Program Activites (selected: {{ selectedActivities.length
            }})</ion-title>
            <ion-icon @click="modalController.dismiss" style="padding-top: 10px; padding-right: 10px"
                :icon="closeCircleOutline"></ion-icon>
        </ion-header>
        <ion-content :fullscreen="true">
            <multi-column-view :items="activities" #default="{ entries }" :numberOfColumns="2" v-if="activities.length">
                <div class="his-card clickable" v-for="(entry, index) in entries" :key="index"
                    @click="entry.isChecked = !entry.isChecked">
                    <ion-row>
                        <ion-col size="1">
                            <ion-checkbox v-model="entry.isChecked" @click="entry.isChecked = !entry.isChecked" />
                        </ion-col>
                        <ion-col class="ion-text-center his-md-text">
                            {{ entry.label }}
                        </ion-col>
                    </ion-row>
                </div>
            </multi-column-view>
            <div v-else class="no-data-container">
                <ion-label>No activities available for this program</ion-label>
            </div>
        </ion-content>
        <ion-footer class="ion-padding">
            <ion-button @click="postActivities"> Save </ion-button>
        </ion-footer>
    </ion-page>
</template>

<script setup lang="ts">
import { NCD_PRIMARY_ACTIVITIES } from "@/apps/NCD/config/programActivities";
import { OPD_PRIMARY_ACTIVITIES } from "@/apps/OPD/config/programActivities";
import { useUserStore } from "@/stores/userStore";
import {
    IonContent,
    IonHeader,
    IonTitle,
    IonPage,
    IonCheckbox,
    IonIcon,
    modalController,
    IonLabel,
    IonRow,
    IonCol,
    IonFooter,
    IonButton,
} from "@ionic/vue";
import { closeCircleOutline } from "ionicons/icons";
import { computed, onMounted, ref } from "vue";
import { Service } from "@/services/service";
import { Option } from "../Forms/FieldInterface";
import { mapObjsToOptions } from "@/utils/Arrays";
import { useProgramStore } from "@/stores/ProgramStore";
import { ART_PRIMARY_ACTIVITIES } from "@/apps/ART/config/programActivities";
import { toastWarning } from "@/utils/Alerts";
import MultiColumnView from "@/components/MultiColumnView.vue";

const user = useUserStore();
const property = ref("activities");
const activities = ref<Option[]>([]);

const selectedActivities = computed(() => {
    return activities.value.filter((activity) => activity.isChecked);
});

async function loadActivities(programName: string) {
    switch (programName.toLowerCase()) {
        case "ncd program":
            activities.value = mapObjsToOptions(NCD_PRIMARY_ACTIVITIES);
            property.value = "NCD_activities";
            break;
        case "opd program":
            activities.value = mapObjsToOptions(OPD_PRIMARY_ACTIVITIES);
            property.value = "OPD_activities";
            break;
        case "hiv program":
            activities.value = mapObjsToOptions(ART_PRIMARY_ACTIVITIES, "name", "workflowID");
            property.value = "activities";
            break;
        default:
            activities.value = [];
            break;
    }
}

async function preCheckActivities() {
    const data = await Service.getJson("user_properties", {
        user_id: user.getUserId(),
        property: property.value,
    });
    if (data) {
        activities.value = activities.value.map((activity) => {
            if (data.property_value.search(activity.value) >= 0) activity.isChecked = true;
            return activity;
        });
    }
}

async function postActivities() {
    try {
        await Service.postJson("user_properties", {
            property: property.value,
            property_value: selectedActivities.value.map((activity) => activity.value).join(","),
        });
        return modalController.dismiss(selectedActivities.value);
    } catch (error: any) {
        console.error(error);
        toastWarning(error.message ?? "Failed to save selected activities");
    }
}

onMounted(() => {
    console.log("USER ID", user.getUserId());
    console.log("USER PROGRAM", useProgramStore().activeProgram?.name);
    loadActivities(useProgramStore().activeProgram?.name);
    preCheckActivities();
});
</script>

<style scoped>
.his-card {
    border: 1px solid #cccbcb;
    background: var(--his-card-primary-color);
    padding: var(--his-card-primary-padding);
    margin: var(--his-card-primary-padding);
    border-radius: var(--his-card-primary-border-radius);
    -webkit-box-shadow: var(--his-card-primary-shadow-webkit);
    -moz-box-shadow: var(--his-card-primary-shadow-moz);
    box-shadow: var(--his-card-primary-shadow);
}

.clickable:hover {
    transform: translateY(4px);
    background-color: rgb(235, 235, 235);
}

.clickable:active {
    background-color: var(--ion-success);
}

.no-data-container {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: 100%;
}
</style>
