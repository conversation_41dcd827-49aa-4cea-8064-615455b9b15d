<template>
  <ion-page>
    <ion-header style="display: flex; justify-content: space-between">
      <ion-title class="modalTitle">Select Workstation Location</ion-title>
      <ion-icon
        @click="dismiss()"
        style="padding-top: 10px; padding-right: 10px"
        :icon="iconsContent.cancel"
      ></ion-icon>
    </ion-header>
    <ion-content
      :fullscreen="true"
      class="ion-padding"
      style="--background: #fff"
    >
      <div class="modal_wrapper">
        <div class="center text_12">
          <ion-row>
            <ion-col>
              <h5>
                Current workstation: <strong>{{ currentWorkstation }}</strong>
              </h5>
              <p>Select a new workstation location from the options below:</p>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <MultiColumnView
                :items="workstationLocations"
                #default="{ entries: locations }"
                :numberOfColumns="2"
              >
                <ion-list>
                  <ion-radio-group v-model="currentWorkstation">
                    <ion-item
                      v-for="(location, index) in locations"
                      :key="index"
                    >
                      <ion-label>{{ location }}</ion-label>
                      <ion-radio
                        :value="location"
                        @click="selectWorkstation(location)"
                      ></ion-radio>
                    </ion-item>
                  </ion-radio-group>
                </ion-list>
              </MultiColumnView>
            </ion-col>
          </ion-row>
        </div>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import {
  IonPage,
  IonHeader,
  IonTitle,
  IonContent,
  IonRow,
  IonCol,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  modalController,
  IonRadioGroup,
  IonRadio,
} from "@ionic/vue";
import { ref, onMounted } from "vue";
import { icons } from "@/utils/svg";
import { LocationService } from "@/services/location_service";
import { useUserStore } from "@/stores/userStore";
import { toastSuccess } from "@/utils/Alerts";
import MultiColumnView from "../MultiColumnView.vue";

// Refs
const iconsContent = ref(icons);
const workstationLocations = ref([] as string[]);
const currentWorkstation = ref("");

// Get the current workstation from the user store
const userStore = useUserStore();

onMounted(async () => {
  currentWorkstation.value = userStore.workstation || "Reception";

  try {
    // Try to fetch workstation locations from the API
    const locations = await LocationService.getWorkstationLocations();
    if (locations && locations.length > 0) {
      workstationLocations.value = locations;
    }
  } catch (error) {
    console.error("Error fetching workstation locations:", error);
  }
});

// Methods
const dismiss = () => {
  modalController.dismiss();
};

const selectWorkstation = (location: string) => {
  userStore.setWorkstation(location);
  currentWorkstation.value = location;
  toastSuccess(`Workstation changed to ${location}`);
  dismiss();
};

</script>

<style scoped>
.center {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.text_12 {
  font-size: 14px;
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  padding: 10px;
}

.btnText {
  font-size: 16px;
}
</style>
