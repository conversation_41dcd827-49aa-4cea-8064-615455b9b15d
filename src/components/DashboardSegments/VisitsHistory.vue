<template>
    <div class="visitContent">
        <ion-row>
            <ion-col size="1.6" style="height: 62vh; overflow-y: auto; padding-right: 0px; padding-left: 0px">
                <div>
                    <DynamicButton
                        class=""
                        style="margin-bottom: 5px; width: 96%; height: 45px"
                        @click="loadSavedEncounters(date)"
                        v-for="(date, index) in visits"
                        :key="index"
                        :name="covertDate(date)"
                        :fill="visitDate != date ? 'outline' : 'solid'"
                        :color="visitDate == date ? 'success' : ''"
                    />
                </div>
            </ion-col>
            <ion-col size="2.3">
                <div>
                    <DynamicButton
                        class=""
                        style="margin-bottom: 5px; width: 96%; height: 45px"
                        @click="setActiveEncounter(encounterType)"
                        v-for="(encounterType, index) in encountersBtn"
                        :key="index"
                        :name="encounterType"
                        :fill="activeEncounterBtn != encounterType ? 'outline' : 'solid'"
                        :color="activeEncounterBtn == encounterType ? 'success' : ''"
                    />
                </div>
            </ion-col>
            <ion-col offset="0.1" size="8">
                <div class="visitData">
                    <div class="table-header">
                        <h3 class="encounter-title">{{ activeEncounterBtn }} Observations</h3>
                        <DynamicButton name="Void Encounter" fill="solid" color="danger" @click="voidEncounter()" v-if="activeEncounterBtn" />
                    </div>
                    <div class="table-responsive">
                        <!-- Add loading state -->
                        <div v-if="isLoading" class="loading-state">
                            <p>Loading observations...</p>
                        </div>
                        <!-- Add empty state -->
                        <div v-else-if="tableData.length === 0 && activeEncounterBtn" class="empty-state">
                            <p>No observations found for {{ activeEncounterBtn }}</p>
                        </div>
                        <!-- DataTable -->
                        <DataTable
                            v-else
                            ref="dataTable"
                            class="display nowrap modern-table"
                            width="100%"
                            :data="tableData"
                            :columns="tableColumns"
                            :options="dataTableOptions"
                        >
                            <thead>
                                <tr>
                                    <th>Observation</th>
                                    <th>Value</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                        </DataTable>
                    </div>
                </div>
            </ion-col>
        </ion-row>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from "vue";
import { useRoute } from "vue-router";
import { icons } from "@/utils/svg";
import { useInvestigationStore } from "@/stores/InvestigationStore";
import { storeToRefs } from "pinia";
import { PatientService } from "@/services/patient_service";
import { EncounterService } from "@/services/encounter_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import HisDate from "@/utils/Date";
import { useProgramStore } from "@/stores/ProgramStore";
import DataTable from "datatables.net-vue3";
import DataTablesCore from "datatables.net";
import DataTablesResponsive from "datatables.net-responsive";
import "datatables.net-buttons";
import "datatables.net-buttons/js/buttons.html5";
import "datatables.net-buttons-dt";
import "datatables.net-responsive";
import DynamicButton from "@/components/DynamicButton.vue";
import OfflineMoreDetailsModal from "@/components/Modal/OfflineMoreDetailsModal.vue";
import { createModal, toastDanger } from "@/utils/Alerts";
import BasicForm from "@/components/BasicForm.vue";
import { toastSuccess, toastWarning } from "@/utils/Alerts";
import "datatables.net-select";
import { ObservationService } from "@/services/observation_service";
import ConfirmVoidingModal from "@/components/Modal/ConfirmVoidingModal.vue";
import popVoidReason from "@/utils/ActionSheetHelpers/VoidReason";

// Store references
const investigationStore = useInvestigationStore();
const { investigations } = storeToRefs(investigationStore);

const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore);

const programStore = useProgramStore();
const { activeProgram } = storeToRefs(programStore);

const route = useRoute();

// Reactive data
const iconsContent = ref(icons);
const visits = ref<any[]>([]);
const encountersData = ref<any[]>([]);
const activeEncounterBtn = ref("");
const visitDate = ref<any[]>([]);
const activeEncounter = ref();
const tableData = ref<any[]>([]); // Changed from computed to ref
const isLoading = ref(false); // Add loading state

// Computed properties
const encountersBtn = computed(() => {
    const uniqueTypes = new Set();
    return encountersData.value
        .filter((encounter) => encounter.type?.name && !uniqueTypes.has(encounter.type.name) && uniqueTypes.add(encounter.type.name))
        .map((encounter) => {
            const name = encounter.type.name;
            return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
        });
});

const dataTableOptions = computed(() => ({
    paging: true,
    lengthChange: false,
    searching: false,
    ordering: true,
    info: true,
    autoWidth: true,
    responsive: true,
    pageLength: 8,
}));

const tableColumns = computed(() => [
    { data: "observation", title: "Observation" },
    { data: "value", title: "Value" },
    { data: "date", title: "Date" },
]);

const inputFields = computed(() => {
    return investigations.value[0]?.selectedData;
});

// Methods
const setActiveEncounter = async (name: any) => {
    activeEncounterBtn.value = name;
    await loadTableData(); // Load data when encounter changes
};

const voidEncounter = async () => {
    // Add void encounter logic here
    popVoidReason(async (reason: string) => {
        EncounterService.voidEncounter(activeEncounter.value?.encounter_id, reason)
            .then(async () => {
                await loadSavedEncounters(visitDate.value);
                toastSuccess("Encounter has been voided!", 2000);
            })
            .catch((e) => {
                toastDanger(`${e}`, 32000);
            });
    });
};

// New method to load table data
const loadTableData = async () => {
    if (!activeEncounterBtn.value) {
        tableData.value = [];
        return;
    }

    isLoading.value = true;

    try {
        activeEncounter.value = encountersData.value.find((encounter) => {
            const formattedName = encounter.type?.name?.charAt(0).toUpperCase() + encounter.type?.name?.slice(1).toLowerCase();
            return formattedName === activeEncounterBtn.value;
        });

        if (!activeEncounter.value?.observations) {
            tableData.value = [];
            return;
        }

        const resolvedData = await Promise.all(
            activeEncounter.value?.observations.map(async (obs: any) => {
                const conceptName =
                    obs.concept?.concept_names?.find(
                        (name: any) => name.concept_name_type === "FULLY_SPECIFIED" || name.concept_name_type === "SHORT"
                    )?.name || "Unknown Observation";

                let value = (await ObservationService.resolvePrimaryValue(obs)) || "";

                return {
                    observation: conceptName,
                    value: value,
                    date: HisDate.toStandardHisDisplayFormat(obs.obs_datetime),
                };
            })
        );

        tableData.value = resolvedData;
    } catch (error) {
        console.error("Error loading table data:", error);
        tableData.value = [];
    } finally {
        isLoading.value = false;
    }
};

const updateData = async () => {
    const patientService = new PatientService();
    visits.value = await PatientService.getPatientVisits(patientService.getID(), false);
    if (visits.value.length > 0) {
        await loadSavedEncounters(visits.value[0]);
    }
};

const covertDate = (date: any) => {
    return HisDate.toStandardHisDisplayFormat(date);
};

const loadSavedEncounters = async (patientVisitDate: any) => {
    visitDate.value = patientVisitDate;
    encountersData.value = await EncounterService.getEncounters(patient.value.patientID, { date: patientVisitDate });

    // Set the first encounter type as active by default and load its data
    if (encountersBtn.value.length > 0) {
        activeEncounterBtn.value = encountersBtn.value[0];
        await loadTableData(); // Load table data after setting encounter
    }
};

// Watchers
watch(
    patient,
    async () => {
        await updateData();
    },
    { deep: true }
);

watch(
    route,
    async () => {
        await updateData();
    },
    { deep: true }
);

// Watch for changes in activeEncounterBtn
watch(activeEncounterBtn, async () => {
    await loadTableData();
});

// Lifecycle
onMounted(async () => {
    await updateData();
});
</script>

<style>
@import "datatables.net-dt";
@import "datatables.net-buttons-dt";
@import "datatables.net-responsive-dt";
@import "datatables.net-select-dt";

.table-responsive {
    width: 100%;
    overflow-x: auto;
}

/* Loading and empty states */
.loading-state,
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
    font-size: 14px;
}

.loading-state {
    background: #f9fafb;
    border-radius: 8px;
    border: 1px dashed #d1d5db;
}

.empty-state {
    background: #fefefe;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Modern table styling */
.modern-table {
    border-collapse: separate !important;
    border-spacing: 0 !important;
    background: white !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.modern-table thead th {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
    color: #334155 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    border: none !important;
    text-transform: uppercase !important;
    font-size: 11px !important;
    letter-spacing: 0.5px !important;
}

.modern-table tbody tr {
    transition: all 0.2s ease !important;
}

.modern-table tbody tr:nth-child(even) {
    background-color: #f8fafc !important;
}

.modern-table tbody tr:hover {
    background-color: #e2e8f0 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.modern-table tbody td {
    padding: 12px !important;
    border-bottom: 1px solid #e2e8f0 !important;
    border-top: none !important;
    vertical-align: middle !important;
}

.modern-table tbody tr:last-child td {
    border-bottom: none !important;
}

/* DataTable controls styling */
.dataTables_wrapper .dataTables_filter input {
    border: 2px solid #e2e8f0 !important;
    border-radius: 6px !important;
    padding: 6px 12px !important;
    margin-left: 8px !important;
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: #667eea !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 1px solid #e2e8f0 !important;
    border-radius: 4px !important;
    margin: 0 2px !important;
    padding: 6px 12px !important;
    color: #4a5568 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #667eea !important;
    color: white !important;
    border-color: #667eea !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #667eea !important;
    color: white !important;
    border-color: #667eea !important;
}
</style>

<style scoped>
.alert {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
    text-align: center;
    flex-direction: column;
}

.visitContent {
    background: #fff;
    margin-top: 1px;
    padding: 20px;
}

.visitData {
    border-left: #a3a1a1 solid 1px;
    padding-left: 20px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.encounter-title {
    margin: 0;
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
}
</style>
