<template>
    <div class="program-buttons-grid">
        <div v-for="(program, index) in programs" :key="index" class="program-tile" @click="() => onSelect(program)">
            <div class="program-icon-container" :style="{ backgroundColor: program.color }">
                <img :src="program.icon" alt="" class="program-icon" />
            </div>
            <div class="program-name">{{ program.name.replace(/program/i, '').trim() }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps<{
    programs: Array<any>;
    onSelect: (program: any) => void;
}>();
</script>

<style scoped>
.program-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    justify-content: center;
    column-gap: 20px;
    row-gap: 40px;
    padding: 10px;
    margin: 40px auto 0 auto;
    max-width: 1000px;
    width: 100%;
}

.program-tile {
    cursor: pointer;
}

.program-icon-container {
    border-radius: 5px;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 85px;
    width: 85px;
    margin: 0 auto;
}

.program-icon {
    font-size: 30px;
    color: black;
    width: 45px;
}

.program-name {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 500;
    color: black;
}
</style>
