<template>
    <IonGrid>
        <IonRow>
            <IonCol v-for="menu in menuItemsComponents" v-bind:key="menu.name">
                <IonButton color="primary">
                    {{ menu.name }} {{menu.icon}}
                </IonButton>
            </IonCol>
        </IonRow>
    </IonGrid>
</template>

<script setup lang="ts">
import { IonButton, IonCol, IonGrid, IonIcon, IonRow } from '@ionic/vue';
import { computed, PropType } from 'vue';

interface MenuItem {
    name: string;
    route: string;
    icon: typeof IonIcon;
}

const props = defineProps({
    menuItems: {
        type: <PropType<MenuItem[]>>Array,
        required: true,
        default: []
    }
});

const menuItemsComponents = computed((): MenuItem[] => {
    return props.menuItems.length > 0 ? props.menuItems : [];
})
</script>

<style scoped lang="css"></style>