<template>
  <IonPage>
  <IonHeader>
    <IonToolbar>
      <IonButtons slot="end">
        <IonButton slot="end" @click="modal.hide()" icon-only>
          <IonIcon :icon="close"></IonIcon>
        </IonButton>
      </IonButtons>
      <IonTitle>{{ title }}</IonTitle>
    </IonToolbar>
  </IonHeader>
  <IonContent>
    <data-table 
      :rows="rows"
      :columns="columns"
      :config="config"
      :row-actions-buttons="rowActionButtons"
      :actions-buttons="actionButtons"
      color="light"
    />
  </IonContent>
</IonPage>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { IonPage, IonHeader, IonToolbar, IonButtons, IonButton, IonTitle, IonIcon, IonContent } from '@ionic/vue';
import { ActionButtonInterface, DataTable, RowActionButtonInterface, TableColumnInterface, TableConfigInterface } from '@uniquedj95/vtable'
import { close } from 'ionicons/icons';
import { modal } from '@/utils/modal';

defineProps({
  title: {
    type: String,
    default: "Drill down table",
  },
  rows: {
    type: Array as PropType<Array<any>>,
    required: true,
  },
  columns: {
    type: Array as PropType<Array<TableColumnInterface>>,
    required: false,
  },
  actionButtons: {
    type: Array as PropType<Array<ActionButtonInterface>>,
  },
  rowActionButtons: {
    type: Array as PropType<Array<RowActionButtonInterface>>,
  },
  config: {
    type: Object as PropType<TableConfigInterface>,
    default: () => ({
      showSubmitButton: false 
    })
  }
});
</script>

<style scoped>
.close-button {
  float: right; 
  margin-top: 0;
  padding-top: 0;
  font-size: x-large;
  cursor: pointer;
}
</style>
