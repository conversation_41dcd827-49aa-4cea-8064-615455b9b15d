<template>
    <ion-page>
        <ion-content class="ion-padding login-page">
            <div class="container">
                <h2 class="login-title ion-margin-bottom">Workstation Location</h2>
                <ion-input
                    v-model="code"
                    fill="outline"
                    placeholder="Scan workstation barcode or QR code"
                    class="input-fields"
                    required
                    @ion-focus="onFocus"
                >
                    <ion-icon :icon="qrCodeOutline" slot="start"></ion-icon>
                </ion-input>
            </div>
        </ion-content>
    </ion-page>
</template>

<script setup lang="ts">
import { IonContent, IonPage, IonIcon, IonInput } from "@ionic/vue";
import { ref, watch } from "vue";
import { useRouter } from "vue-router";
import { qrCodeOutline } from "ionicons/icons";
import { Service } from "@/services/service";
import { Capacitor } from "@capacitor/core";
import { scannedData } from "@/services/national_id";

const code = ref("");
const router = useRouter();

watch(code, async (newValue) => {
    if (newValue.endsWith("$")) {
        try {
            const response = await Service.getJson(`locations/${newValue.replace("$", "")}`, {});
            // console.log("Location data:", response);
            code.value = "";
            router.push("/home");
        } catch (error) {
            console.error("Error parsing barcode:", error);
        }
    }
});

async function onFocus() {
    if (Capacitor.isNativePlatform()) {
        const data = await scannedData();
        if (data) code.value = data.toLocaleString() + "$";
    }
}
</script>

<style scoped>
.container {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
    height: 100vh;
    margin-top: 10%;
}
.login-title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
}
.input-fields {
    width: 80%;
}
</style>
