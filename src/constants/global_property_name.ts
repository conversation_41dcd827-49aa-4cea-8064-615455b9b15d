export enum GlobalPropName {
    PASSWORD_POLICY_ENABLED = "password_policy_enabled",
    PASSWORD_RESET_INTERVAL = "password_reset_interval",
    INACTIVITY_TIMEOUT = "inactivity_timeout",
    LAST_PASSWORD_RESET = "last_password_reset",
    CURRENT_HEALTH_CENTER_ID = "current_health_center_id",
    SITE_PREFIX = "site_prefix",
    SITE_UUID = "site_uuid",
    IS_DIC_SITE = "is_dic_site",
    TARGET_LAB = "target.lab",
    EXTENDED_LABS = "extended_labs",
    VL_ROUTINE_CHECK = "activate.vl.routine.check",
    PILLS_REMAINING = "ask_pills_remaining_at_home",
    FILING_NUMBERS = "use.filing.number",
    DRUG_MANAGEMENT = "activate.drug.management",
    HTN_ENHANCEMENT = "activate.htn.enhancement",
    FAST_TRACK = "enable_fast_track",
    THREE_HP_AUTO_SELECT = "activate_3hp_auto_select",
    APPOINTMENT_LIMIT = "clinic.appointment.limit",
    HTN_SCREENING_AGE_THRESHOLD = "htn.screening.age.threshold",
    NOTIFICATION_PERIOD = "notification.period",
    HTN_SYSTOLIC_THRESHOLD = "htn.systolic.threshold",
    HTN_DIASTOLIC_THRESHOLD = "htn.diastolic.threshold",
    PEADS_CLINIC_DAYS = "peads.clinic.days",
    ADULT_CLINIC_DAYS = "clinic.days",
    FILING_NUMBER_LIMIT = "filing.number.limit",
    FILING_NUMBER_PREFIX = "filing.number.prefix",
    CERVICAL_CANCER_SCREENING = "activate.cervical.cancer.screening",
    CERVICAL_CANCER_AGE_BOUNDS = "cervical.cancer.screening.age.bounds",
    CLINIC_HOLIDAYS = "clinic.holidays",
    EXCLUDE_EXTERNAL_AND_DRUG_REFILLS = "can.remove.external.and.drug.refills.from.data.cleaning",
    CAN_SCAN_DBS_BARCODE = "can.scan.dbs",
    LAB_ORDER_PRINT_COPIES = "max.lab.order.print.copies",
}