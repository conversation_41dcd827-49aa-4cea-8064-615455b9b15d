import { ProgramId } from "@/services/program_service";

export const getProgramColor = (programId: number): string => {
    switch (programId) {
        case ProgramId.HIV_PROGRAM:
            return "#F4A6A6"; // Soft red
        case ProgramId.TB_PROGRAM:
            return "#E6B89C"; // Muted brown
        case ProgramId.EARLY_INFANT_DIAGNOSIS_PROGRAM:
            return "#FFF2A6"; // Soft yellow
        case ProgramId.MDR_TB_PROGRAM:
            return "#D2A679"; // Light saddle brown
        case ProgramId.KAPOSIS_SARCOMA_PROGRAM:
            return "#D8B4E2"; // Light purple
        case ProgramId.CHRONIC_CARE_PROGRAM:
            return "#C7EFCF"; // Soft green
        case ProgramId.MATERNITY_PROGRAM:
            return "#FFCCE5"; // Soft pink
        case ProgramId.ANC_PROGRAM:
            return "#FFD6DD"; // Pastel pink
        case ProgramId.DIABETES_PROGRAM:
            return "#D6F5D6"; // Pastel green
        case ProgramId.OPD_PROGRAM:
            return "#B3E5FC"; // Pastel blue
        case ProgramId.IPD_PROGRAM:
            return "#FFF9C4"; // Light khaki
        case ProgramId.UNDER_5_PROGRAM:
            return "#FFB3B3"; // Baby red
        case ProgramId.CIVIL_REGISTRATION_PROGRAM:
            return "#E0E0E0"; // Light gray
        case ProgramId.HTC_PROGRAM:
            return "#B2DFDB"; // Soft teal
        case ProgramId.ANC_CONNECT_PROGRAM:
            return "#FFD6DD"; // Pastel pink
        case ProgramId.HYPERTENSION_PROGRAM:
            return "#B2DFDB"; // Soft teal
        case ProgramId.IPT_PROGRAM:
            return "#FFF9C4"; // Light khaki
        case ProgramId.LABORATORY_PROGRAM:
            return "#E6B89C"; // Muted brown
        case ProgramId.CXCA_PROGRAM:
            return "#FFF2A6"; // Soft yellow
        case ProgramId.RADIOLOGY_PROGRAM:
            return "#CCCCCC"; // Pale gray
        case ProgramId.PATIENT_REGISTRATION_PROGRAM:
            return "#FFF9C4"; // Soft khaki
        case ProgramId.AETC_PROGRAM:
            return "#FFB199"; // Soft orange
        case ProgramId.SPINE_PROGRAM:
            return "#C7EFCF"; // Soft green
        case ProgramId.NCD_PROGRAM:
            return "#D6F5D6"; // Pastel green
        case ProgramId.IMMUNIZATION_PROGRAM:
            return "#FFF2A6"; // Soft yellow
        case ProgramId.LABOUR_AND_DELIVERY_PROGRAM:
            return "#E6D6F3"; // Pale lilac
        default:
            return "#F5F5DC"; // Soft beige
    }
};
