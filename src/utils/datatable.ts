import { RowActionButtonInterface, TableColumnInterface } from "@uniquedj95/vtable"
import { eye } from "ionicons/icons";
import { useRouter } from "vue-router";

type ReportIndicators = Record<string, string> | Array<string>;

/**
 * Get a button that navigates to client dashboard.
 * 
 * @param idKey The key in the row object that contains the client's ID. Defaults to 'person_id'.
 * @returns A row action button 
 */
export function getSelectButton(idKey = 'person_id', router = useRouter()): RowActionButtonInterface {
  return { 
    label: "view client",
    icon: eye,
    color: 'light',
    action: (row) => router.push(`/patient/${row[idKey]}`)
  }
}

/**
 * Converts a list or record of indicators to an array of drillable table column configurations.
 * 
 * @param indicators - An array or record of indicators where keys are column paths and values are column labels.
 * @param other - An optional object containing additional properties to include in each column configuration.
 * @returns An array of table column configurations.
 */
export function toIndicatorColumns (indicators: ReportIndicators, other: Record<string, any> = {}): Array<TableColumnInterface> {
  if(Array.isArray(indicators)) return indicators.map(label => ({
    label,
    path: label,
    drillable: true,
    sortable: true,
    ...other
  }))

  return Object.entries(indicators).map(([path, label]) => {
    return {
      path,
      label,
      drillable: true,
      sortable: false,
      ...other
    }
  })
}
