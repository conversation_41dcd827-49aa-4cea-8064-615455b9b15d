import { ReportService } from "./report_service";
import { ApiCore } from "emr-api-client";

export enum CtIndicator {
    DobMoreThanEnrolledDate = 'DOB MORE THAN DATE ENROLLED',
    ClientsWithEncountersAfterDeath = 'CLIENTS WITH ENCOUNTERS AFTER DECLARED DEAD',
    DateEnrolledLessThanEarliestStartDate = 'DATE ENROLLED LESS THAN EARLIEST START DATE',
    MalesWithFemaleObs = 'MALE CLIENTS WITH FEMALE OBS',
    PrescriptionWithoutDispensation = 'PRESCRIPTION WITHOUT DISPENSATION',
    MissingDemographics = 'MISSING DEMOGRAPHICS',
    MissingArtStartDate = 'MISSING ART START DATE',
    MissingStartReasons = 'MISSING START REASONS',
    MultipleStartReasons = 'MULTIPLE START REASONS',
    PreArtOrUnknownOutcomes ='PRE ART OR UNKNOWN OUTCOMES',
    MissingVlResults = 'MISSING VL RESULTS',
    ActiveClientsWithAdverseOutcomes = 'ACTIVE CLIENTS WITH ADVERSE OUTCOMES',
}

export interface VerificationData {
    'data_cleaning_datetime': string;
    'supervisors': string;
    'comments': string;
}

export class DataCleaningReportService extends ReportService {
    constructor() {
        super()
    }
    
    saveDataCleaningVerification(data: VerificationData) {
        return ApiCore.postJson('data_cleaning_supervisions', data)
    }

    getCleaningToolReport(indicator: CtIndicator) {
        return this.getReport('art_data_cleaning_tools', { 
            'report_name' : indicator 
        })
    }

    getEnrolledOnArtBeforeBirth() {
        return ApiCore.getJson('enrolled_on_art_before_birth')
    }

    getIncompleteVisits() {
        return this.getReport('incomplete_visits', {
            'tool_name': 'INCOMPLETE VISITS'
        })
    }

    getSupervisionHistory() {
        return this.getReport('data_cleaning_supervisions')
    }
}
