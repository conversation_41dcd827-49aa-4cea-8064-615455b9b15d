import { AgeGroup, CommonRowData, Gender, ReportService } from "./report_service";
import { AGE_GROUPS } from "@/constants";
import { get } from "lodash";

export const RETENTION_INDICATORS = {
    initiated_1: "Initiated one month",
    completed_1: "Completed one month",
    initiated_3: "Initiated Three months",
    completed_3: "Completed Three months",
    initiated_6: "Initiated Six months",
    completed_6: "Completed Six months",
};

export type RetentionIndicator = keyof typeof RETENTION_INDICATORS;
export type RetentionIndicatorData = Record<RetentionIndicator, Array<number>>;
export type RetentionRowData = RetentionIndicatorData & CommonRowData;

export class RetentionReportService extends ReportService {
    constructor() {
        super();
    }

    async generate() {
        const data = await this.getReport(`programs/${this.programId}/reports/retention`);
        const males: Array<RetentionRowData> = [];
        const females: Array<RetentionRowData> = [];

        AGE_GROUPS.forEach((ageGroup) => {
            males.push(this.buildRowData(data, "M", ageGroup));
            females.push(this.buildRowData(data, "F", ageGroup));
        });
        return [...females, ...males];
    }

    private getIndicatorData(data: Array<any>, gender: Gender, age: AgeGroup) {
        return data.filter(({ gender: g, age_group }) => g === gender && age_group === age).map(({ patient_id }) => patient_id);
    }

    private buildRowData(data: any, gender: Gender, ageGroup: AgeGroup) {
        const months = [1, 3, 6];
        const defaultRow = { ageGroup, gender } as RetentionRowData;
        for (const month of months) {
            const all = get(data, `${month}.all`, []) as any[];
            const retained = get(data, `${month}.all`, []) as any[];
            defaultRow[`initiated_${month}` as RetentionIndicator] = this.getIndicatorData(all, gender, ageGroup);
            defaultRow[`completed_${month}` as RetentionIndicator] = this.getIndicatorData(retained, gender, ageGroup);
        }
        return defaultRow;
    }
}
