import { AGE_GROUPS, PROGRAM_ID } from "@/constants";
import { uniq, isEmpty } from "lodash";
import { toStandardFmt, toDisplayFmt, today } from "@/utils/his_date";
import { parameterizeUrl } from "@/utils/Url";
import dayjs from "dayjs";
import { ApiCore } from "emr-api-client";
import ApiClient from "../api_client";

export interface QuarterInterface {
    name: string;
    start: string;
    end: string;
}

export type AgeGroup = (typeof AGE_GROUPS)[number];
export type Gender = "M" | "F";
export interface MaternityData {
    FBf: Array<number>;
    FP: Array<number>;
}

export interface CommonRowData {
    gender: Gender;
    ageGroup: AgeGroup;
}

export type PatientData = Record<string, any>;
export type IndicatorData = Record<string, Array<number | PatientData>>;
export type AggregatedReportData<T = any, K = any> = Record<
    Gender,
    {
        rows: Array<T>;
        aggregate: K;
    }
>;

export class ReportService {
    date: string;
    startDate: string;
    endDate: string;
    programId: number;
    useDefaultParams: boolean;

    constructor() {
        this.programId = PROGRAM_ID;
        this.date = today();
        this.endDate = "";
        this.startDate = "";
        this.useDefaultParams = true;
    }

    setDate(date: string) {
        this.date = toStandardFmt(date);
    }

    setStartDate(date: string) {
        this.startDate = toStandardFmt(date);
    }

    setEndDate(date: string) {
        this.endDate = toStandardFmt(date);
    }

    getDateIntervalPeriod() {
        return this.startDate && this.endDate ? `${toDisplayFmt(this.startDate)} - ${toDisplayFmt(this.endDate)}` : "-";
    }

    protected buildParams(params?: any) {
        let p: any = {};
        if (this.useDefaultParams) {
            p["date"] = this.date;
            p["program_id"] = this.programId;
        }
        if (this.startDate) p["start_date"] = this.startDate;
        if (this.endDate) p["end_date"] = this.endDate;
        if (params) p = { ...p, ...params };
        return p;
    }

    async getReport<T = any>(name: string, params?: any) {
        const res = await ApiClient.get(parameterizeUrl(name, this.buildParams(params)));
        return res!.json();
    }

    static getReportQuarters(maxQuarters = 5) {
        const qtrs: QuarterInterface[] = [];
        let currDate = new Date();
        let currYear = currDate.getFullYear();
        const curYr = currYear;

        currDate = new Date(`${curYr}-${currDate.getMonth() + 1}-${currDate.getDate()} 00:00`);
        const currentQtr = this.getQtrByDate(currDate);
        let qtr = currentQtr.qtr;
        let i = 0;

        if (qtr === 4) qtrs.push(this.buildQtrObj("Q1", currYear + 1));

        while (i < maxQuarters) {
            // Add following quarter
            if (i === 0 && qtr < 4) qtrs.push(this.buildQtrObj(`Q${qtr + 1}`, currYear));

            qtrs.push(this.buildQtrObj(`Q${qtr}`, currYear));
            qtr = qtr > 0 ? (qtr -= 1) : qtr;
            currYear = qtr == 0 ? currYear - 1 : currYear;
            qtr = qtr == 0 ? (qtr += 4) : qtr;
            i++;
        }
        return qtrs;
    }

    static getQtrByDate(date: Date) {
        const qtrBounds: any = this.getQuarterBounds(date.getFullYear());
        for (const qtr in qtrBounds) {
            const { start, end } = qtrBounds[qtr];
            if (date >= new Date(start) && date <= new Date(end)) {
                return qtrBounds[qtr];
            }
        }
        return null;
    }

    static buildQtrObj(qtrName: string, year: number): QuarterInterface {
        const qtrBounds: any = this.getQuarterBounds(year);
        const { start, end } = qtrBounds[qtrName];
        return {
            start,
            end,
            name: `${qtrName} ${year}`,
        };
    }

    static getQuarterBounds(year: number) {
        const daysInMonth = (m: string) => dayjs(`${year}-${m}-01`).daysInMonth();
        const startMonth = (m: string) => `${year}-${m}-01 00:00`;
        const endMonth = (m: string) => `${year}-${m}-${daysInMonth(m)} 00:00`;

        return {
            Q1: {
                qtr: 1,
                start: startMonth("01"),
                end: endMonth("03"),
            },
            Q2: {
                qtr: 2,
                start: startMonth("04"),
                end: endMonth("06"),
            },
            Q3: {
                qtr: 3,
                start: startMonth("07"),
                end: endMonth("09"),
            },
            Q4: {
                qtr: 4,
                start: startMonth("10"),
                end: endMonth("12"),
            },
        };
    }

    private async getMaternalStatus(patientIds: number[], reportDefinition = "pepfar") {
        if (isEmpty(patientIds)) return { FP: [], FBf: [] };
        const url = parameterizeUrl("vl_maternal_status", this.buildParams({ report_definition: reportDefinition }));
        const res = await ApiCore.postJson<MaternityData>(url, {
            patient_ids: patientIds,
        });
        return res.data ?? { FP: [], FBf: [] };
    }

    async buildMaternityAgreggateRows(femaleData: IndicatorData, reportDefinition = "pepfar") {
        const females = uniq(
            Object.values(femaleData)
                .flat(1)
                .map((entry) => (entry instanceof Object ? entry.patient_id : entry))
        ) as Array<number>;
        const indicators = Object.keys(femaleData);
        const mStatus = await this.getMaternalStatus(females, reportDefinition);
        const allFp = mStatus.FBf.concat(mStatus.FP);
        return ["FP", "FNP", "FBf"].map((gender) => {
            return indicators.reduce(
                (row, indicator) => {
                    return {
                        [indicator]: femaleData[indicator].filter((idOrPatient) => {
                            const id = idOrPatient instanceof Object ? idOrPatient.patient_id : idOrPatient;
                            return gender === "FNP" ? !allFp.includes(id) : mStatus[gender as keyof MaternityData].includes(id);
                        }),
                        ...row,
                    };
                },
                {
                    ageGroup: "All",
                    gender,
                } as Record<string, any>
            );
        });
    }
}
