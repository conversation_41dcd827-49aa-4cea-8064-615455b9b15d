import { AGE_GROUPS, LAB_PROGRAM_ID } from "@/constants";
import { AgeGroup, Gender, ReportService } from "./report_service";

export interface TestMeasure {
  name: string;
  modifier: string;
  value: string;
}

export interface TestResult {
  accession_number: string;
  result_id: number;
  result_date: string;
  patient_id: number;
  order_date: string;
  test: string;
  gender: string;
  reason_for_test: string;
  reason_for_test_obs_id: number;
  arv_number: string;
  birthdate: string;
  age_group: string;
  measures: Array<TestMeasure>;
}

export type PatientLevelData = Array<TestResult & {
  test_name: string;
  result: string
}>

export type DisaggregatedLabData = Array<{
  ageGroup: AgeGroup;
  gender: Gender;
  viral_load: Array<number>
}>;

export interface LabResultRowData {
  patientLevelData: PatientLevelData;
  disaggregatedData: DisaggregatedLabData;
}

export class LabReportService extends ReportService {
  constructor(){
    super();
    this.programId = LAB_PROGRAM_ID;
  }

  buildLabPatientLevelData(data: Array<TestResult>): PatientLevelData {
    return data.flatMap(result => result.measures.map(measure => ({
      ...result,
      test_name: measure.name,
      result: `${measure.modifier} ${measure.value}`
    })))
  }

  buildLabResultRowData(data: PatientLevelData, ageGroup: AgeGroup, gender: Gender) {
    const viral_load = data.filter(p => p.gender === gender && p.age_group === ageGroup)
      .map(({patient_id}) => patient_id);

    return {
      ageGroup,
      gender,
      viral_load
    }
  }

  buildLabDisaggregatedData(data: PatientLevelData): DisaggregatedLabData {
    const males: DisaggregatedLabData = [];
    const females: DisaggregatedLabData = [];

    AGE_GROUPS.forEach(ageGroup => {
      males.push(this.buildLabResultRowData(data, ageGroup, "M"));
      females.push(this.buildLabResultRowData(data, ageGroup, "F"));
    });

    return [
      ...females,
      ...males
    ]
  }

  async getLabResultReport(): Promise<LabResultRowData> {
    const data = await this.getReport('lab_test_results');
    if(!data) return {} as LabResultRowData
    const patientLevelData = this.buildLabPatientLevelData(data);
    const disaggregatedData = this.buildLabDisaggregatedData(patientLevelData)
    return {
      patientLevelData,
      disaggregatedData
    }
  }
}