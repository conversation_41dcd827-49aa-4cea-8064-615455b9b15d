import { get } from "lodash";
import { AgeGroup, AggregatedReportData, CommonRowData, Gender, ReportService } from "./report_service";
import { AGE_GROUPS } from "@/constants";

export const TX_ML_INDICATORS = ["Died", "IIT <3 mo", "IIT 3-5 mo", "IIT 6+ mo", "Transferred out", "Refused (Stopped)"];

export const TX_TB_INDICATORS = {
    tx_curr: "TX_CURR",
    symptom_screen_alone: "Symptom Screen (alone)",
    cxr_screen: "CXR Screen",
    mwrd_screen: "mWRD Screen",
    sceen_pos_new: "New on ART/Screen Positive",
    sceen_neg_new: "New on ART/Screen Negative",
    sceen_pos_prev: "Already on ART/Screen Positive",
    sceen_neg_prev: "Already on ART/Screen Negative",
    started_tb_new: "Started TB RX_New on ART",
    started_tb_prev: "Started TB RX_Prev on ART",
};

export const TX_MMD_INDICATORS = {
    less_than_three_months: "# of clients on <3 months of ARVs",
    three_to_five_months: "# of clients on 3 - 5 months of ARVs",
    greater_than_six_months: "# of clients on >= 6 months of ARVs",
};

export const TX_HIV_HTN_INDICATORS = {
    tx_curr: "TX_CURR",
    ever_diagnosed_htn: "Ever diagnosed with HTN",
    screened_for_htn: "Screened for HTN",
    newly_diagnosed_htn: "Newly diagnosed with HTN",
    controlled_htn: "Controlled HTN",
};

export type TxMmdIndicator = keyof typeof TX_MMD_INDICATORS;
export type TxTbIndicator = keyof typeof TX_TB_INDICATORS;

export type TxMmdIndicatorData = Record<TxMmdIndicator, Array<number>>;
export type TxTbIndicatorData = Record<TxTbIndicator, Array<number>>;
export type TxMlIndicatorData = Record<string, Array<number>>;

export type TxMmdRowData = TxMmdIndicatorData & CommonRowData;
export type TxTbRowData = TxTbIndicatorData & CommonRowData;
export type TxMlRowData = TxMlIndicatorData & CommonRowData;

export type TxMmdReportData = Record<AgeGroup, Record<Gender, Record<TxMmdIndicator, Array<number>>>>;
export type TxTbReportData = Record<AgeGroup, Record<Gender, Record<TxTbIndicator, Array<number>>>>;
export type TxMlReportData = Record<AgeGroup, Record<Gender, Array<Array<number>>>>;

export class TxReportService extends ReportService {
    reportType: "pepfar" | "moh" | "clinic";
    initialize: boolean;
    constructor() {
        super();
        this.reportType = "pepfar";
        this.initialize = true;
    }

    setReportType(reportType: typeof this.reportType) {
        this.reportType = reportType;
    }

    setInitialize(initialize: boolean) {
        this.initialize = initialize;
    }

    async getTxCurrMMDReport(rebuild: boolean = false) {
        const data = await this.getReport<TxMmdReportData>(`programs/${this.programId}/reports/tx_curr_mmd`, {
            definition: this.reportType,
            rebuild,
        });
        if (!data) return [];
        const aggregated = this.txMmdReportBuilder(data);
        const femaleAggregates = (await this.buildMaternityAgreggateRows(aggregated.F.aggregate)) as Array<TxMmdRowData>;
        return [...aggregated.F.rows, ...aggregated.M.rows, { ageGroup: "All", gender: "M", ...aggregated.M.aggregate }, ...femaleAggregates];
    }

    async getTxMlReport(rebuild: boolean) {
        const data = await this.getReport<TxMlReportData>("tx_ml", { rebuild });
        if (!data) return [];
        const aggregated = this.txMlReportBuilder(data);
        const femaleAggregates = await this.buildMaternityAgreggateRows(aggregated.F.aggregate);
        return [...aggregated.F.rows, ...aggregated.M.rows, { ageGroup: "All", gender: "M", ...aggregated.M.aggregate }, ...femaleAggregates];
    }

    getTxRttReport(rebuild: boolean) {
        return this.getReport("tx_rtt", {
            rebuild,
        });
    }

    getClinicTxRtt(rebuild: boolean) {
        return this.getReport(`programs/${this.programId}/reports/clinic_tx_rtt`, {
            rebuild,
        });
    }

    async getTxTbReport(rebuild_outcome: boolean) {
        return this.getReport(`programs/${this.programId}/reports/tx_tb`, {
            rebuild_outcome,
        });
    }

    getTxNewReport(rebuild: boolean) {
        return this.getReport(`programs/${this.programId}/reports/tx_new`, {
            rebuild,
        });
    }

    async getTxHivHtnReport(rebuild: boolean) {
        const res = await this.getReport(`programs/${this.programId}/reports/tx_hiv_htn`, {
            rebuild,
        });
        const flattedData = Object.entries(res).reduce(
            (result, [ageGroup, data]: any) => {
                if (!/Unknown|All/i.test(ageGroup)) {
                    result.M.push({ ageGroup, gender: "Male", ...data["M"] });
                    result.F.push({ ageGroup, gender: "Female", ...data["F"] });
                }
                return result;
            },
            { M: [] as Array<any>, F: [] as Array<any> }
        );
        return [
            ...flattedData.F,
            ...flattedData.M,
            { ageGroup: "All", gender: "Male", ...res.All.Male },
            { ageGroup: "All", gender: "FP", ...res.All.FP },
            { ageGroup: "All", gender: "FNP", ...res.All.FNP },
            { ageGroup: "All", gender: "FBf", ...res.All.FBf },
        ];
    }

    private mapTxMlIndcators(indicators: Array<any>) {
        return TX_ML_INDICATORS.reduce((row, indicator, index) => {
            row[indicator] = indicators[index] ?? [];
            return row;
        }, {} as TxMlIndicatorData);
    }

    private txMlReportBuilder(data?: TxMlReportData) {
        const result: AggregatedReportData<TxMlRowData> = {
            M: { rows: [], aggregate: {} as TxMlIndicatorData },
            F: { rows: [], aggregate: {} as TxMlIndicatorData },
        };

        AGE_GROUPS.forEach((ageGroup: AgeGroup) => {
            (["F", "M"] as Array<Gender>).forEach((gender) => {
                const row = this.mapTxMlIndcators(get(data, `${ageGroup}.${gender}`, []));
                result[gender].rows.push({ ageGroup, gender, ...row } as TxMlRowData);
                this.aggregateIndicatorData(result, row, gender);
            });
        });
        return result;
    }

    private aggregateIndicatorData<T extends Record<string, number[]>>(data: any, newData: T, gender: Gender) {
        Object.entries(newData).forEach(([indicator, values]) => {
            data[gender].aggregate[indicator] = [...values, ...(data[gender].aggregate[indicator] ?? [])];
        });
    }

    private txMmdReportBuilder(data: TxMmdReportData) {
        return Object.keys(data).reduce(
            (result, ageGroup: AgeGroup) => {
                if (!/Unknown/i.test(ageGroup)) {
                    const femaleIndicatorData = get(data, `${ageGroup}.Female`) as TxMmdIndicatorData;
                    result.F.rows.push({ ageGroup, gender: "F", ...femaleIndicatorData });
                    this.aggregateIndicatorData<TxMmdIndicatorData>(result, femaleIndicatorData, "F");

                    const maleIndicatorData = get(data, `${ageGroup}.Male`) as TxMmdIndicatorData;
                    result.M.rows.push({ ageGroup, gender: "M", ...maleIndicatorData });
                    this.aggregateIndicatorData<TxMmdIndicatorData>(result, maleIndicatorData, "M");
                }
                return result;
            },
            {
                M: { rows: [] as Array<TxMmdRowData>, aggregate: {} as TxMmdIndicatorData },
                F: { rows: [] as Array<TxMmdRowData>, aggregate: {} as TxMmdIndicatorData },
            }
        );
    }
}
