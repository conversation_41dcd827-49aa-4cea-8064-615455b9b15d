import { ReportService } from "./report_service";

export type ReportData = Record<number, {
    given_name: string;
    family_name: string;
    gender: string;
    birthdate: string;
    arv_number: string;
    national_id: string;
    dates: string[];
}>

export class IncompleteVisitsReportService extends ReportService {
  constructor() {
    super()
  }

  async generate() {
    const data = await this.getReport<ReportData>('incomplete_visits', {
        "tool_name": "INCOMPLETE VISITS",
    })

    return Object.values(data ?? {});
  }
}
