import { AGE_GROUPS, REGIMENS } from "@/constants";
import { ProgramReportService } from "./program_report_service";
import { AgeGroup, Gender } from "./report_service";

type Regimen = typeof REGIMENS[number] | "unknown" | "total";
type RegimensData = Record<Regimen, Array<number>>;
type ExtendedGender = Gender | "FBf" | "FNP" | "FP";
export interface DisagReportData {
  age_group: AgeGroup | "unknown";
  gender: ExtendedGender;
  regimens: RegimensData;
}

export class DisaggregatedReportService extends ProgramReportService {
  private gender: string;
  private ageGroup: string;

  constructor() {
    super()
    this.gender = '';
    this.ageGroup = AGE_GROUPS[0]
  }

  setAgeGroup(ageGroup: string) {
    this.ageGroup = ageGroup;
  }

  setGender(gender: string) {
    this.gender = gender;
  }

  getGender() {
    return this.gender;
  }

  getTxIpt() {
    return this.getReport('clients_given_ipt', { 
      'gender': this.gender, 
      'age_group': this.ageGroup 
    })
  }

  getTxCurrTB() {
    return this.getReport('screened_for_tb', { 
      'gender': this.gender, 
      'age_group': this.ageGroup 
    })
  }

  getRegimenDistribution() {
    return this.getReport('disaggregated_regimen_distribution', {
      'gender': this.gender,
      'age_group': this.ageGroup
    })
  }

  async getDisaggReport(rebuild = false, definition: 'pepfar'|'moh' ='moh') {
    const data = await this.generate(`cohort_disaggregated`, {
      rebuild, 
      definition
    })

    return this.buildDisagRowData(data);
  }

  buildDisagRowData(data: Array<DisagReportData>){
    const sortedData = data.reduce((acc, curr) => {
      if(/unknown/i.test(curr.age_group)) return acc;
      acc[curr.gender].push(curr);
      return acc;
    }, { M: [], F: [], FP: [], FBf: [], FNP: []} as Record<ExtendedGender, Array<DisagReportData>>)

    return [
      ...sortedData.F,
      ...sortedData.M,
      ...sortedData.FP,
      ...sortedData.FNP,
      ...sortedData.FBf
    ]
  }
}
