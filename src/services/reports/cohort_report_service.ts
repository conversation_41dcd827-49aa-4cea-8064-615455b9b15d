import { ReportService } from "./report_service";
import ApiClient from "../api_client";
import { parameterizeUrl } from "@/utils/Url";

export class CohortReportService extends ReportService {
    private quarter: string;
    private regenerate: boolean;
    constructor() {
        super();
        this.regenerate = false;
        this.quarter = "";
    }

    setQuarter(quarter: string) {
        this.quarter = quarter;
    }

    setRegenerate(regenete: boolean) {
        this.regenerate = regenete;
    }

    getCohortDrillDown(resourceId: string) {
        const url = parameterizeUrl("cohort_report_drill_down", {
            id: resourceId,
            date: this.date,
            program_id: this.programId,
        });
        return ApiClient.get(url);
    }

    qaurterRequestParams() {
        return {
            name: this.quarter,
            regenerate: this.regenerate,
        };
    }

    datePeriodRequestParams() {
        return {
            name: `Cohort-${this.startDate}-${this.endDate}`,
            start_date: this.startDate,
            end_date: this.endDate,
            regenerate: this.regenerate,
        };
    }

    async requestCohort(params: any) {
        const url = parameterizeUrl(`programs/${this.programId}/reports/cohort`, params);
        try {
            const response = await ApiClient.get(url);
            if (!response) {
                return { ok: false, data: null, httpStatusResponse: 0 };
            }

            const httpStatusResponse = response.status;

            if (response.ok) {
                // Check if there's content to parse
                const text = await response.text();
                let data = null;

                if (text && text.trim() !== "") {
                    try {
                        data = JSON.parse(text);
                    } catch (parseError) {
                        console.error("Error parsing JSON response:", parseError);
                        return { ok: false, data: null, error: parseError, httpStatusResponse };
                    }
                }

                return { ok: true, data, httpStatusResponse };
            } else {
                return { ok: false, data: null, httpStatusResponse };
            }
        } catch (error) {
            console.error("Error fetching cohort data:", error);
            return { ok: false, data: null, error, httpStatusResponse: 0 };
        }
    }
}
