import { sortBy } from "@/utils/Arrays";
import { AgeGroup, AggregatedReportData, CommonRowData, Gender, ReportService } from "./report_service";
import { AGE_GROUPS } from "@/constants";
import { isEmpty } from "lodash";
import { AGE_GROUP } from "./survival_analysis_report_service";

export const TPT_INITIATION_INDICATORS = {
    "3HP_new": "3H (Started New on ART)",
    "6H_new": "6H (Started New on ART)",
    "3HP_prev": "3H (Started Previously on ART)",
    "6H_prev": "6H (Started Previously on ART)",
};

export const TPT_OUTCOMES_INDICATORS = {
    started_tpt_new: "Started TPT (New on ART)",
    started_tpt_prev: "Started TPT (Previous on ART)",
    completed_tpt_new: "Completed TPT(New on ART)",
    completed_tpt_prev: "Completed TPT(Previous on ART)",
    not_completed_tpt: "Not completed TPT",
    died: "Died",
    defaulted: "Defaulted",
    stopped: "Stopped ART",
    transfer_out: "Transfered Out",
    confirmed_tb: "Confirmed TB",
    pregnant: "Pregnant",
    breast_feeding: "Breastfeeding",
    skin_rash: "Skin rash",
    peripheral_neuropathy: "Peripheral neuropathy",
    yellow_eyes: "Yellow eyes",
    nausea: "Nausea",
    dizziness: "Dizziness",
};

export interface TptInitiationPatientData {
    patient_id: number;
    birthdate: string;
    arv_number: string;
    gender: Gender;
    dispensation_date: string;
    art_start_date: string;
    tpt_start_date: string;
}

export type TptInitiationIndicator = keyof typeof TPT_INITIATION_INDICATORS;
export type Location = string;
export type TptInitiationIndicatorData = Record<TptInitiationIndicator, Array<TptInitiationPatientData>>;
export type TptInitiationRowData = TptInitiationIndicatorData &
    CommonRowData & {
        location: string;
    };

export type TptInitiationReportData = Record<AgeGroup, Record<TptInitiationIndicator, Record<Gender, Array<TptInitiationPatientData>>>> & {
    Location: string;
};

export type TptOutcomesIndicator = keyof typeof TPT_OUTCOMES_INDICATORS;
export type TptOutcomesIndicatorData = Record<TptOutcomesIndicator, Array<number>>;
export interface TptOutcomePatient {
    patient_id: number;
    gender: string;
}

export type TptOutcomesReportData = Array<Record<TptOutcomesIndicator | "age_group" | "tpt_type", string | Array<TptOutcomePatient>>>;
export type TptOutcomeRowData = TptInitiationIndicatorData & {
    age_group: AGE_GROUP;
    tpt_type: string;
};
export class TptReportService extends ReportService {
    constructor() {
        super();
    }

    async getTtpOutcomes() {
        const data = (await this.getReport<TptOutcomesReportData>(`programs/${this.programId}/reports/tpt_outcome`)) ?? [];
        const aggregatedData = this.aggregateTptOutcomes(data);
        const aggregatefemales = this.getTotalTptOutcomeFemales(aggregatedData.females);
        const sortedData = sortBy(this.tptOutcomesBuilder(data), "tpt_type");
        return [...sortedData, { age_group: "All", tpt_type: "M", ...aggregatedData.males }, ...aggregatefemales];
    }

    async getCohort() {
        const data = await this.getReport("moh_tpt");
        return sortBy(data ?? [], "gender");
    }

    async getTptNewInitiations() {
        return this.tptInitiationBuilder(await this.getReport(`programs/${this.programId}/reports/tpt_newly_initiated`));
    }

    private tptOutcomesBuilder(data: TptOutcomesReportData) {
        return data.map((d) =>
            Object.entries(d).reduce((row, [key, value]) => {
                row[key] = typeof value === "string" ? value : value.map(({ patient_id }) => patient_id);
                return row;
            }, {} as any)
        );
    }

    private getTotalTptOutcomeFemales(data: TptOutcomesIndicatorData) {
        return ["FP", "FNP", "FBf"].map((gender) => {
            const row: Record<string, any> = { age_group: "All", tpt_type: gender };
            for (const i in TPT_OUTCOMES_INDICATORS) {
                row[i] = data[i as TptOutcomesIndicator].filter((patient: number) => {
                    if (gender === "FP") return data.pregnant.includes(patient);
                    if (gender === "FBf") return data.breast_feeding.includes(patient);
                    return ![...data.pregnant, data.breast_feeding].includes(patient);
                });
            }
            return row;
        });
    }

    private aggregateTptOutcomes(data: TptOutcomesReportData) {
        return data.reduce(
            (acc: any, curr: any) => {
                for (const i in TPT_OUTCOMES_INDICATORS) {
                    if (!(i in acc.males)) acc.males[i] = [];
                    if (!(i in acc.females)) acc.females[i] = [];
                    if (isEmpty(curr[i])) continue;
                    curr[i].forEach((d: any) => {
                        if (d.gender === "F") acc.females[i].push(d.patient_id);
                        else acc.males[i].push(d.patient_id);
                    });
                }
                return acc;
            },
            { males: {}, females: {} }
        );
    }

    private aggregateTptInitiationData(
        gender: Gender,
        indicator: TptInitiationIndicator,
        data: Array<TptInitiationPatientData>,
        result: AggregatedReportData<TptInitiationRowData>
    ) {
        result[gender].aggregate[indicator] = [...(result[gender].aggregate[indicator] ?? []), ...data];
    }

    private tptInitiationBuilder(data = {} as TptInitiationReportData) {
        const defaultData: AggregatedReportData<TptInitiationRowData> = {
            M: { rows: [], aggregate: {} as TptInitiationIndicatorData },
            F: { rows: [], aggregate: {} as TptInitiationIndicatorData },
        };

        return AGE_GROUPS.reduce((result, ageGroup) => {
            const rows = Object.entries(data[ageGroup]).reduce(
                (row, [indicator, disag]) => {
                    this.aggregateTptInitiationData("M", indicator as TptInitiationIndicator, disag.M, result);
                    this.aggregateTptInitiationData("F", indicator as TptInitiationIndicator, disag.F, result);
                    row.M[indicator as TptInitiationIndicator] = disag.M;
                    row.F[indicator as TptInitiationIndicator] = disag.F;
                    return row;
                },
                {
                    M: { gender: "M", location: data.Location, ageGroup },
                    F: { gender: "F", location: data.Location, ageGroup },
                } as Record<Gender, TptInitiationRowData>
            );
            result.F.rows.push(rows.F);
            result.M.rows.push(rows.M);
            return result;
        }, defaultData);
    }
}
