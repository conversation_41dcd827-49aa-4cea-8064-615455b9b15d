/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActionButtons: typeof import('./components/ActionButtons.vue')['default']
    ActionSheetFooter: typeof import('./components/DataViews/actionsheet/ActionSheetFooter.vue')['default']
    ActionSheetMixin: typeof import('./components/DataViews/actionsheet/ActionSheetMixin.vue')['default']
    ActionSheetToolbar: typeof import('./components/DataViews/actionsheet/ActionSheetToolbar.vue')['default']
    ActivitySelectionModal: typeof import('./components/Modal/ActivitySelectionModal.vue')['default']
    AddDiagnosisModal: typeof import('./apps/NCD/components/ConsultationPlan/Diagnosis/AddDiagnosisModal.vue')['default']
    AddImmunizationSessionModal: typeof import('./components/Modal/AddImmunizationSessionModal.vue')['default']
    AddOtherOPDMedication: typeof import('./apps/NCD/components/ConsultationPlan/AddOtherOPDMedication.vue')['default']
    AddStockModal: typeof import('./components/StockManagement/AddStockModal.vue')['default']
    AddTA: typeof import('./components/Registration/Modal/AddTA.vue')['default']
    AddVillage: typeof import('./components/Registration/Modal/AddVillage.vue')['default']
    AdministerOtherVaccineModal: typeof import('./apps/Immunization/components/Modals/administerOtherVaccineModal.vue')['default']
    AdministerVaccineModal: typeof import('./apps/Immunization/components/Modals/administerVaccineModal.vue')['default']
    AdmittedforShortStayOutcome: typeof import('./apps/OPD/components/ConsultationPlan/AdmittedforShortStayOutcome.vue')['default']
    AEFIReport: typeof import('./apps/Immunization/components/Reports/AEFIReport.vue')['default']
    AEFIReportTemplate: typeof import('./apps/Immunization/components/Reports/AEFIReportTemplate.vue')['default']
    Alert: typeof import('./apps/Immunization/components/Modals/alert.vue')['default']
    Allergies: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment/Allergies.vue')['default']
    AllergiesContraindication: typeof import('./components/DashboardSegments/AllergiesContraindication.vue')['default']
    AncEnrollmentModal: typeof import('./components/Modal/AncEnrollmentModal.vue')['default']
    ARTAdherence: typeof import('./apps/ART/components/ARTAdherence.vue')['default']
    ARTAppointment: typeof import('./apps/ART/components/ARTAppointment.vue')['default']
    ARTDashboard: typeof import('./apps/ART/components/ARTDashboard.vue')['default']
    ArtDispensation: typeof import('./apps/ART/components/ArtDispensation.vue')['default']
    ArtFormWrapper: typeof import('./apps/ART/components/ArtFormWrapper.vue')['default']
    ARTHistory: typeof import('./apps/ART/components/ARTRegistration/ARTHistory.vue')['default']
    ARTIdentification: typeof import('./apps/ART/components/ARTRegistration/ARTIdentification.vue')['default']
    ArtOutcome: typeof import('./apps/ART/components/ArtOutcome.vue')['default']
    ARTPatientType: typeof import('./apps/ART/components/ARTPatientType.vue')['default']
    ARTVisitHeader: typeof import('./apps/ART/components/ARTVisitHeader.vue')['default']
    ARTVisitStepper: typeof import('./apps/ART/components/ARTVisitStepper.vue')['default']
    BaseKeyboard: typeof import('./components/Keyboard/BaseKeyboard.vue')['default']
    BaseTextInput: typeof import('./components/FormElements/BaseTextInput.vue')['default']
    BasicCard: typeof import('./components/BasicCard.vue')['default']
    BasicFooter: typeof import('./components/BasicFooter.vue')['default']
    BasicForm: typeof import('./components/BasicForm.vue')['default']
    BasicInputChangeUnits: typeof import('./components/BasicInputChangeUnits.vue')['default']
    BasicInputField: typeof import('./components/BasicInputField.vue')['default']
    BasicPhoneInputField: typeof import('./components/BasicPhoneInputField.vue')['default']
    BasicReportTemplate: typeof import('./components/Forms/BasicReportTemplate.vue')['default']
    BirthRegistration: typeof import('./components/Registration/BirthRegistration.vue')['default']
    BloodPressure: typeof import('./components/Graphs/BloodPressure.vue')['default']
    BottomNavBar: typeof import('./apps/Immunization/components/bottomNavBar.vue')['default']
    ButtonActionSheet: typeof import('./components/DataViews/actionsheet/ButtonActionSheet.vue')['default']
    ChangeStatus: typeof import('./apps/Immunization/components/ConsultationPlan/ChangeStatus.vue')['default']
    CheckInConfirmationModal: typeof import('./components/Modal/CheckInConfirmationModal.vue')['default']
    CheckPatientNationalID: typeof import('./components/CheckPatientNationalID.vue')['default']
    ClinicalAssessment: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment.vue')['default']
    ClinicCasesSeen: typeof import('./apps/OPD/components/reports/clinic/ClinicCasesSeen.vue')['default']
    ClinicDaysSelector: typeof import('./apps/ART/components/ClinicDaysSelector.vue')['default']
    ClinicRegistrationReport: typeof import('./apps/OPD/components/reports/clinic/ClinicRegistrationReport.vue')['default']
    ClinicWithNIDsReport: typeof import('./apps/OPD/components/reports/clinic/ClinicWithNIDsReport.vue')['default']
    Complications: typeof import('./apps/OPD/components/ConsultationPlan/Complications.vue')['default']
    ComplicationsScreening: typeof import('./apps/NCD/components/ConsultationPlan/ComplicationsScreening.vue')['default']
    ConfirmationSheet: typeof import('./components/DataViews/actionsheet/ConfirmationSheet.vue')['default']
    ConfirmDiagnosis: typeof import('./apps/NCD/components/ConsultationPlan/ConfirmDiagnosis.vue')['default']
    ConfirmModal: typeof import('./apps/NCD/components/confirmModal.vue')['default']
    ConfirmPrinting: typeof import('./components/ConfirmPrinting.vue')['default']
    ConfirmVoidingModal: typeof import('./components/Modal/ConfirmVoidingModal.vue')['default']
    ConnectionError: typeof import('./components/ConnectionError.vue')['default']
    Consultation: typeof import('./apps/ART/components/Consultation.vue')['default']
    CountDown: typeof import('./components/CountDown.vue')['default']
    Country: typeof import('./components/Registration/Country.vue')['default']
    CurrentLocation: typeof import('./components/Registration/CurrentLocation.vue')['default']
    CustomDatePicker: typeof import('./apps/Immunization/components/customDatePicker.vue')['default']
    CustomSlider: typeof import('./apps/Immunization/components/customSlider.vue')['default']
    CustomVaccine: typeof import('./apps/Immunization/components/customVaccine.vue')['default']
    Dashboard: typeof import('./apps/NCD/components/Dashboard/Dashboard.vue')['default']
    DashBox: typeof import('./components/DashBox.vue')['default']
    DateInputField: typeof import('./components/DateInputField.vue')['default']
    DatePicker: typeof import('./components/DatePicker.vue')['default']
    DDERequestIDsModal: typeof import('./components/Modal/DDERequestIDsModal.vue')['default']
    DeathOutcome: typeof import('./apps/OPD/components/ConsultationPlan/DeathOutcome.vue')['default']
    DeduplicateClients: typeof import('./apps/Immunization/components/Deduplication/deduplicateClients.vue')['default']
    DemographicBar: typeof import('./components/DemographicBar.vue')['default']
    DiagnosesHistory: typeof import('./components/DashboardSegments/DiagnosesHistory.vue')['default']
    Diagnosis: typeof import('./apps/NCD/components/ConsultationPlan/Diagnosis.vue')['default']
    DiagnosisByAddress: typeof import('./apps/OPD/components/reports/clinic/DiagnosisByAddress.vue')['default']
    DischargedHome: typeof import('./apps/OPD/components/ConsultationPlan/DischargedHome.vue')['default']
    DispensationSummary: typeof import('./apps/OPD/components/dispensationSummary.vue')['default']
    DispensedMedication: typeof import('./apps/OPD/components/dispensedMedication.vue')['default']
    DrilldownTable: typeof import('./components/DrilldownTable.vue')['default']
    DrugRefill: typeof import('./components/ProfileModal/DrugRefill.vue')['default']
    DrugsReport: typeof import('./apps/OPD/components/reports/clinic/DrugsReport.vue')['default']
    DueModal: typeof import('./components/DashboardModal/DueModal.vue')['default']
    DynamicButton: typeof import('./components/DynamicButton.vue')['default']
    DynamicComponent: typeof import('./components/DynamicComponent.vue')['default']
    DynamicDispositionList: typeof import('./components/DynamicDispositionList.vue')['default']
    DynamicList: typeof import('./apps/OPD/components/DynamicList.vue')['default']
    DynamicListOPDcopy: typeof import('./apps/OPD/components/DynamicListOPDcopy.vue')['default']
    EditImmunizationSessionModal: typeof import('./components/Modal/EditImmunizationSessionModal.vue')['default']
    EIPMReport: typeof import('./apps/Immunization/components/Reports/EIPMReport.vue')['default']
    EIR_Report: typeof import('./apps/Immunization/components/Reports/EIR_Report.vue')['default']
    EIR_Report_Template: typeof import('./apps/Immunization/components/Reports/EIR_Report_Template.vue')['default']
    EmergencyRefill: typeof import('./apps/ART/components/EmergencyRefill.vue')['default']
    EnterResultModal: typeof import('./apps/NCD/components/ConsultationPlan/Investigation/EnterResultModal.vue')['default']
    ExternalReferrals: typeof import('./apps/NCD/components/Dashboard/ExternalReferrals.vue')['default']
    FacilityInformationBar: typeof import('./components/FacilityInformationBar.vue')['default']
    FacilitySelector: typeof import('./apps/ART/components/FacilitySelector.vue')['default']
    FamilyHistory: typeof import('./apps/NCD/components/Enrollment/FamilyHistory.vue')['default']
    FamilyHistoryNCDNumber: typeof import('./apps/OPD/components/Enrollment/FamilyHistoryNCDNumber.vue')['default']
    FieldMixin: typeof import('./components/FormElements/FieldMixin.vue')['default']
    FilingNumberManagement: typeof import('./apps/ART/components/ARTRegistration/FilingNumberManagement.vue')['default']
    FilingNumberView: typeof import('./components/FormElements/FilingNumberView.vue')['default']
    FollowUpVisitModal: typeof import('./apps/Immunization/components/Modals/followUpVisitModal.vue')['default']
    FormElementError: typeof import('./components/Forms/FormElementError.vue')['default']
    FullScreenModifier: typeof import('./components/FullScreenModifier.vue')['default']
    GetDataDuration: typeof import('./apps/OPD/components/reports/GetDataDuration.vue')['default']
    GuardianInformation: typeof import('./components/Registration/GuardianInformation.vue')['default']
    HisArtRegimenSelection: typeof import('./components/FormElements/HisArtRegimenSelection.vue')['default']
    HisCardSelector: typeof import('./components/FormElements/HisCardSelector.vue')['default']
    HisComplaintsPicker: typeof import('./components/FormElements/HisComplaintsPicker.vue')['default']
    HisDataTable: typeof import('./components/FormElements/HisDataTable.vue')['default']
    HisDateInput: typeof import('./components/FormElements/HisDateInput.vue')['default']
    HisDateKeypad: typeof import('./components/Keyboard/HisDateKeypad.vue')['default']
    HisDynamicNavFooter: typeof import('./components/HisDynamicNavFooter.vue')['default']
    HisFormInfoCard: typeof import('./components/DataViews/HisFormInfoCard.vue')['default']
    HisInfiniteScrollMultipleSelect: typeof import('./components/FormElements/HisInfiniteScrollMultipleSelect.vue')['default']
    HisKeyboard: typeof import('./components/Keyboard/HisKeyboard.vue')['default']
    HisKeypad: typeof import('./components/Keyboard/HisKeypad.vue')['default']
    HisModalKeyboard: typeof import('./components/Keyboard/HisModalKeyboard.vue')['default']
    HisMonthlyDays: typeof import('./components/FormElements/HisMonthlyDays.vue')['default']
    HisMultiSelectGrid: typeof import('./components/FormElements/HisMultiSelectGrid.vue')['default']
    HisNextVisitInterval: typeof import('./components/FormElements/HisNextVisitInterval.vue')['default']
    HisNumberInput: typeof import('./components/FormElements/HisNumberInput.vue')['default']
    HisSelect: typeof import('./components/FormElements/HisSelect.vue')['default']
    HisStandardForm: typeof import('./components/Forms/HisStandardForm.vue')['default']
    HIVClinicRegistration: typeof import('./apps/ART/components/ARTRegistration/HIVClinicRegistration.vue')['default']
    HIVConfirmation: typeof import('./apps/ART/components/ARTRegistration/HIVConfirmation.vue')['default']
    HMIS15: typeof import('./apps/OPD/components/reports/moh/HMIS/HMIS15.vue')['default']
    HMIS17: typeof import('./apps/OPD/components/reports/moh/HMIS/HMIS17.vue')['default']
    HMISTemplate: typeof import('./apps/OPD/components/reports/moh/HMIS/HMISTemplate.vue')['default']
    HomeLocation: typeof import('./components/Registration/HomeLocation.vue')['default']
    HTSLinkageNumber: typeof import('./apps/ART/components/ARTRegistration/HTSLinkageNumber.vue')['default']
    IDSRMonthly: typeof import('./apps/OPD/components/reports/moh/IDSR/IDSRMonthly.vue')['default']
    IDSRTableTemplate: typeof import('./apps/OPD/components/reports/moh/IDSR/IDSRTableTemplate.vue')['default']
    IDSRWeekly: typeof import('./apps/OPD/components/reports/moh/IDSR/IDSRWeekly.vue')['default']
    ImmunizationDashboard: typeof import('./apps/Immunization/components/ImmunizationDashboard.vue')['default']
    ImmunizationGroupGraph: typeof import('./apps/Immunization/components/Graphs/ImmunizationGroupGraph.vue')['default']
    ImmunizationNextAppointment: typeof import('./apps/Immunization/components/ImmunizationNextAppointment.vue')['default']
    ImmunizationServices: typeof import('./apps/Immunization/components/ConsultationPlan/ImmunizationServices.vue')['default']
    ImmunizationTrendsGraph: typeof import('./apps/Immunization/components/Graphs/ImmunizationTrendsGraph.vue')['default']
    InfoActionSheet: typeof import('./components/DataViews/actionsheet/InfoActionSheet.vue')['default']
    InfoListActionSheet: typeof import('./components/DataViews/actionsheet/InfoListActionSheet.vue')['default']
    InitialHealthStatus: typeof import('./apps/ART/components/ARTRegistration/InitialHealthStatus.vue')['default']
    IntervalCard: typeof import('./components/DataViews/IntervalCard.vue')['default']
    Investigations: typeof import('./apps/NCD/components/ConsultationPlan/Investigations.vue')['default']
    InvestigationsGrid: typeof import('./components/PatientProfileGrid/InvestigationsGrid.vue')['default']
    InvestigationsModal: typeof import('./components/ProfileModal/InvestigationsModal.vue')['default']
    LabModal: typeof import('./components/Lab/LabModal.vue')['default']
    LabOrderResults: typeof import('./apps/NCD/components/ConsultationPlan/Investigation/labOrderResults.vue')['default']
    LabResults: typeof import('./components/Lab/LabResults.vue')['default']
    LabTestsHistory: typeof import('./components/DashboardSegments/LabTestsHistory.vue')['default']
    LabViewResultsModal: typeof import('./apps/NCD/components/ConsultationPlan/Investigation/LabViewResultsModal.vue')['default']
    LaReport: typeof import('./apps/OPD/components/reports/clinic/LaReport.vue')['default']
    LevelOfConsciousness: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment/LevelOfConsciousness.vue')['default']
    List: typeof import('./components/List.vue')['default']
    ListPicker: typeof import('./components/ListPicker.vue')['default']
    LoaderFormPlaceholder: typeof import('./components/Forms/LoaderFormPlaceholder.vue')['default']
    LotNumberList: typeof import('./apps/Immunization/components/Modals/lotNumberList.vue')['default']
    Malaria_report: typeof import('./apps/OPD/components/reports/moh/malaria_report.vue')['default']
    ManageGuardian: typeof import('./apps/ART/components/Reception/ManageGuardian.vue')['default']
    ManageVillageModal: typeof import('./components/Modal/ManageVillageModal.vue')['default']
    Medication: typeof import('./apps/NCD/components/ConsultationPlan/Medication.vue')['default']
    MedicationDetailsModal: typeof import('./apps/NCD/components/ConsultationPlan/MedicationDetailsModal.vue')['default']
    MedicationHistory: typeof import('./apps/NCD/components/MedicationHistory.vue')['default']
    MedicationSearchInput: typeof import('./apps/NCD/components/ConsultationPlan/MedicationSearchInput.vue')['default']
    MedicationsGrid: typeof import('./components/PatientProfileGrid/MedicationsGrid.vue')['default']
    MedicationsModal: typeof import('./components/ProfileModal/MedicationsModal.vue')['default']
    MentalHealth: typeof import('./apps/OPD/components/reports/clinic/MentalHealth.vue')['default']
    Menu: typeof import('./components/Menu.vue')['default']
    MenuGrid: typeof import('./components/MenuGrid.vue')['default']
    MissedVaccinesModal: typeof import('./apps/Immunization/components/Modals/missedVaccinesModal.vue')['default']
    ModalContainer: typeof import('./components/ModalContainer.vue')['default']
    ModernLoader: typeof import('./apps/NCD/components/ModernLoader.vue')['default']
    ModulePicker: typeof import('./components/DashboardModal/ModulePicker.vue')['default']
    MOHReportHeader: typeof import('./apps/OPD/components/reports/moh/MOHReportHeader.vue')['default']
    MonthsPicker: typeof import('./apps/Immunization/components/Reports/MonthsPicker.vue')['default']
    MultiColumnView: typeof import('./components/MultiColumnView.vue')['default']
    NavigationMenu: typeof import('./apps/Immunization/components/Reports/NavigationMenu.vue')['default']
    NCDActivePatients: typeof import('./apps/NCD/components/NCDActivePatients.vue')['default']
    NCDActivePatientsTemplate: typeof import('./apps/NCD/components/NCDActivePatientsTemplate.vue')['default']
    NCDAppointments: typeof import('./apps/NCD/components/NCDAppointments.vue')['default']
    NCDDashboard: typeof import('./apps/NCD/components/NCDDashboard.vue')['default']
    NCDMedication: typeof import('./apps/NCD/components/ConsultationPlan/NCDMedication.vue')['default']
    NCDMedicationDispenstion: typeof import('./apps/NCD/components/ConsultationPlan/NCDMedicationDispenstion.vue')['default']
    NCDNumber: typeof import('./apps/NCD/components/Enrollment/NCDNumber.vue')['default']
    NextAppointment: typeof import('./apps/NCD/components/ConsultationPlan/NextAppointment.vue')['default']
    NextAppointMent: typeof import('./apps/Immunization/components/Modals/nextAppointMent.vue')['default']
    NonPharmacologicalIntervention: typeof import('./apps/OPD/components/ConsultationPlan/NonPharmacologicalIntervention.vue')['default']
    NonPharmalogicalTherapyAndOtherNotes: typeof import('./apps/NCD/components/ConsultationPlan/NonPharmalogicalTherapyAndOtherNotes.vue')['default']
    Note: typeof import('./apps/OPD/components/ConsultationPlan/Note.vue')['default']
    NotesGrid: typeof import('./components/PatientProfileGrid/NotesGrid.vue')['default']
    NotesModal: typeof import('./components/ProfileModal/NotesModal.vue')['default']
    OfflineDataSyncSettings: typeof import('./components/OfflineDataSyncSettings.vue')['default']
    OfflineDataSyncSettingsModal: typeof import('./components/OfflineDataSyncSettingsModal.vue')['default']
    OfflineMoreDetailsModal: typeof import('./components/Modal/OfflineMoreDetailsModal.vue')['default']
    OPDAllPatientsModal: typeof import('./components/DashboardModal/OPDAllPatientsModal.vue')['default']
    OPDDashboard: typeof import('./apps/OPD/components/OPDDashboard.vue')['default']
    OPDDiagnosis: typeof import('./apps/OPD/components/ConsultationPlan/OPDDiagnosis.vue')['default']
    OPDFooter: typeof import('./apps/OPD/components/OPDFooter.vue')['default']
    OPDMedications: typeof import('./apps/NCD/components/ConsultationPlan/OPDMedications.vue')['default']
    OPDOutcome: typeof import('./apps/OPD/components/ConsultationPlan/OPDOutcome.vue')['default']
    Opdpopover: typeof import('./components/Popovers/Opdpopover.vue')['default']
    OPDPrintingModal: typeof import('./apps/OPD/components/ConsultationPlan/Modals/OPDPrintingModal.vue')['default']
    OPDTreatmentPlan: typeof import('./apps/OPD/components/ConsultationPlan/OPDTreatmentPlan.vue')['default']
    OPDWaitingListModal: typeof import('./components/DashboardModal/OPDWaitingListModal.vue')['default']
    OthervitalsModal: typeof import('./apps/Immunization/components/OthervitalsModal.vue')['default']
    Outcome: typeof import('./apps/NCD/components/ConsultationPlan/Outcome.vue')['default']
    OutcomeGrid: typeof import('./components/PatientProfileGrid/OutcomeGrid.vue')['default']
    OutcomeModal: typeof import('./components/OutcomeModal.vue')['default']
    OverDueReport: typeof import('./apps/Immunization/components/Reports/OverDueReport.vue')['default']
    Pagination: typeof import('./components/Pagination.vue')['default']
    PastMedicalHistory: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment/PastMedicalHistory.vue')['default']
    PatientConfirmationCards: typeof import('./components/Cards/PatientConfirmationCards.vue')['default']
    PatientHistory: typeof import('./apps/NCD/components/Enrollment/PatientHistory.vue')['default']
    PatientHistoryHIV: typeof import('./apps/NCD/components/Enrollment/PatientHistoryHIV.vue')['default']
    PatientHistoryTB: typeof import('./apps/NCD/components/Enrollment/PatientHistoryTB.vue')['default']
    PatientHistoryTraditionalMedicine: typeof import('./apps/NCD/components/Enrollment/PatientHistoryTraditionalMedicine.vue')['default']
    PatientProfile: typeof import('./apps/Immunization/components/PatientProfile.vue')['default']
    PatientType: typeof import('./apps/NCD/components/Enrollment/PatientType.vue')['default']
    PersonalInformation: typeof import('./components/Registration/PersonalInformation.vue')['default']
    PersonalInformationModal: typeof import('./apps/Immunization/components/Modals/personalInformationModal.vue')['default']
    PersonCardComponent: typeof import('./apps/Immunization/components/Modals/PersonCardComponent.vue')['default']
    PersonMatchView: typeof import('./components/FormElements/PersonMatchView.vue')['default']
    PersonSearchView: typeof import('./components/FormElements/PersonSearchView.vue')['default']
    Pharmacy: typeof import('./apps/OPD/components/Pharmacy.vue')['default']
    PharmacyToolbarSearch: typeof import('./apps/OPD/components/pharmacyToolbarSearch.vue')['default']
    PhysicalExamination: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment/PhysicalExamination.vue')['default']
    PickerSelector: typeof import('./components/Selectors/PickerSelector.vue')['default']
    PregnancyBreastfeeding: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment/PregnancyBreastfeeding.vue')['default']
    Prescription: typeof import('./apps/ART/components/Prescription.vue')['default']
    PresentingComplaints: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment/PresentingComplaints.vue')['default']
    PreviousAllergies: typeof import('./apps/NCD/components/ConsultationPlan/PreviousAllergies.vue')['default']
    PreviousComplaints: typeof import('./apps/OPD/components/ConsultationPlan/previousVisits/previousComplaints.vue')['default']
    PreviousDiagnosis: typeof import('./apps/NCD/components/ConsultationPlan/previousVisits/previousDiagnosis.vue')['default']
    PreviousNCDDiagnosis: typeof import('./apps/NCD/components/PreviousNCDDiagnosis.vue')['default']
    PreviousNotes: typeof import('./apps/NCD/components/ConsultationPlan/PreviousNotes.vue')['default']
    PreviousVitals: typeof import('./components/Graphs/previousVitals.vue')['default']
    PrivacyPolicy: typeof import('./components/PrivacyPolicy.vue')['default']
    ProgramGrid: typeof import('./components/ProgramGrid.vue')['default']
    QRCodeReader: typeof import('./components/QRCodeReader.vue')['default']
    RadioActionSheet: typeof import('./components/DataViews/actionsheet/RadioActionSheet.vue')['default']
    RadiologyInvestigation: typeof import('./apps/OPD/components/ConsultationPlan/Radiology/RadiologyInvestigation.vue')['default']
    RawTable: typeof import('./apps/Immunization/components/Reports/rawTable.vue')['default']
    Reception: typeof import('./apps/ART/components/Reception/Reception.vue')['default']
    Referrals: typeof import('./apps/NCD/components/Dashboard/Referrals.vue')['default']
    ReferredOutCome: typeof import('./apps/OPD/components/ConsultationPlan/ReferredOutCome.vue')['default']
    RegimenCard: typeof import('./components/DataViews/RegimenCard.vue')['default']
    RegistrationSummaryModal: typeof import('./apps/OPD/components/RegistrationSummaryModal.vue')['default']
    RelationsSelection: typeof import('./components/FormElements/RelationsSelection.vue')['default']
    ReportDataTable: typeof import('./apps/ART/components/SimpleTable/ReportDataTable.vue')['default']
    ReportFilter: typeof import('./components/ReportFilter.vue')['default']
    ReportMixin: typeof import('./apps/OPD/components/reports/ReportMixin.vue')['default']
    ReportTable: typeof import('./components/ReportTable.vue')['default']
    RiskAssessment: typeof import('./apps/NCD/components/ConsultationPlan/RiskAssessment.vue')['default']
    RoleSelectionModal: typeof import('./apps/OPD/components/RoleSelectionModal.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SaveProgressModal: typeof import('./components/SaveProgressModal.vue')['default']
    ScanRegistration: typeof import('./components/Registration/ScanRegistration.vue')['default']
    SearchField: typeof import('./components/SearchField.vue')['default']
    SelectAppointMentDate: typeof import('./apps/Immunization/components/Modals/SelectAppointMentDate.vue')['default']
    SelectFacility: typeof import('./apps/OPD/components/SelectFacility.vue')['default']
    SelectionPopover: typeof import('./components/SelectionPopover.vue')['default']
    SelectMixin: typeof import('./components/FormElements/SelectMixin.vue')['default']
    SendToLabConfirmationModal: typeof import('./components/Lab/SendToLabConfirmationModal.vue')['default']
    SessionDate: typeof import('./components/Modal/SessionDate.vue')['default']
    SideEffectModal: typeof import('./apps/ART/components/consultation/SideEffectModal.vue')['default']
    SideEffectsCheckboxes: typeof import('./apps/ART/components/consultation/SideEffectsCheckboxes.vue')['default']
    SideMenuItem: typeof import('./components/SideMenuItem.vue')['default']
    SideMenuItemLabel: typeof import('./components/SideMenuItemLabel.vue')['default']
    SingleSelection: typeof import('./components/SingleSelection.vue')['default']
    SIngleTouchField: typeof import('./components/Forms/SIngleTouchField.vue')['default']
    SmsConfirmation: typeof import('./apps/Immunization/components/Modals/smsConfirmation.vue')['default']
    SocialHistory: typeof import('./components/Registration/SocialHistory.vue')['default']
    SselectionList: typeof import('./components/SselectionList.vue')['default']
    Staging: typeof import('./apps/ART/components/Staging.vue')['default']
    Stepper: typeof import('./components/Stepper.vue')['default']
    StockManagementModal: typeof import('./components/Modal/StockManagementModal.vue')['default']
    SubstanceDiagnosis: typeof import('./apps/OPD/components/Enrollment/SubstanceDiagnosis.vue')['default']
    SyncingStatusModal: typeof import('./components/Modal/SyncingStatusModal.vue')['default']
    TableActionSheet: typeof import('./components/DataViews/actionsheet/TableActionSheet.vue')['default']
    TaskCard: typeof import('./components/DataViews/TaskCard.vue')['default']
    TB: typeof import('./apps/ART/components/ARTRegistration/TB.vue')['default']
    TBSymptomsCheckboxes: typeof import('./apps/ART/components/consultation/TBSymptomsCheckboxes.vue')['default']
    TextSkeleton: typeof import('./components/TextSkeleton.vue')['default']
    TimePicker: typeof import('./components/TimePicker.vue')['default']
    Toolbar: typeof import('./components/Toolbar.vue')['default']
    ToolbarMediumCard: typeof import('./components/Cards/ToolbarMediumCard.vue')['default']
    ToolbarSearch: typeof import('./components/ToolbarSearch.vue')['default']
    TouchScreenForm: typeof import('./components/Forms/TouchScreenForm.vue')['default']
    TreatmentHistory: typeof import('./components/DashboardSegments/TreatmentHistory.vue')['default']
    TreatmentPlan: typeof import('./apps/NCD/components/ConsultationPlan/TreatmentPlan.vue')['default']
    TruncateText: typeof import('./components/TruncateText.vue')['default']
    UpcomingFeature: typeof import('./components/UpcomingFeature.vue')['default']
    UpdateNCDNumberModal: typeof import('./components/UpdateNCDNumberModal.vue')['default']
    UpdateTA: typeof import('./components/Registration/Modal/UpdateTA.vue')['default']
    UpdateVillage: typeof import('./components/Registration/Modal/UpdateVillage.vue')['default']
    UserCardList: typeof import('./components/userCardList.vue')['default']
    UsersTemplate: typeof import('./components/usersTemplate.vue')['default']
    VaccinationHistoryModal: typeof import('./apps/Immunization/components/Modals/vaccinationHistoryModal.vue')['default']
    ViewImmunizationSessionModal: typeof import('./components/Modal/ViewImmunizationSessionModal.vue')['default']
    ViewPort: typeof import('./components/DataViews/ViewPort.vue')['default']
    ViewToggleComponent: typeof import('./apps/NCD/components/ViewToggleComponent.vue')['default']
    VisitHistory: typeof import('./apps/OPD/components/ConsultationPlan/ClinicalAssessment/VisitHistory.vue')['default']
    VisitsHistory: typeof import('./components/DashboardSegments/VisitsHistory.vue')['default']
    Vitals: typeof import('./apps/ART/components/Vitals.vue')['default']
    VitalsGrid: typeof import('./components/PatientProfileGrid/VitalsGrid.vue')['default']
    VitalSigns: typeof import('./apps/NCD/components/ConsultationPlan/VitalSigns.vue')['default']
    VitalsMeasurementsSummary: typeof import('./components/DashboardSegments/VitalsMeasurementsSummary.vue')['default']
    VitalsModal: typeof import('./components/ProfileModal/VitalsModal.vue')['default']
    VoidAdminstredVaccine: typeof import('./apps/Immunization/components/Modals/voidAdminstredVaccine.vue')['default']
    VoidReason: typeof import('./apps/Immunization/components/Modals/voidReason.vue')['default']
    WeightAndHeight: typeof import('./apps/Immunization/components/Modals/weightAndHeight.vue')['default']
    WeightHeightChart: typeof import('./apps/Immunization/components/Graphs/WeightHeightChart.vue')['default']
    WorkstationModal: typeof import('./components/Modal/WorkstationModal.vue')['default']
    ZebraPrinterImage: typeof import('./components/ZebraPrinterImage.vue')['default']
  }
}
